# Supabase Setup Guide for DodoBooker

This guide will help you set up Supabase database for your DodoBooker Flutter app.

## Prerequisites

1. A Supabase account (sign up at https://supabase.com)
2. Flutter development environment set up
3. Your DodoBooker project ready

## Step 1: Create a New Supabase Project

1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: DodoBooker
   - **Database Password**: Choose a strong password (save it securely)
   - **Region**: Choose the closest region to your users
5. Click "Create new project"
6. Wait for the project to be set up (usually takes 1-2 minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **anon/public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

## Step 3: Configure Your Flutter App

1. Open `lib/config/supabase_config.dart`
2. Replace the placeholder values with your actual credentials:

```dart
class SupabaseConfig {
  static const String supabaseUrl = 'https://your-project-id.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key-here';
  // ... rest of the file
}
```

## Step 4: Create Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the entire content from `database_schema.sql` file
4. Paste it into the SQL editor
5. Click "Run" to execute the schema creation
6. You should see "Success. No rows returned" message

## Step 5: Insert Sample Data

1. In the SQL Editor, create another new query
2. Copy the entire content from `sample_data.sql` file
3. Paste it into the SQL editor
4. Click "Run" to insert sample data
5. You should see success messages for each insert operation

## Step 6: Verify Database Setup

1. Go to **Table Editor** in your Supabase dashboard
2. You should see the following tables:
   - `categories` (with 8 sample categories)
   - `subcategories` (with sample subcategories)
   - `products` (with sample products)
   - `user_profiles`
   - `user_addresses`
   - `cart_items`
   - `bookings`
   - `booking_items`
   - `coupons` (with 4 sample coupons)

## Step 7: Enable Authentication

1. Go to **Authentication** > **Settings**
2. Enable the authentication providers you want:
   - **Phone**: Enable for OTP-based authentication
   - **Email**: Optional, if you want email authentication
3. Configure phone authentication:
   - Go to **Authentication** > **Settings** > **Phone Auth**
   - Enable phone authentication
   - Configure your SMS provider (Twilio recommended)

## Step 8: Test Your Setup

1. Run your Flutter app: `flutter run`
2. The app should start without errors
3. Check the console for Supabase initialization messages
4. Try navigating to different screens to see if data loads

## Step 9: Update Your App to Use Supabase

To switch from mock APIs to Supabase, you can:

### Option 1: Use SupabaseProvider (Recommended)
```dart
// In your widgets, use SupabaseProvider instead of other providers
Consumer<SupabaseProvider>(
  builder: (context, supabaseProvider, child) {
    if (!supabaseProvider.isConfigured) {
      return Text('Please configure Supabase credentials');
    }
    
    if (supabaseProvider.isLoading) {
      return CircularProgressIndicator();
    }
    
    return ListView.builder(
      itemCount: supabaseProvider.categories.length,
      itemBuilder: (context, index) {
        final category = supabaseProvider.categories[index];
        return ListTile(title: Text(category['name']));
      },
    );
  },
)
```

### Option 2: Update Existing Providers
Modify your existing providers to use SupabaseService instead of mock APIs.

## Troubleshooting

### Common Issues:

1. **"Supabase is not configured" error**
   - Make sure you've updated the credentials in `supabase_config.dart`
   - Ensure the URL and key are correct (no extra spaces or quotes)

2. **Database connection errors**
   - Check your internet connection
   - Verify your Supabase project is active
   - Check if your API key has the correct permissions

3. **Authentication not working**
   - Ensure phone authentication is enabled in Supabase dashboard
   - Check if you've configured SMS provider correctly
   - For testing, you can use Supabase's built-in test phone numbers

4. **Data not loading**
   - Check if the database schema was created correctly
   - Verify sample data was inserted
   - Check browser console for error messages

### Getting Help:

1. Check Supabase documentation: https://supabase.com/docs
2. Join Supabase Discord: https://discord.supabase.com
3. Check the Flutter Supabase package docs: https://pub.dev/packages/supabase_flutter

## Next Steps

1. **Customize the schema**: Add more fields or tables as needed for your app
2. **Set up real-time subscriptions**: Use Supabase real-time features for live updates
3. **Add file storage**: Use Supabase Storage for images and files
4. **Implement advanced auth**: Add social logins, password reset, etc.
5. **Set up Edge Functions**: For complex business logic
6. **Configure production settings**: Set up proper RLS policies, backups, etc.

## Security Notes

- Never commit your Supabase credentials to version control
- Use environment variables for production
- Review and customize Row Level Security (RLS) policies
- Regularly update your dependencies
- Monitor your Supabase usage and set up alerts

## Production Checklist

Before going live:
- [ ] Update RLS policies for your specific needs
- [ ] Set up proper backup strategy
- [ ] Configure monitoring and alerts
- [ ] Set up staging environment
- [ ] Test all authentication flows
- [ ] Verify all CRUD operations work correctly
- [ ] Set up proper error handling
- [ ] Configure rate limiting if needed
