import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

/// HTTP-based API service that connects to the Node.js mock server
/// This service fetches data from your actual mock_api_server.js
class HttpApiService {
  // Singleton pattern
  static final HttpApiService _instance = HttpApiService._internal();
  factory HttpApiService() => _instance;
  HttpApiService._internal();

  // Base URL for the Node.js server
  static const String _baseUrl = 'http://localhost:3000';
  
  // Current user ID (set after authentication)
  String? _currentUserId;

  // Debug logging
  void _log(String message) {
    if (kDebugMode) {
      print('HttpApiService: $message');
    }
  }

  // Helper method to create success response
  Map<String, dynamic> _createSuccessResponse(dynamic data) {
    return {
      'status': 'success',
      'data': data,
      'message': 'Success',
    };
  }

  // Helper method to create error response
  Map<String, dynamic> _createErrorResponse(String message) {
    return {
      'status': 'error',
      'data': null,
      'message': message,
    };
  }

  // Set current user ID
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
    _log('Current user ID set to: $userId');
  }

  // Get current user ID
  String? getCurrentUserId() {
    return _currentUserId;
  }

  // Generic HTTP GET method
  Future<Map<String, dynamic>> _get(String endpoint) async {
    try {
      _log('GET request to: $_baseUrl$endpoint');
      
      final response = await http.get(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      _log('Response status: ${response.statusCode}');
      _log('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data;
      } else {
        return _createErrorResponse('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      _log('Error in GET request: $e');
      return _createErrorResponse('Network error: $e');
    }
  }

  // Generic HTTP POST method
  Future<Map<String, dynamic>> _post(String endpoint, Map<String, dynamic> body) async {
    try {
      _log('POST request to: $_baseUrl$endpoint');
      _log('Request body: $body');
      
      final response = await http.post(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(body),
      ).timeout(const Duration(seconds: 10));

      _log('Response status: ${response.statusCode}');
      _log('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data;
      } else {
        return _createErrorResponse('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      _log('Error in POST request: $e');
      return _createErrorResponse('Network error: $e');
    }
  }

  // Authentication methods
  Future<Map<String, dynamic>> sendOtp(String mobile) async {
    return await _post('/api/auth/customer/otp/send/', {
      'mobile': mobile,
      'user_type': 'CUSTOMER',
    });
  }

  Future<Map<String, dynamic>> verifyOtp(String mobile, String otp) async {
    final result = await _post('/api/auth/customer/otp/verify/', {
      'mobile': mobile,
      'otp': otp,
      'user_type': 'CUSTOMER',
    });
    
    // Set current user ID if verification successful
    if (result['status'] == 'success' && result['data'] != null) {
      final userData = result['data']['user'];
      if (userData != null && userData['id'] != null) {
        setCurrentUserId(userData['id']);
      }
    }
    
    return result;
  }

  Future<Map<String, dynamic>> logout() async {
    final result = await _post('/api/auth/token/cookie/logout/', {});
    
    // Clear current user ID on logout
    if (result['status'] == 'success') {
      _currentUserId = null;
      _log('User logged out, current user ID cleared');
    }
    
    return result;
  }

  // Category methods
  Future<Map<String, dynamic>> getCategories() async {
    return await _get('/api/categories/');
  }

  Future<Map<String, dynamic>> getSubcategories(String categoryId) async {
    return await _get('/api/categories/$categoryId/subcategories/');
  }

  Future<Map<String, dynamic>> getProducts(String categoryId, String subcategoryId) async {
    return await _get('/api/categories/$categoryId/subcategories/$subcategoryId/products/');
  }

  // Cart methods
  Future<Map<String, dynamic>> getCart() async {
    return await _get('/api/cart/');
  }

  Future<Map<String, dynamic>> addToCart(String productId, int quantity, double price) async {
    return await _post('/api/cart/add/', {
      'productId': productId,
      'quantity': quantity,
      'price': price,
    });
  }

  Future<Map<String, dynamic>> clearCart() async {
    return await http.delete(Uri.parse('$_baseUrl/api/cart/clear/')).then((response) {
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return _createErrorResponse('Failed to clear cart');
      }
    }).catchError((e) {
      return _createErrorResponse('Network error: $e');
    });
  }

  // Booking methods
  Future<Map<String, dynamic>> createBooking(Map<String, dynamic> bookingData) async {
    return await _post('/api/bookings/', bookingData);
  }

  Future<Map<String, dynamic>> getBookings() async {
    return await _get('/api/bookings/');
  }

  Future<Map<String, dynamic>> getBookingDetails(String bookingId) async {
    return await _get('/api/bookings/$bookingId/');
  }

  // Profile methods
  Future<Map<String, dynamic>> getProfile() async {
    return await _get('/api/profile/');
  }

  // Address methods
  Future<Map<String, dynamic>> getAddresses() async {
    return await _get('/api/addresses/');
  }

  // Coupon methods
  Future<Map<String, dynamic>> getCoupons() async {
    return await _get('/api/coupons/');
  }

  // Health check
  Future<Map<String, dynamic>> healthCheck() async {
    return await _get('/health');
  }

  // Test server connection
  Future<bool> testConnection() async {
    try {
      final result = await healthCheck();
      return result['status'] == 'success' || result.containsKey('status');
    } catch (e) {
      _log('Connection test failed: $e');
      return false;
    }
  }
}
