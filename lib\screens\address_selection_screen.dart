import 'package:flutter/material.dart';
import '../models/cart_item.dart';
import 'schedule_selection_screen.dart';
import 'map_location_screen.dart';

class AddressSelectionScreen extends StatefulWidget {
  final List<CartItem> cartItems;
  final double totalAmount;

  const AddressSelectionScreen({
    super.key,
    required this.cartItems,
    required this.totalAmount,
  });

  @override
  State<AddressSelectionScreen> createState() => _AddressSelectionScreenState();
}

class _AddressSelectionScreenState extends State<AddressSelectionScreen> {
  final List<Map<String, dynamic>> _addresses = [
    {
      'id': '1',
      'type': 'Home',
      'address': '123 Main Street, Apt 4B, New York, NY 10001',
      'isDefault': true,
      'isSelected': true,
    },
    {
      'id': '2',
      'type': 'Work',
      'address': '456 Business Ave, Suite 200, New York, NY 10002',
      'isDefault': false,
      'isSelected': false,
    },
  ];

  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _zipController = TextEditingController();
  bool _isAddingNewAddress = false;

  @override
  void dispose() {
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipController.dispose();
    super.dispose();
  }

  void _selectAddress(String id) {
    setState(() {
      for (var address in _addresses) {
        address['isSelected'] = address['id'] == id;
      }
    });
  }

  void _addNewAddress() {
    if (_addressController.text.isEmpty ||
        _cityController.text.isEmpty ||
        _stateController.text.isEmpty ||
        _zipController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all address fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final newAddress = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'type': 'Other',
      'address':
          '${_addressController.text}, ${_cityController.text}, ${_stateController.text} ${_zipController.text}',
      'isDefault': false,
      'isSelected': true,
    };

    setState(() {
      for (var address in _addresses) {
        address['isSelected'] = false;
      }
      _addresses.add(newAddress);
      _isAddingNewAddress = false;
      _addressController.clear();
      _cityController.clear();
      _stateController.clear();
      _zipController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: const Text(
          'Select Address',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Cart summary
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color(0xFFCFD8DC),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0F7FA),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.shopping_cart,
                    size: 30,
                    color: Color(0xFF26C6DA),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Items: ${widget.cartItems.length}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Total Quantity: ${widget.cartItems.fold(0, (sum, item) => sum + item.quantity)}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF7D8CA3),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Total: ₹${widget.totalAmount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF26C6DA),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Saved Addresses',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                        Row(
                          children: [
                            TextButton.icon(
                              onPressed: () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const MapLocationScreen(),
                                  ),
                                );

                                if (result != null) {
                                  final newAddress = {
                                    'id': DateTime.now()
                                        .millisecondsSinceEpoch
                                        .toString(),
                                    'type': 'Custom',
                                    'address': result['address'],
                                    'latitude': result['latitude'],
                                    'longitude': result['longitude'],
                                    'isDefault': false,
                                    'isSelected': true,
                                  };

                                  setState(() {
                                    for (var address in _addresses) {
                                      address['isSelected'] = false;
                                    }
                                    _addresses.add(newAddress);
                                  });
                                }
                              },
                              icon: const Icon(
                                Icons.map,
                                color: Colors.black,
                                size: 18,
                              ),
                              label: const Text(
                                'Map',
                                style: TextStyle(
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  _isAddingNewAddress = !_isAddingNewAddress;
                                });
                              },
                              icon: Icon(
                                _isAddingNewAddress ? Icons.close : Icons.add,
                                color: Colors.black,
                                size: 18,
                              ),
                              label: Text(
                                _isAddingNewAddress ? 'Cancel' : 'Add New',
                                style: const TextStyle(
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Address list or add new form
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: !_isAddingNewAddress
                        ? Column(
                            children: _addresses.map((address) {
                              final isSelected = address['isSelected'] as bool;
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 12.0),
                                child: Card(
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: InkWell(
                                    onTap: () => _selectAddress(address['id']),
                                    borderRadius: BorderRadius.circular(12),
                                    child: Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Radio(
                                            value: true,
                                            groupValue: isSelected,
                                            onChanged: (_) =>
                                                _selectAddress(address['id']),
                                            activeColor: Colors.black,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Text(
                                                      address['type'],
                                                      style: const TextStyle(
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color:
                                                            Color(0xFF2D4059),
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    if (address['isDefault'])
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 8,
                                                          vertical: 2,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: const Color(
                                                              0xFFEEEEEE),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                        ),
                                                        child: const Text(
                                                          'Default',
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  address['address'],
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Color(0xFF7D8CA3),
                                                  ),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          )
                        : Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Add New Address',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF2D4059),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  TextField(
                                    controller: _addressController,
                                    decoration: const InputDecoration(
                                      hintText: 'Street Address',
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 12),
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  TextField(
                                    controller: _cityController,
                                    decoration: const InputDecoration(
                                      hintText: 'City',
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 12),
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextField(
                                          controller: _stateController,
                                          decoration: const InputDecoration(
                                            hintText: 'State',
                                            border: OutlineInputBorder(),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 12),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: TextField(
                                          controller: _zipController,
                                          decoration: const InputDecoration(
                                            hintText: 'ZIP Code',
                                            border: OutlineInputBorder(),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 12),
                                          ),
                                          keyboardType: TextInputType.number,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      onPressed: _addNewAddress,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.black,
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 12),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      child: const Text(
                                        'Save Address',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color(0xFFCFD8DC),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            // Find selected address
            final selectedAddress = _addresses.firstWhere(
              (address) => address['isSelected'] == true,
              orElse: () => _addresses.first,
            );

            // Navigate to schedule selection screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ScheduleSelectionScreen(
                  cartItems: widget.cartItems,
                  totalAmount: widget.totalAmount,
                  address: selectedAddress,
                ),
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'Confirm Location',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
