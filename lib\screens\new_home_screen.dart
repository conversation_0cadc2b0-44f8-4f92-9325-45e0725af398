import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../widgets/cart_icon_with_badge.dart';
import '../models/hierarchical_category.dart';
import '../providers/category_provider.dart';
import 'map_location_screen.dart';
import 'category_navigation_screen.dart';
import 'search_results_screen.dart';

class NewHomeScreen extends StatefulWidget {
  const NewHomeScreen({super.key});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> {
  String _currentAddress = 'Fetching location...';
  bool _isLoadingLocation = true;
  final TextEditingController _searchController = TextEditingController();

  // Categories will be loaded from the CategoryProvider

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    final categoryProvider =
        Provider.of<CategoryProvider>(context, listen: false);
    await categoryProvider.loadCategories();
  }

  Future<void> _refreshCategories() async {
    final categoryProvider =
        Provider.of<CategoryProvider>(context, listen: false);
    await categoryProvider.refreshCategories();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _currentAddress = 'Location permissions denied';
            _isLoadingLocation = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _currentAddress = 'Location permissions permanently denied';
          _isLoadingLocation = false;
        });
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        setState(() {
          _currentAddress = _formatAddress(place);
          _isLoadingLocation = false;
        });
      } else {
        setState(() {
          _currentAddress = 'Unknown location';
          _isLoadingLocation = false;
        });
      }
    } catch (e) {
      setState(() {
        _currentAddress = 'Error getting location';
        _isLoadingLocation = false;
      });
    }
  }

  String _formatAddress(Placemark place) {
    List<String> addressParts = [];

    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    if (addressParts.isEmpty &&
        place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }

    return addressParts.join(', ');
  }

  void _openLocationPicker() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MapLocationScreen(),
      ),
    );

    if (result != null && result['address'] != null) {
      setState(() {
        _currentAddress = result['address'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: GestureDetector(
          onTap: _openLocationPicker,
          child: Row(
            children: [
              const Icon(
                Icons.location_on,
                color: Color(0xFF2D4059),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _currentAddress,
                  style: const TextStyle(
                    color: Color(0xFF2D4059),
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (_isLoadingLocation)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF2D4059)),
                  ),
                ),
            ],
          ),
        ),
        actions: const [
          CartIconWithBadge(),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search bar
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: GestureDetector(
                  onTap: () {
                    // Navigate to search screen when clicked
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SearchResultsScreen(
                          initialQuery: '',
                        ),
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Row(
                      children: [
                        const Icon(Icons.search, color: Colors.grey),
                        const SizedBox(width: 8),
                        Expanded(
                          child: AbsorbPointer(
                            // Absorb pointer events to make the TextField non-interactive
                            // This ensures the GestureDetector handles the tap
                            child: TextField(
                              controller: _searchController,
                              decoration: const InputDecoration(
                                hintText: 'Find a service',
                                border: InputBorder.none,
                                hintStyle: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 16,
                                ),
                              ),
                              onSubmitted: (query) {
                                if (query.isNotEmpty) {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => SearchResultsScreen(
                                        initialQuery: query.trim(),
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Special Offers Section
              _buildSpecialOffersSection(),

              // Services Section
              _buildServicesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpecialOffersSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Special Offers',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to all offers page
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.purple,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 180,
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.only(left: 16),
              children: [
                _buildSpecialOfferCard(),
                const SizedBox(width: 16),
                _buildSpecialOfferCard(discount: 20),
                const SizedBox(width: 16),
                _buildSpecialOfferCard(discount: 15),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialOfferCard({int discount = 30}) {
    return Container(
      width: 300,
      decoration: BoxDecoration(
        color: Colors.purple,
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            right: 0,
            bottom: 0,
            top: 0,
            child: Image.network(
              'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
              fit: BoxFit.cover,
              width: 150,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.cleaning_services,
                  size: 80,
                  color: Colors.white54,
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$discount%',
                  style: const TextStyle(
                    fontSize: 40,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Text(
                  'Today\'s Special!',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Get discount for every order, only valid for today',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServicesSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Services',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to all services page
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.purple,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          _buildServiceCategoriesGrid(),
        ],
      ),
    );
  }

  Widget _buildServiceCategoriesGrid() {
    // Get categories from the provider
    final categoryProvider = Provider.of<CategoryProvider>(context);
    final categories = categoryProvider.categories;

    // Show loading indicator if categories are still loading
    if (categoryProvider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // Show error message if there was an error loading categories
    if (categoryProvider.error != null) {
      return Center(
        child: Text(
          'Error loading categories: ${categoryProvider.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    // If no categories are available, show a message with refresh option
    if (categories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('No categories available'),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _refreshCategories,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh Categories'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4ECDC4),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    // Create a list of category widgets
    final displayCategories = categories.map((category) {
      return _buildServiceCategoryFromModel(category);
    }).toList();

    // Show all categories without limitation
    return GridView.count(
      crossAxisCount: 4,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      mainAxisSpacing: 20,
      crossAxisSpacing: 16,
      children: displayCategories,
    );
  }

  Widget _buildServiceCategoryFromModel(HierarchicalCategory category) {
    return GestureDetector(
      onTap: () {
        _navigateToCategory(context, category);
      },
      child: Column(
        children: [
          // Circular icon container
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: category.color.withAlpha(50),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                category.icon,
                size: 28,
                color: category.color,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Category name
          Text(
            category.name,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _navigateToCategory(
      BuildContext context, HierarchicalCategory category) {
    if (category.hasSubcategories) {
      // Navigate to subcategories
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryNavigationScreen(
            parentCategory: category,
            categories: category.subcategories,
            title: '${category.name} Categories',
          ),
        ),
      );
    } else {
      // Navigate to products/services
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryNavigationScreen(
            parentCategory: category,
            categories: const [],
            title: '${category.name} Services',
          ),
        ),
      );
    }
  }
}
