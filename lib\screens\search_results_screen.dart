import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hierarchical_category.dart';
import '../models/cleaning_product.dart';
import '../models/service.dart';
import '../models/service_category.dart';
import '../models/service_adapter.dart';
import '../providers/product_provider.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/app_navigation_bar.dart';
import '../widgets/cart_icon_with_badge.dart';
import 'category_navigation_screen.dart';
import 'cleaning_product_list_screen.dart';
import 'cleaning_product_detail_screen.dart';

class SearchResultsScreen extends StatefulWidget {
  final String initialQuery;

  const SearchResultsScreen({
    super.key,
    required this.initialQuery,
  });

  @override
  State<SearchResultsScreen> createState() => _SearchResultsScreenState();
}

class _SearchResultsScreenState extends State<SearchResultsScreen> {
  late TextEditingController _searchController;
  List<HierarchicalCategory> _mainCategories = [];
  List<HierarchicalCategory> _filteredCategories = [];
  List<HierarchicalCategory> _filteredSubcategories = [];
  List<CleaningProduct> _filteredProducts = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);
    _loadCategoriesAndSearch();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCategoriesAndSearch() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get all categories
      final mainCategories = CategoryData.getMainCategories();

      // Search for categories
      _searchCategories(mainCategories, _searchController.text);

      // Search for products
      await _searchProducts(_searchController.text);

      setState(() {
        _mainCategories = mainCategories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'An error occurred: $e';
        _isLoading = false;
      });
    }
  }

  void _searchCategories(List<HierarchicalCategory> categories, String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredCategories = [];
        _filteredSubcategories = [];
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();

    // Filter main categories
    final filteredMainCategories = categories
        .where(
            (category) => category.name.toLowerCase().contains(lowercaseQuery))
        .toList();

    // Filter subcategories
    final filteredSubcategories = <HierarchicalCategory>[];

    for (final mainCategory in categories) {
      for (final subcategory in mainCategory.subcategories) {
        if (subcategory.name.toLowerCase().contains(lowercaseQuery)) {
          filteredSubcategories.add(subcategory);
        }
      }
    }

    setState(() {
      _filteredCategories = filteredMainCategories;
      _filteredSubcategories = filteredSubcategories;
    });
  }

  Future<void> _searchProducts(String query) async {
    if (query.isEmpty) {
      setState(() {
        _filteredProducts = [];
      });
      return;
    }

    try {
      // Normalize the query
      final normalizedQuery = query.trim();

      // Special handling for numeric queries (like "1" for "1 BHK")
      String searchQuery = normalizedQuery;
      bool isNumericQuery = RegExp(r'^\d+$').hasMatch(normalizedQuery);

      // For numeric queries, also try searching for "X BHK"
      if (isNumericQuery) {
        searchQuery = "$normalizedQuery bhk";
        print(
            "Converting numeric query '$normalizedQuery' to BHK query: '$searchQuery'");
      }

      final productProvider =
          Provider.of<ProductProvider>(context, listen: false);
      final products = await productProvider.searchProducts(searchQuery);

      if (mounted) {
        setState(() {
          _filteredProducts = products;
        });

        print("Search results count in screen: ${products.length}");
        for (var product in products) {
          print("Product in screen: ${product.name}");
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Error searching products: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Search',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: const [
          CartIconWithBadge(),
        ],
      ),
      bottomNavigationBar: AppNavigationBar(
        currentSection: NavigationSection.home,
        mainContext: context,
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SearchBarWidget(
              controller: _searchController,
              onChanged: (query) {
                _searchCategories(_mainCategories, query);
                _searchProducts(query);
              },
              onSubmitted: (query) {
                _searchCategories(_mainCategories, query);
                _searchProducts(query);
              },
              hintText: 'Search for services...',
              autofocus: true,
            ),
          ),

          // Results
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchController.text.isEmpty) {
      // Show popular categories and services when search is empty
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Popular Categories section
              const Text(
                'Popular Categories',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount:
                      _mainCategories.length > 5 ? 5 : _mainCategories.length,
                  itemBuilder: (context, index) {
                    return _buildPopularCategoryItem(_mainCategories[index]);
                  },
                ),
              ),

              const SizedBox(height: 24),

              // Popular Services section
              const Text(
                'Popular Services',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),

              // Get some popular services from different categories
              FutureBuilder<List<CleaningProduct>>(
                future: _getPopularServices(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        'Error loading popular services',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    );
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return Center(
                      child: Text(
                        'No popular services found',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    );
                  } else {
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: snapshot.data!.length,
                      itemBuilder: (context, index) {
                        return _buildProductItem(snapshot.data![index]);
                      },
                    );
                  }
                },
              ),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCategoriesAndSearch,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredCategories.isEmpty &&
        _filteredSubcategories.isEmpty &&
        _filteredProducts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found for "${_searchController.text}"',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main categories section
            if (_filteredCategories.isNotEmpty) ...[
              const Text(
                'Categories',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _filteredCategories.length,
                itemBuilder: (context, index) {
                  return _buildCategoryItem(_filteredCategories[index]);
                },
              ),
              const SizedBox(height: 24),
            ],

            // Subcategories section
            if (_filteredSubcategories.isNotEmpty) ...[
              const Text(
                'Subcategories',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _filteredSubcategories.length,
                itemBuilder: (context, index) {
                  return _buildCategoryItem(_filteredSubcategories[index]);
                },
              ),
              const SizedBox(height: 24),
            ],

            // Products section
            if (_filteredProducts.isNotEmpty) ...[
              const Text(
                'Services',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _filteredProducts.length,
                itemBuilder: (context, index) {
                  return _buildProductItem(_filteredProducts[index]);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(HierarchicalCategory category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToCategory(category),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Category image or icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: category.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: category.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          category.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              category.icon,
                              color: category.color,
                              size: 30,
                            );
                          },
                        ),
                      )
                    : Icon(
                        category.icon,
                        color: category.color,
                        size: 30,
                      ),
              ),
              const SizedBox(width: 16),
              // Category details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      category.hasSubcategories
                          ? '${category.subcategories.length} subcategories'
                          : 'View services',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              // Arrow icon
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCategory(HierarchicalCategory category) {
    if (category.hasSubcategories) {
      // Navigate to subcategories
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryNavigationScreen(
            parentCategory: category,
            categories: category.subcategories,
            title: '${category.name} Categories',
          ),
        ),
      );
    } else {
      // Navigate to products/services
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CleaningProductListScreen(
            category: category,
          ),
        ),
      );
    }
  }

  Widget _buildProductItem(CleaningProduct product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToProduct(product),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Product image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    product.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.image_not_supported,
                        color: Colors.grey.shade400,
                        size: 30,
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Product details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.description,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '₹${product.price.toInt()}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              // Arrow icon
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToProduct(CleaningProduct product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CleaningProductDetailScreen(
          product: product,
        ),
      ),
    );
  }

  // Method to build a popular category item
  Widget _buildPopularCategoryItem(HierarchicalCategory category) {
    return GestureDetector(
      onTap: () => _navigateToCategory(category),
      child: Container(
        width: 100,
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            // Category icon or image
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                color: category.color.withAlpha(50),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: category.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(35),
                        child: Image.network(
                          category.imageUrl!,
                          width: 70,
                          height: 70,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              category.icon,
                              size: 30,
                              color: category.color,
                            );
                          },
                        ),
                      )
                    : Icon(
                        category.icon,
                        size: 30,
                        color: category.color,
                      ),
              ),
            ),
            const SizedBox(height: 8),
            // Category name
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Method to get popular services
  Future<List<CleaningProduct>> _getPopularServices() async {
    final productProvider =
        Provider.of<ProductProvider>(context, listen: false);

    // Get a few products from different categories
    final List<CleaningProduct> popularServices = [];

    try {
      // Get one product from each of these categories
      final categories = [
        'cleaning', // Cleaning category
        'plumbing', // Plumbing category
        'electrical', // Electrical category
      ];

      for (final categoryId in categories) {
        final products =
            await productProvider.fetchProductsByCategoryId(categoryId);
        if (products.isNotEmpty) {
          // Add the first product from each category
          popularServices.add(products.first);
        }
      }

      return popularServices;
    } catch (e) {
      print('Error getting popular services: $e');
      return [];
    }
  }
}
