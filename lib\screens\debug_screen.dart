import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/real_time_mock_api_service.dart';
import '../services/mock_api_service.dart';
import '../providers/http_api_provider.dart';
import '../providers/category_provider.dart';
import 'supabase_debug_screen.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  final RealTimeMockApiService _apiService = RealTimeMockApiService();
  final MockApiService _mockApiService = MockApiService();
  bool _isLoading = false;
  String _lastRefreshResult = '';

  // Method to synchronize categories between both API services
  Future<void> _synchronizeCategories() async {
    try {
      // Get categories from RealTimeMockApiService
      final result = await _apiService.getCategories();
      if (result['status'] == 'success') {
        final categories = List<Map<String, dynamic>>.from(result['data']);

        // Update MockApiService with the same categories
        _mockApiService.updateCategories(categories);

        debugPrint(
            'Debug: Synchronized ${categories.length} categories between services');
      }
    } catch (e) {
      debugPrint('Debug: Error synchronizing categories: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug & Data Refresh'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            const Text(
              'Data Management',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Use these tools to refresh data when you make changes to JSON files or need to reload fresh data.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 32),

            // Refresh All Data Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _refreshAllData,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.refresh),
              label: Text(_isLoading ? 'Refreshing...' : 'Refresh All Data'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Refresh Categories Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _refreshCategories,
              icon: const Icon(Icons.category),
              label: const Text('Refresh Categories Only'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Add Test Category Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _addTestCategory,
              icon: const Icon(Icons.add),
              label: const Text('Add Test Category'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Test HTTP API Connection Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testHttpApiConnection,
              icon: const Icon(Icons.cloud),
              label: const Text('Test Node.js Server Connection'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Load from HTTP API Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadFromHttpApi,
              icon: const Icon(Icons.download),
              label: const Text('Load Categories from Node.js Server'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Supabase Debug Button
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SupabaseDebugScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.storage),
              label: const Text('Supabase Debug & Test'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Result Display
            if (_lastRefreshResult.isNotEmpty) ...[
              const Text(
                'Last Refresh Result:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  _lastRefreshResult,
                  style: const TextStyle(
                    fontSize: 14,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],

            const Spacer(),

            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[700], size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Instructions',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• Use "Refresh All Data" to reload all categories, products, and other data\n'
                    '• Use "Refresh Categories Only" to reload just the categories\n'
                    '• After refreshing, go back to the home screen to see updated data\n'
                    '• This clears the in-memory cache and loads fresh data',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshAllData() async {
    setState(() {
      _isLoading = true;
      _lastRefreshResult = '';
    });

    // Get the provider before async operations
    final categoryProvider =
        Provider.of<CategoryProvider>(context, listen: false);

    try {
      final result = await _apiService.refreshAllData();
      setState(() {
        _lastRefreshResult = 'Success!\n\n'
            'Message: ${result['data']['message']}\n'
            'Timestamp: ${result['data']['timestamp']}\n'
            'Categories: ${result['data']['categoriesCount']}\n'
            'Products: ${result['data']['productsCount']}';
      });

      // Also refresh the CategoryProvider so home screen gets updated
      await categoryProvider.loadCategories();

      // Show success snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'All data refreshed successfully! Home screen will update.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _lastRefreshResult = 'Error: $e';
      });

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshCategories() async {
    setState(() {
      _isLoading = true;
      _lastRefreshResult = '';
    });

    // Get the provider before async operations
    final categoryProvider =
        Provider.of<CategoryProvider>(context, listen: false);

    try {
      final result = await _apiService.getCategories(forceRefresh: true);
      final categories = result['data'] as List;

      setState(() {
        _lastRefreshResult = 'Categories Refreshed!\n\n'
            'Total Categories: ${categories.length}\n'
            'Categories: ${categories.map((c) => c['name']).join(', ')}';
      });

      // Also refresh the CategoryProvider so home screen gets updated
      await categoryProvider.loadCategories();

      // Show success snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Categories refreshed successfully! Home screen will update.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _lastRefreshResult = 'Error: $e';
      });

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing categories: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addTestCategory() async {
    setState(() {
      _isLoading = true;
      _lastRefreshResult = '';
    });

    // Get the provider before async operations
    final categoryProvider =
        Provider.of<CategoryProvider>(context, listen: false);

    try {
      final result = await _apiService.addTestCategory();
      final data = result['data'] as Map<String, dynamic>;

      setState(() {
        _lastRefreshResult = 'Test Category Added!\n\n'
            'Message: ${data['message']}\n'
            'Category Name: ${data['category']['name']}\n'
            'Total Categories: ${data['totalCategories']}';
      });

      // Also refresh the CategoryProvider so home screen gets updated
      await categoryProvider.loadCategories();

      // Show success snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Test category added successfully! Home screen will update.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _lastRefreshResult = 'Error: $e';
      });

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding test category: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testHttpApiConnection() async {
    setState(() {
      _isLoading = true;
      _lastRefreshResult = '';
    });

    try {
      final httpProvider = Provider.of<HttpApiProvider>(context, listen: false);
      await httpProvider.retryConnection();

      setState(() {
        if (httpProvider.isConnected) {
          _lastRefreshResult = 'HTTP API Connection Test: SUCCESS!\n\n'
              'Node.js server is running at http://localhost:3000\n'
              'Connection established successfully\n'
              'Ready to load data from server';
        } else {
          _lastRefreshResult = 'HTTP API Connection Test: FAILED\n\n'
              'Error: ${httpProvider.error ?? "Unknown error"}\n'
              'Make sure your Node.js server is running:\n'
              '1. Open terminal in project root\n'
              '2. Run: node mock_api_server.js\n'
              '3. Server should start on port 3000';
        }
      });

      // Show result snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(httpProvider.isConnected
                ? 'Connected to Node.js server successfully!'
                : 'Failed to connect to Node.js server'),
            backgroundColor:
                httpProvider.isConnected ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _lastRefreshResult = 'HTTP API Connection Test: ERROR\n\n'
            'Exception: $e\n'
            'Make sure your Node.js server is running';
      });

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadFromHttpApi() async {
    setState(() {
      _isLoading = true;
      _lastRefreshResult = '';
    });

    try {
      final httpProvider = Provider.of<HttpApiProvider>(context, listen: false);

      // First test connection
      if (!httpProvider.isConnected) {
        await httpProvider.retryConnection();
      }

      if (!httpProvider.isConnected) {
        setState(() {
          _lastRefreshResult = 'Load from HTTP API: FAILED\n\n'
              'Cannot connect to Node.js server\n'
              'Error: ${httpProvider.error ?? "Connection failed"}\n'
              'Please test connection first';
        });
        return;
      }

      // Load categories from HTTP API
      await httpProvider.loadCategories();

      setState(() {
        if (httpProvider.error == null) {
          final categories = httpProvider.categories;
          _lastRefreshResult = 'Load from HTTP API: SUCCESS!\n\n'
              'Categories loaded from Node.js server\n'
              'Total Categories: ${categories.length}\n'
              'Categories: ${categories.map((c) => c['name']).join(', ')}\n\n'
              'Data source: HTTP API (mock_api_server.js)';
        } else {
          _lastRefreshResult = 'Load from HTTP API: FAILED\n\n'
              'Error: ${httpProvider.error}\n'
              'Check server logs for details';
        }
      });

      // Show result snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(httpProvider.error == null
                ? 'Categories loaded from Node.js server successfully!'
                : 'Failed to load data from Node.js server'),
            backgroundColor:
                httpProvider.error == null ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _lastRefreshResult = 'Load from HTTP API: ERROR\n\n'
            'Exception: $e\n'
            'Check if Node.js server is running and accessible';
      });

      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load from HTTP API: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
