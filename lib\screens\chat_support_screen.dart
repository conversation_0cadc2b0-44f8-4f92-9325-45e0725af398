import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';
import '../providers/real_time_api_provider.dart';

class ChatSupportScreen extends StatefulWidget {
  const ChatSupportScreen({super.key});

  @override
  State<ChatSupportScreen> createState() => _ChatSupportScreenState();
}

class _ChatSupportScreenState extends State<ChatSupportScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadChatMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadChatMessages() async {
    // Only load initial messages if the list is empty
    if (_messages.isNotEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiProvider =
          Provider.of<RealTimeApiProvider>(context, listen: false);
      final messages = await apiProvider.getChatMessages();

      print('ChatSupport: Loaded ${messages.length} messages');

      setState(() {
        _messages.clear();
        for (final msg in messages) {
          try {
            print('ChatSupport: Processing message: ${msg}');

            // Handle both message formats
            final messageText = msg['message'] ?? msg['content'] ?? '';
            final senderId = msg['senderId'] ?? msg['userId'] ?? '';
            final senderName = msg['senderName'] ?? 'Support Agent';
            final timestamp =
                msg['timestamp'] ?? DateTime.now().toIso8601String();

            // Determine if message is from user or support
            final isUser = senderId != 'support-agent-1' &&
                senderId != 'agent_1' &&
                !senderId.toString().contains('bot') &&
                !senderId.toString().contains('Bot') &&
                !senderId.toString().contains('agent');

            _messages.add(ChatMessage(
              text: messageText,
              isUser: isUser,
              timestamp: DateTime.tryParse(timestamp) ?? DateTime.now(),
              botName: isUser ? null : senderName,
            ));

            print(
                'ChatSupport: Added message - Text: "$messageText", IsUser: $isUser');
          } catch (e) {
            print('Error parsing message: $e');
            // Skip invalid messages
          }
        }
        _isLoading = false;
      });

      print('ChatSupport: Total messages in UI: ${_messages.length}');
      _scrollToBottom();
    } catch (e) {
      print('Error loading chat messages: $e');
      setState(() {
        _isLoading = false;
      });
      // Add a welcome message if no messages are loaded
      if (_messages.isEmpty) {
        _addBotMessage(
          'Hello! Welcome to DodoBooker support. How can I help you today?',
          botName: 'Support Agent',
        );
      }
    }
  }

  void _addBotMessage(String text, {String? botName}) {
    setState(() {
      _messages.add(
        ChatMessage(
          text: text,
          isUser: false,
          timestamp: DateTime.now(),
          botName: botName,
        ),
      );
    });
    _scrollToBottom();
  }

  Future<void> _loadNewBotResponse() async {
    try {
      final apiProvider =
          Provider.of<RealTimeApiProvider>(context, listen: false);
      final allMessages = await apiProvider.getChatMessages();

      // Find new bot messages that aren't already in our local list
      final lastMessageTime =
          _messages.isNotEmpty ? _messages.last.timestamp : DateTime(2000);

      // Get messages from API and find new ones
      for (final msg in allMessages) {
        try {
          // Handle both message formats
          final senderId = msg['senderId'] ?? msg['userId'] ?? '';
          final messageText = msg['message'] ?? msg['content'] ?? '';
          final senderName = msg['senderName'] ?? 'Support Agent';
          final timestamp =
              msg['timestamp'] ?? DateTime.now().toIso8601String();

          // Determine if message is from user or support
          final isUser = senderId != 'support-agent-1' &&
              senderId != 'agent_1' &&
              !senderId.toString().contains('bot') &&
              !senderId.toString().contains('Bot') &&
              !senderId.toString().contains('agent');

          final messageTime = DateTime.tryParse(timestamp) ?? DateTime.now();

          // Only add bot messages that are newer than our last message
          if (!isUser && messageTime.isAfter(lastMessageTime)) {
            setState(() {
              _messages.add(ChatMessage(
                text: messageText,
                isUser: false,
                timestamp: messageTime,
                botName: senderName,
              ));
            });
          }
        } catch (e) {
          print('Error parsing new message: $e');
        }
      }

      _scrollToBottom();
    } catch (e) {
      print('Error loading new bot response: $e');
      rethrow;
    }
  }

  Future<void> _handleSubmitted(String text) async {
    if (text.trim().isEmpty) return;

    _messageController.clear();
    setState(() {
      _messages.add(
        ChatMessage(
          text: text,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      _isTyping = true;
    });
    _scrollToBottom();

    try {
      // Send message to API
      final apiProvider =
          Provider.of<RealTimeApiProvider>(context, listen: false);
      await apiProvider.sendChatMessage(text);

      // Wait a bit for the bot response
      await Future.delayed(const Duration(seconds: 3));

      // Get only new messages instead of reloading all
      try {
        await _loadNewBotResponse();
      } catch (e) {
        print('Error loading bot response: $e');
        // If reload fails, add a generic bot response
        _addBotMessage(
          'Thank you for your message. I\'m processing your request...',
          botName: 'Support Agent',
        );
      }

      setState(() {
        _isTyping = false;
      });
    } catch (e) {
      print('Error sending message: $e');
      setState(() {
        _isTyping = false;
      });
      _addBotMessage(
        'Thank you for your message. Our team will get back to you shortly.',
        botName: 'Support Agent',
      );
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: const Text(
          'Chat Support',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16.0),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return _buildMessageItem(message);
              },
            ),
          ),
          if (_isTyping)
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                children: [
                  NeumorphicContainer(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        _buildTypingIndicator(),
                        const SizedBox(width: 8),
                        const Text(
                          'Support is typing...',
                          style: TextStyle(
                            color: Color(0xFF7D8CA3),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return SizedBox(
      width: 40,
      child: Row(
        children: List.generate(
          3,
          (index) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: _buildDot(index),
          ),
        ),
      ),
    );
  }

  Widget _buildDot(int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 300 + (index * 200)),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: Color.lerp(
              const Color(0xFFCFD8DC),
              const Color(0xFF2D4059),
              value,
            ),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }

  Widget _buildMessageItem(ChatMessage message) {
    final isUser = message.isUser;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatar(isUser),
          const SizedBox(width: 8),
          Flexible(
            child: NeumorphicContainer(
              color: isUser ? const Color(0xFF2D4059) : Colors.white,
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment:
                    isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  if (!isUser && message.botName != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        message.botName!,
                        style: const TextStyle(
                          color: Color(0xFF7D8CA3),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  Text(
                    message.text,
                    style: TextStyle(
                      color: isUser ? Colors.white : const Color(0xFF2D4059),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      color: isUser
                          ? Colors.white.withValues(alpha: 0.7)
                          : const Color(0xFF7D8CA3),
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isUser) _buildAvatar(isUser),
        ],
      ),
    );
  }

  Widget _buildAvatar(bool isUser) {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        color: isUser ? const Color(0xFF2D4059) : Colors.blue,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Icon(
          isUser ? Icons.person : Icons.support_agent,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0xFFCFD8DC),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: NeumorphicContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: 'Type a message...',
                  border: InputBorder.none,
                ),
                onSubmitted: _handleSubmitted,
              ),
            ),
          ),
          const SizedBox(width: 8),
          NeumorphicButton(
            width: 50,
            height: 50,
            borderRadius: 25,
            padding: EdgeInsets.zero,
            onPressed: () => _handleSubmitted(_messageController.text),
            child: const Icon(
              Icons.send,
              color: Colors.white,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final hour = timestamp.hour.toString().padLeft(2, '0');
    final minute = timestamp.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? botName;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.botName,
  });
}
