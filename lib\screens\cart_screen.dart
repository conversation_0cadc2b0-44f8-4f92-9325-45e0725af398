import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/cart_provider.dart';
import '../providers/coupon_provider.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';
import '../widgets/service_addons_widget.dart';
import '../widgets/animated_empty_cart.dart';
import '../widgets/coupon_modal.dart';
import 'address_selection_screen.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: const Text(
          'My Cart',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).maybePop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_outline, color: Colors.black),
            onPressed: () {
              _showClearCartDialog(context);
            },
          ),
        ],
      ),
      body: Consumer<CartProvider>(
        builder: (context, cart, child) {
          if (cart.items.isEmpty) {
            return _buildEmptyCart(context);
          }
          return _buildCartItems(context, cart);
        },
      ),
      bottomNavigationBar: Consumer<CartProvider>(
        builder: (context, cart, child) {
          if (cart.items.isEmpty) {
            return const SizedBox.shrink();
          }
          return _buildBottomBar(context, cart);
        },
      ),
    );
  }

  Widget _buildEmptyCart(BuildContext context) {
    return AnimatedEmptyCart(
      key: const ValueKey('empty_cart_animation'),
      onBrowseServices: () {
        // Use maybePop to safely navigate back
        Navigator.of(context).maybePop();
      },
    );
  }

  Widget _buildCartItems(BuildContext context, CartProvider cart) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: cart.items.length,
      itemBuilder: (context, index) {
        final item = cart.items[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: NeumorphicContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Service image
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: item.category.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        item.category.icon,
                        size: 40,
                        color: item.category.color,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Service details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.service.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2D4059),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${item.date.day} ${_getMonthName(item.date.month)} at ${item.time}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF7D8CA3),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Duration: ${item.service.durationMinutes} min',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF7D8CA3),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Remove button
                    IconButton(
                      icon: const Icon(Icons.close, color: Color(0xFF7D8CA3)),
                      onPressed: () {
                        cart.removeItem(item.id);
                      },
                    ),
                  ],
                ),
                const Divider(color: Color(0xFFEEEEEE)),
                // Quantity selector
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove,
                              color: Color(0xFF7D8CA3)),
                          onPressed: item.quantity > 1
                              ? () {
                                  cart.updateQuantity(
                                      item.id, item.quantity - 1);
                                }
                              : null,
                        ),
                        Text(
                          item.quantity.toString(),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.add,
                            color: item.category.color,
                          ),
                          onPressed: () {
                            cart.updateQuantity(item.id, item.quantity + 1);
                          },
                        ),
                      ],
                    ),
                    Text(
                      '₹${item.subtotal.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: item.category.color,
                      ),
                    ),
                  ],
                ),

                // Add-ons section
                ServiceAddonsWidget(item: item),

                // Show selected add-ons
                if (item.addons.isNotEmpty) ...[
                  const Divider(color: Color(0xFFEEEEEE)),
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Selected Add-ons:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...item.addons
                            .map((addon) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            addon.name,
                                            style: const TextStyle(
                                              fontSize: 13,
                                              color: Color(0xFF7D8CA3),
                                            ),
                                          ),
                                          Text(
                                            '₹${addon.price.toStringAsFixed(0)} × ${addon.quantity}',
                                            style: const TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF2D4059),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            children: [
                                              // Decrease button
                                              InkWell(
                                                onTap: addon.quantity > 1
                                                    ? () {
                                                        cart.updateAddonQuantity(
                                                            item.id,
                                                            addon.id,
                                                            addon.quantity - 1);
                                                      }
                                                    : null,
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(4),
                                                  decoration: BoxDecoration(
                                                    color: addon.quantity > 1
                                                        ? Colors.grey.shade200
                                                        : Colors.grey.shade100,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4),
                                                  ),
                                                  child: Icon(
                                                    Icons.remove,
                                                    size: 14,
                                                    color: addon.quantity > 1
                                                        ? const Color(
                                                            0xFF7D8CA3)
                                                        : Colors.grey.shade400,
                                                  ),
                                                ),
                                              ),

                                              // Quantity display
                                              Container(
                                                margin:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 2),
                                                decoration: BoxDecoration(
                                                  color: Colors.grey.shade100,
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                                child: Text(
                                                  addon.quantity.toString(),
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: Color(0xFF2D4059),
                                                  ),
                                                ),
                                              ),

                                              // Increase button
                                              InkWell(
                                                onTap: () {
                                                  cart.updateAddonQuantity(
                                                      item.id,
                                                      addon.id,
                                                      addon.quantity + 1);
                                                },
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(4),
                                                  decoration: BoxDecoration(
                                                    color: Colors.grey.shade200,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4),
                                                  ),
                                                  child: Icon(
                                                    Icons.add,
                                                    size: 14,
                                                    color: item.category.color,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),

                                          // Total price for this addon
                                          Text(
                                            '₹${(addon.price * addon.quantity).toStringAsFixed(0)}',
                                            style: TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold,
                                              color: item.category.color,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ))
                            .toList(),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomBar(BuildContext context, CartProvider cart) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0xFFCFD8DC),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Coupon section
          InkWell(
            onTap: () {
              _showCouponModal(context, cart);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.discount_outlined,
                        color: Colors.black,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        cart.appliedCoupon != null
                            ? '${cart.appliedCoupon!.code} Applied'
                            : 'Apply Coupon',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.black,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Subtotal',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF7D8CA3),
                ),
              ),
              Text(
                '₹${cart.subtotal.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2D4059),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Tax (5%)',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF7D8CA3),
                ),
              ),
              Text(
                '₹${cart.tax.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2D4059),
                ),
              ),
            ],
          ),
          if (cart.smallOrderFee > 0)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Small Order Fee',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7D8CA3),
                  ),
                ),
                Text(
                  '₹${cart.smallOrderFee.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2D4059),
                  ),
                ),
              ],
            ),
          if (cart.couponDiscount > 0)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'Coupon Discount',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '(${cart.appliedCoupon!.code})',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 4),
                    InkWell(
                      onTap: () {
                        cart.removeCoupon();
                      },
                      child: const Icon(
                        Icons.close,
                        size: 14,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                Text(
                  '-₹${cart.couponDiscount.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Divider(color: Color(0xFFCFD8DC)),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D4059),
                ),
              ),
              Text(
                '₹${cart.total.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          NeumorphicButton(
            color: Theme.of(context).primaryColor,
            onPressed: () {
              _proceedToCheckout(context, cart);
            },
            child: const Text(
              'Proceed to Checkout',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearCartDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          'Clear Cart',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D4059),
          ),
        ),
        content: const Text(
          'Are you sure you want to clear your cart? This action cannot be undone.',
          style: TextStyle(
            color: Color(0xFF7D8CA3),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Provider.of<CartProvider>(context, listen: false).clear();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade400,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _proceedToCheckout(BuildContext context, CartProvider cart) {
    if (cart.items.isEmpty) return;

    // Navigate to address selection with all cart items
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddressSelectionScreen(
          cartItems: cart.items,
          totalAmount: cart.total,
        ),
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  void _showCouponModal(BuildContext context, CartProvider cart) {
    // Initialize the coupon provider with the current cart total
    final couponProvider = Provider.of<CouponProvider>(context, listen: false);
    couponProvider.setCartTotal(cart.subtotal);
    couponProvider.loadCoupons();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.75,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (_, controller) {
            return ChangeNotifierProvider.value(
              value: couponProvider,
              child: Consumer<CouponProvider>(
                builder: (context, couponProvider, _) {
                  return const CouponModal();
                },
              ),
            );
          },
        );
      },
    ).then((_) {
      // When modal is closed, check if a coupon was applied
      if (couponProvider.appliedCoupon != null) {
        cart.applyCoupon(couponProvider.appliedCoupon!);
      }
    });
  }
}
