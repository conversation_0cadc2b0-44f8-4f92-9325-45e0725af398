import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';

class HttpClient {
  // Singleton pattern
  static final HttpClient _instance = HttpClient._internal();
  factory HttpClient() => _instance;
  HttpClient._internal() {
    // Initialize the mobile-to-user-ID mapping when the instance is created
    _initializeMobileToUserIdMap();
  }

  // Token storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _mobileToUserIdMapKey = 'mobile_to_user_id_map';

  // Mock user data
  String? _mockMobile;
  final String _mockOtp = "123456"; // Fixed mock OTP for testing
  static final Map<String, String> _mobileToUserIdMap =
      {}; // Track mobile to user ID mapping

  // Debug flag
  final bool _debug = true; // Set to true for debug logging

  // Debug log helper
  void _log(String message) {
    if (_debug) {
      debugPrint('HttpClient: $message');
    }
  }

  // Get access token
  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  // Get refresh token
  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  // Save tokens to shared preferences
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  // Clear tokens from shared preferences
  Future<void> clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    // Note: We don't clear the mobile-to-user-ID mapping on logout
    // This allows users to maintain their user ID across sessions
    _log("Tokens cleared, mobile-to-user-ID mapping preserved");
  }

  // Load mobile-to-user-ID mapping from shared preferences
  Future<void> _loadMobileToUserIdMap() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final mapString = prefs.getString(_mobileToUserIdMapKey);
      if (mapString != null) {
        final Map<String, dynamic> decoded = jsonDecode(mapString);
        _mobileToUserIdMap.clear();
        _mobileToUserIdMap.addAll(Map<String, String>.from(decoded));
        _log("Loaded mobile-to-user-ID mapping: $_mobileToUserIdMap");
      } else {
        _log("No existing mobile-to-user-ID mapping found");
      }
    } catch (e) {
      _log("Error loading mobile-to-user-ID mapping: $e");
    }
  }

  // Save mobile-to-user-ID mapping to shared preferences
  Future<void> _saveMobileToUserIdMap() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final mapString = jsonEncode(_mobileToUserIdMap);
      await prefs.setString(_mobileToUserIdMapKey, mapString);
      _log("Saved mobile-to-user-ID mapping: $_mobileToUserIdMap");
    } catch (e) {
      _log("Error saving mobile-to-user-ID mapping: $e");
    }
  }

  // Initialize mobile-to-user-ID mapping on startup
  Future<void> _initializeMobileToUserIdMap() async {
    try {
      await _loadMobileToUserIdMap();
      _log("Mobile-to-user-ID mapping initialized");
    } catch (e) {
      _log("Error initializing mobile-to-user-ID mapping: $e");
    }
  }

  // GET request (mock implementation)
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    T? Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    return _mockGet<T>(endpoint, fromJson);
  }

  // Mock GET implementation
  Future<ApiResponse<T>> _mockGet<T>(
    String endpoint,
    T? Function(dynamic)? fromJson,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Handle different endpoints
    if (endpoint == '/api/auth/user/info/') {
      // Mock user info
      final userData = {
        'id': 'user_12345',
        'mobile': _mockMobile ?? '+919876543210',
        'user_type': 'CUSTOMER',
        'first_name': 'Test',
        'last_name': 'User',
        'is_active': true,
      };

      return ApiResponse(
        status: 'success',
        message: 'User info retrieved successfully',
        data: fromJson != null ? fromJson(userData) : null,
      );
    }

    // Default response for unhandled endpoints
    return ApiResponse(
      status: 'error',
      message: 'Endpoint not implemented in mock: $endpoint',
      data: null,
    );
  }

  // POST request (mock implementation)
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic body,
    T? Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    return _mockPost<T>(endpoint, body, fromJson);
  }

  // Mock POST implementation
  Future<ApiResponse<T>> _mockPost<T>(
    String endpoint,
    dynamic body,
    T? Function(dynamic)? fromJson,
  ) async {
    _log("_mockPost called with endpoint: $endpoint, body: $body");

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Handle different endpoints
    if (endpoint == '/api/auth/customer/otp/send/') {
      _log("Handling OTP send endpoint");

      // Store mobile for later use
      _mockMobile = body['mobile'] as String;
      _log("Stored mobile: $_mockMobile");

      // Mock send OTP
      _log("Returning mock OTP: $_mockOtp");
      return ApiResponse(
        status: 'success',
        message: 'OTP sent successfully. Use $_mockOtp for testing.',
        data: _mockOtp as T,
      );
    } else if (endpoint == '/api/auth/customer/otp/resend/') {
      _log("Handling OTP resend endpoint");

      // Mock resend OTP
      return ApiResponse(
        status: 'success',
        message: 'OTP resent successfully. Use $_mockOtp for testing.',
        data: _mockOtp as T,
      );
    } else if (endpoint == '/api/auth/customer/otp/verify/') {
      _log("Handling OTP verify endpoint");

      // Mock verify OTP - check against our mock OTP
      final otp = body['otp'] as String;
      final mobile = body['mobile'] as String;

      _log("Verifying OTP: $otp against mock OTP: $_mockOtp");

      if (otp == _mockOtp) {
        _log("OTP verification successful");

        // Ensure mapping is loaded (should already be loaded during initialization)
        if (_mobileToUserIdMap.isEmpty) {
          _log("Mobile-to-user-ID mapping is empty, loading from storage...");
          await _loadMobileToUserIdMap();
        }

        // Check if user already exists for this mobile number
        String userId;
        if (_mobileToUserIdMap.containsKey(mobile)) {
          userId = _mobileToUserIdMap[mobile]!;
          _log("Found existing user ID for mobile $mobile: $userId");
        } else {
          userId = 'user_${mobile}_${DateTime.now().millisecondsSinceEpoch}';
          _mobileToUserIdMap[mobile] = userId;
          _log("Created new user ID for mobile $mobile: $userId");

          // Save the updated mapping
          await _saveMobileToUserIdMap();
        }

        final userData = {
          'id': userId,
          'mobile': mobile,
          'user_type': 'CUSTOMER',
          'first_name': 'Test',
          'last_name': 'User',
          'is_active': true,
        };

        // Generate mock tokens
        final accessToken =
            'mock_access_token_${DateTime.now().millisecondsSinceEpoch}';
        final refreshToken =
            'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';

        // Save tokens
        await saveTokens(accessToken, refreshToken);

        return ApiResponse(
          status: 'success',
          message: 'OTP verified successfully',
          data: {
            'user': userData,
            'access': accessToken,
            'refresh': refreshToken,
          } as T,
        );
      } else {
        _log("OTP verification failed");
        return ApiResponse(
          status: 'error',
          message: 'Invalid OTP. Use $_mockOtp for testing.',
          data: null,
        );
      }
    } else if (endpoint == '/api/auth/token/cookie/logout/') {
      // Mock logout
      _log("Handling logout endpoint");

      // Clear tokens
      await clearTokens();

      // Reset mock mobile
      _mockMobile = null;

      _log("Tokens cleared, mobile reset");

      return ApiResponse(
        status: 'success',
        message: 'Logged out successfully',
        data: null,
      );
    }

    // Default response for unhandled endpoints
    return ApiResponse(
      status: 'error',
      message: 'Endpoint not implemented in mock: $endpoint',
      data: null,
    );
  }

  // PUT request (mock implementation)
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic body,
    T? Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Not implemented in mock
    return ApiResponse(
      status: 'error',
      message: 'PUT requests not implemented in mock API',
      data: null,
    );
  }

  // DELETE request (mock implementation)
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    T? Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Not implemented in mock
    return ApiResponse(
      status: 'error',
      message: 'DELETE requests not implemented in mock API',
      data: null,
    );
  }
}
