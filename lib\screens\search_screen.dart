import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/neumorphic_container.dart';
import '../models/service.dart';
import '../models/service_category.dart';
import '../models/cleaning_product.dart';
import '../providers/product_provider.dart';
import '../screens/service_detail_screen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<CleaningProduct> _searchResults = [];
  bool _isSearching = false;
  String _searchQuery = '';
  bool _isLoading = false;
  String? _error;

  // Categories for display
  final List<ServiceCategory> _categories = [
    ServiceCategory(
      id: '1',
      name: 'Cleaning',
      icon: Icons.cleaning_services,
      color: const Color(0xFF4ECDC4),
      imageUrl:
          'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-**********.jpg',
    ),
    ServiceCategory(
      id: '2',
      name: 'Plumbing',
      icon: Icons.plumbing,
      color: const Color(0xFF6C63FF),
      imageUrl:
          'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
    ),
    ServiceCategory(
      id: '3',
      name: 'Electrical',
      icon: Icons.electrical_services,
      color: const Color(0xFFFF6B6B),
      imageUrl:
          'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-2147734355.jpg',
    ),
    ServiceCategory(
      id: '4',
      name: 'Painting',
      icon: Icons.format_paint,
      color: const Color(0xFFFFD166),
      imageUrl:
          'https://img.freepik.com/free-photo/painter-painting-wall-with-roller_23-2147894089.jpg',
    ),
    ServiceCategory(
      id: '5',
      name: 'Carpenter',
      icon: Icons.handyman,
      color: const Color(0xFF8D6E63),
      imageUrl:
          'https://img.freepik.com/free-photo/carpenter-working-with-equipment-wooden-table-workshop_176474-7920.jpg',
    ),
    ServiceCategory(
      id: '6',
      name: 'Appliance Repair',
      icon: Icons.home_repair_service,
      color: const Color(0xFF118AB2),
      imageUrl:
          'https://img.freepik.com/free-photo/technician-repairing-refrigerator_23-2149176085.jpg',
    ),
    ServiceCategory(
      id: '7',
      name: 'AC Service',
      icon: Icons.ac_unit,
      color: const Color(0xFF64B5F6),
      imageUrl:
          'https://img.freepik.com/free-photo/technician-service-removing-air-filter-air-conditioner-cleaning_35076-3617.jpg',
    ),
    ServiceCategory(
      id: '8',
      name: 'Pest Control',
      icon: Icons.pest_control,
      color: const Color(0xFFEF476F),
      imageUrl:
          'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149326894.jpg',
    ),
    ServiceCategory(
      id: '9',
      name: 'Car Wash',
      icon: Icons.car_repair,
      color: const Color(0xFF26A69A),
      imageUrl:
          'https://img.freepik.com/free-photo/man-washing-his-car-garage_1157-26072.jpg',
    ),
    ServiceCategory(
      id: '10',
      name: 'Commercial Cleaning',
      icon: Icons.business,
      color: const Color(0xFF5C6BC0),
      imageUrl:
          'https://img.freepik.com/free-photo/cleaning-service-office_23-2149374132.jpg',
    ),
    ServiceCategory(
      id: '11',
      name: 'Packers & Movers',
      icon: Icons.local_shipping,
      color: const Color(0xFF073B4C),
      imageUrl:
          'https://img.freepik.com/free-photo/delivery-man-carrying-boxes_23-2148936853.jpg',
    ),
  ];

  // Sample services for initial display
  final List<Service> _popularServices = [
    Service(
      id: '1',
      categoryId: '1',
      name: 'Standard Cleaning',
      description: 'Professional cleaning service for your home.',
      price: 499,
      rating: 4.8,
      reviewCount: 156,
      imageUrl:
          'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-**********.jpg',
      durationMinutes: 120,
    ),
    Service(
      id: '2',
      categoryId: '2',
      name: 'Pipe Repair',
      description: 'Fix leaky pipes and plumbing issues.',
      price: 899,
      rating: 4.7,
      reviewCount: 124,
      imageUrl:
          'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
      durationMinutes: 90,
    ),
    Service(
      id: '3',
      categoryId: '3',
      name: 'Electrical Wiring',
      description: 'Professional electrical wiring and repairs.',
      price: 999,
      rating: 4.8,
      reviewCount: 178,
      imageUrl:
          'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-2147734355.jpg',
      durationMinutes: 120,
    ),
    Service(
      id: '4',
      categoryId: '8',
      name: 'Pest Control',
      description: 'Eliminate pests from your home.',
      price: 1299,
      rating: 4.9,
      reviewCount: 210,
      imageUrl:
          'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149326894.jpg',
      durationMinutes: 150,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();

    // Debounce search to avoid too many API calls
    if (_searchQuery == query) {
      return;
    }

    if (query.isEmpty) {
      setState(() {
        _searchQuery = '';
        _isSearching = false;
        _searchResults = [];
        _isLoading = false;
      });
    } else {
      setState(() {
        _searchQuery = query;
        _isSearching = true;
        _isLoading = true;
      });

      // Special handling for BHK queries
      final normalizedQuery = query.toLowerCase();
      if (normalizedQuery.contains('bhk') ||
          normalizedQuery.contains('1') ||
          normalizedQuery.contains('2') ||
          normalizedQuery.contains('3')) {
        _performSearch(query);
      } else {
        // For other queries, wait a bit before searching
        Future.delayed(const Duration(milliseconds: 300), () {
          // Only perform search if the query hasn't changed
          if (_searchQuery == query) {
            _performSearch(query);
          }
        });
      }
    }
  }

  Future<void> _performSearch(String query) async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Normalize the query
      final normalizedQuery = query.trim();

      if (normalizedQuery.isEmpty) {
        setState(() {
          _searchResults = [];
          _isLoading = false;
        });
        return;
      }

      // Special handling for numeric queries (like "1" for "1 BHK")
      String searchQuery = normalizedQuery;
      bool isNumericQuery = RegExp(r'^\d+$').hasMatch(normalizedQuery);

      // For numeric queries, also try searching for "X BHK"
      if (isNumericQuery) {
        searchQuery = "$normalizedQuery bhk";
        print(
            "Converting numeric query '$normalizedQuery' to BHK query: '$searchQuery'");
      }

      final productProvider =
          Provider.of<ProductProvider>(context, listen: false);
      final results = await productProvider.searchProducts(searchQuery);

      if (mounted) {
        setState(() {
          _searchResults = results;
          _isLoading = false;
        });

        print("Search results count in search screen: ${results.length}");
        for (var product in results) {
          print("Product in search screen: ${product.name}");
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'An error occurred: $e';
          _isLoading = false;
        });
      }
    }
  }

  ServiceCategory _getCategoryById(String categoryId) {
    return _categories.firstWhere(
      (category) => category.id == categoryId,
      orElse: () => _categories.first,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Search',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF2D4059)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NeumorphicContainer(
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Search for services...',
                  prefixIcon: Icon(Icons.search, color: Color(0xFF7D8CA3)),
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_isSearching && _searchResults.isEmpty)
              const Expanded(
                child: Center(
                  child: Text(
                    'No results found',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                ),
              )
            else if (_isSearching)
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Search Results (${_searchResults.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D4059),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _searchResults.length,
                        itemBuilder: (context, index) {
                          final product = _searchResults[index];
                          final category = _getCategoryById(product.categoryId);

                          // Convert CleaningProduct to Service for display
                          final service = Service(
                            id: product.id,
                            categoryId: product.categoryId,
                            name: product.name,
                            description: product.description,
                            price: product.price.toDouble(),
                            rating: product.rating,
                            reviewCount: product.reviews,
                            imageUrl: product.imageUrl,
                            durationMinutes: 120, // Default duration
                          );

                          return _buildServiceItem(service, category);
                        },
                      ),
                    ),
                  ],
                ),
              )
            else if (_error != null)
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => _performSearch(_searchQuery),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Popular Categories',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D4059),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          return _buildCategoryItem(category);
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Popular Services',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D4059),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _popularServices.length,
                        itemBuilder: (context, index) {
                          final service = _popularServices[index];
                          final category = _getCategoryById(service.categoryId);
                          return _buildServiceItem(service, category);
                        },
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(ServiceCategory category) {
    return GestureDetector(
      onTap: () {
        // Search by category name
        _searchController.text = category.name;
        _onSearchChanged();
      },
      child: Container(
        width: 120,
        margin: const EdgeInsets.only(right: 12),
        child: NeumorphicContainer(
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: category.imageUrl != null
                      ? Image.network(
                          category.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildFallbackIcon(category);
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return _buildFallbackIcon(category);
                          },
                        )
                      : _buildFallbackIcon(category),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                category.name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2D4059),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceItem(Service service, ServiceCategory category) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).push(
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  ServiceDetailScreen(service: service, category: category),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeInOutCubic;
                var tween = Tween(begin: begin, end: end).chain(
                  CurveTween(curve: curve),
                );
                var offsetAnimation = animation.drive(tween);
                return SlideTransition(position: offsetAnimation, child: child);
              },
              transitionDuration: const Duration(milliseconds: 500),
            ),
          );
        },
        child: NeumorphicContainer(
          child: Row(
            children: [
              Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                clipBehavior: Clip.antiAlias,
                child: category.imageUrl != null
                    ? Image.network(
                        category.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: category.color.withAlpha(26),
                            child: Icon(
                              category.icon,
                              size: 32,
                              color: category.color,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: category.color.withAlpha(26),
                        child: Icon(
                          category.icon,
                          size: 32,
                          color: category.color,
                        ),
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D4059),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      category.name,
                      style: TextStyle(
                        fontSize: 14,
                        color: category.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16,
                          color: category.color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${service.rating} (${service.reviewCount})',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF7D8CA3),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${service.price.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: category.color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${service.durationMinutes ~/ 60}h ${service.durationMinutes % 60}min',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFallbackIcon(ServiceCategory category) {
    return Container(
      color: category.color.withAlpha(50),
      child: Center(
        child: Icon(
          category.icon,
          size: 30,
          color: category.color,
        ),
      ),
    );
  }
}
