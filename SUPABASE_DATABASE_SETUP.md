# Supabase Database Setup for DodoBooker

## Quick Setup Guide

### 1. Access Your Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Select your DodoBooker project

### 2. Set Up the Database
1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `setup_supabase_database.sql`
3. Paste it into the SQL Editor
4. Click **Run** to execute the script

This will create:
- All necessary tables (categories, products, users, cart, bookings, etc.)
- Row Level Security (RLS) policies
- Sample data for testing

### 3. Enable Phone Authentication
1. Go to **Authentication** > **Settings**
2. Under **Auth Providers**, enable **Phone**
3. Configure your SMS provider (or use Supabase's test phone numbers for development)

### 4. Verify Your Configuration
Your app is already configured with your Supabase credentials:
- **URL**: `https://vlrdhrgahxaxnizkedmv.supabase.co`
- **Anon Key**: Already set in `lib/config/supabase_config.dart`

### 5. Test the Connection
1. Run your Flutter app: `flutter run`
2. Try logging in with phone number: `**********`
3. Use OTP: `123456` (for testing)

## What's Changed in Your App

### Authentication Flow
- **Before**: Used mock APIs for authentication
- **Now**: Uses Supabase authentication with real user management

### Data Storage
- **Before**: In-memory mock data
- **Now**: Real database with persistent data

### User Management
- **Before**: Temporary user sessions
- **Now**: Real user profiles linked to Supabase auth

## Testing the Integration

### 1. Authentication Test
```
Phone: **********
OTP: 123456
```

### 2. Data Persistence Test
- Add items to cart
- Create bookings
- Update profile
- Data will persist across app restarts

### 3. Real-time Features
- Cart updates
- Booking status changes
- Profile modifications

## Troubleshooting

### Common Issues:

1. **"Failed to initialize authentication" error**
   - Check your internet connection
   - Verify Supabase project is active
   - Ensure database tables are created

2. **"Invalid OTP" error**
   - Use `123456` for testing
   - Check if phone authentication is enabled in Supabase

3. **Database connection errors**
   - Verify your Supabase URL and anon key
   - Check if RLS policies are properly set

### Switching Back to Mock APIs
If you need to switch back to mock APIs for testing:
1. Open `lib/services/auth_service.dart`
2. Change `bool _useSupabase = true;` to `bool _useSupabase = false;`

## Next Steps

1. **Configure SMS Provider**: Set up a real SMS provider for production OTP
2. **Add More Data**: Use the Supabase dashboard to add more services and categories
3. **Monitor Usage**: Check the Supabase dashboard for user activity and database usage
4. **Deploy**: Your app is now ready for production with real backend data

## Database Schema Overview

### Core Tables:
- `categories` - Service categories (Cleaning, Plumbing, etc.)
- `subcategories` - Specific service types
- `products` - Individual services with pricing
- `user_profiles` - Customer information
- `cart_items` - Shopping cart data
- `bookings` - Service appointments
- `user_addresses` - Customer addresses
- `coupons` - Discount codes

### Security:
- Row Level Security (RLS) enabled
- Users can only access their own data
- Public read access for categories and products
