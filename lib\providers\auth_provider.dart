import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../models/api_response.dart';
import '../services/auth_service.dart';
import '../providers/api_provider.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  otpSent,
  loading,
  error,
}

class AuthProvider with ChangeNotifier {
  // Use AuthService for API integration
  final _authService = AuthService();

  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _mobile;
  String? _errorMessage;
  String? _mockOtp;

  // Getters
  AuthStatus get status => _status;
  User? get user => _user;
  String? get mobile => _mobile;
  String? get errorMessage => _errorMessage;
  String? get mockOtp => _mockOtp;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;

  // Initialize auth state
  Future<void> initialize() async {
    _status = AuthStatus.loading;
    notifyListeners();

    try {
      // Initialize the auth service (including Supabase if enabled)
      await _authService.initialize();

      final isAuth = await _authService.isAuthenticated();

      if (isAuth) {
        final user = await _authService.getCurrentUser();

        if (user != null) {
          _user = user;
          _status = AuthStatus.authenticated;
        } else {
          _status = AuthStatus.unauthenticated;
        }
      } else {
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      _errorMessage = 'Failed to initialize authentication: $e';
      _status = AuthStatus.error;
    }

    notifyListeners();
  }

  // Send OTP
  Future<bool> sendOtp(String mobile) async {
    _status = AuthStatus.loading;
    _mobile = mobile;
    _errorMessage = null;
    _mockOtp = null;
    notifyListeners();

    try {
      print("AuthProvider: Sending OTP to $mobile");
      final response = await _authService.sendOtp(mobile);
      print(
          "AuthProvider: OTP send response: ${response.status}, message: ${response.message}");

      if (response.isSuccess) {
        // Store the mock OTP if available
        if (response.data != null) {
          _mockOtp = response.data;
          print("AuthProvider: Mock OTP stored: $_mockOtp");
        }

        // Set status to otpSent
        _status = AuthStatus.otpSent;
        print("AuthProvider: Status set to $_status");

        notifyListeners();
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to send OTP';
        _status = AuthStatus.error;
        print("AuthProvider: Error: $_errorMessage");
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Failed to send OTP: $e';
      _status = AuthStatus.error;
      print("AuthProvider: Exception: $_errorMessage");
      notifyListeners();
      return false;
    }
  }

  // Resend OTP
  Future<bool> resendOtp() async {
    if (_mobile == null) {
      _errorMessage = 'Mobile number not provided';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }

    _status = AuthStatus.loading;
    _errorMessage = null;
    _mockOtp = null;
    notifyListeners();

    try {
      final response = await _authService.resendOtp(_mobile!);

      if (response.isSuccess) {
        _status = AuthStatus.otpSent;
        // Store the mock OTP if available
        if (response.data != null) {
          _mockOtp = response.data;
        }
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to resend OTP';
        _status = AuthStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Failed to resend OTP: $e';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  // Verify OTP
  Future<bool> verifyOtp(String otp) async {
    if (_mobile == null) {
      _errorMessage = 'Mobile number not provided';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }

    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _authService.verifyOtp(_mobile!, otp);

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        _status = AuthStatus.authenticated;

        // Set the current user in the ApiProvider
        // Note: This will be handled in the UI using Consumer<AuthProvider>
        // and then calling apiProvider.setCurrentUser(user.id)

        notifyListeners();
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to verify OTP';
        _status = AuthStatus.otpSent; // Stay on OTP screen
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Failed to verify OTP: $e';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    print("AuthProvider: Starting logout process");
    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      await _authService.logout();

      // Reset all state variables
      _user = null;
      _mobile = null;
      _mockOtp = null;

      // Force status to unauthenticated
      _status = AuthStatus.unauthenticated;
      print("AuthProvider: Logout successful, status set to $_status");

      // Ensure the state is updated
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to logout: $e';
      _status = AuthStatus.error;
      print("AuthProvider: Logout failed with error: $_errorMessage");
      notifyListeners();
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (_status != AuthStatus.authenticated) return;

    try {
      final response = await _authService.getUserInfo();

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        notifyListeners();
      }
    } catch (e) {
      // Silently fail, keep existing user data
    }
  }

  // Google Sign-in
  Future<bool> signInWithGoogle() async {
    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _authService.signInWithGoogle();

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.message ?? 'Google sign-in failed';
        _status = AuthStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Google sign-in failed: $e';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    if (_status == AuthStatus.error) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }
}
