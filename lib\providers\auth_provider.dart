import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../models/api_response.dart';
import '../services/auth_service.dart';
import '../providers/api_provider.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  loading,
  error,
}

class AuthProvider with ChangeNotifier {
  // Use AuthService for API integration
  final _authService = AuthService();

  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _errorMessage;

  // Getters
  AuthStatus get status => _status;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;

  // Initialize auth state
  Future<void> initialize() async {
    _status = AuthStatus.loading;
    notifyListeners();

    try {
      // Initialize the auth service (Google OAuth only)
      await _authService.initialize();

      final isAuth = await _authService.isAuthenticated();

      if (isAuth) {
        final user = await _authService.getCurrentUser();

        if (user != null) {
          _user = user;
          _status = AuthStatus.authenticated;
        } else {
          _status = AuthStatus.unauthenticated;
        }
      } else {
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      _errorMessage = 'Failed to initialize authentication: $e';
      _status = AuthStatus.error;
    }

    notifyListeners();
  }

  // Logout
  Future<void> logout() async {
    print("AuthProvider: Starting logout process");
    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      await _authService.logout();

      // Reset all state variables
      _user = null;

      // Force status to unauthenticated
      _status = AuthStatus.unauthenticated;
      print("AuthProvider: Logout successful, status set to $_status");

      // Ensure the state is updated
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to logout: $e';
      _status = AuthStatus.error;
      print("AuthProvider: Logout failed with error: $_errorMessage");
      notifyListeners();
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (_status != AuthStatus.authenticated) return;

    try {
      final response = await _authService.getUserInfo();

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        notifyListeners();
      }
    } catch (e) {
      // Silently fail, keep existing user data
    }
  }

  // Sign up with email and password
  Future<bool> signUp(String email, String password, String name) async {
    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _authService.signUp(email, password, name);

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.message ?? 'Sign up failed';
        _status = AuthStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Sign up failed: $e';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  // Sign in with email and password
  Future<bool> signIn(String email, String password) async {
    _status = AuthStatus.loading;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _authService.signIn(email, password);

      if (response.isSuccess && response.data != null) {
        _user = response.data;
        _status = AuthStatus.authenticated;
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.message ?? 'Login failed';
        _status = AuthStatus.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'Login failed: $e';
      _status = AuthStatus.error;
      notifyListeners();
      return false;
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    if (_status == AuthStatus.error) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }
}
