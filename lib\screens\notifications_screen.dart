import 'package:flutter/material.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<Map<String, dynamic>> _notifications = [
    {
      'id': '1',
      'title': 'Booking Confirmed',
      'message': 'Your cleaning service has been confirmed for tomorrow at 10:00 AM.',
      'time': '2 hours ago',
      'isRead': false,
      'type': 'booking',
    },
    {
      'id': '2',
      'title': 'Special Offer',
      'message': 'Get 20% off on your next electrical service booking!',
      'time': '1 day ago',
      'isRead': true,
      'type': 'promotion',
    },
    {
      'id': '3',
      'title': 'Service Completed',
      'message': 'Your plumbing service has been completed. Please rate your experience.',
      'time': '2 days ago',
      'isRead': true,
      'type': 'service',
    },
    {
      'id': '4',
      'title': 'Payment Received',
      'message': 'We have received your payment of ₹1,200 for the cleaning service.',
      'time': '3 days ago',
      'isRead': true,
      'type': 'payment',
    },
    {
      'id': '5',
      'title': 'New Service Available',
      'message': 'We now offer home sanitization services. Book now!',
      'time': '5 days ago',
      'isRead': true,
      'type': 'promotion',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Notifications',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                for (var notification in _notifications) {
                  notification['isRead'] = true;
                }
              });
            },
            child: const Text(
              'Mark all as read',
              style: TextStyle(
                color: Color(0xFF4ECDC4),
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
      body: _notifications.isEmpty
          ? _buildEmptyState()
          : _buildNotificationsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ll be notified about bookings, offers and more',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _notifications.length,
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildNotificationItem(Map<String, dynamic> notification) {
    final IconData iconData = _getIconForType(notification['type']);
    final Color iconColor = _getColorForType(notification['type']);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: NeumorphicContainer(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!notification['isRead'])
              Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.only(top: 8, right: 8),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                iconData,
                size: 24,
                color: iconColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          notification['title'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                      ),
                      Text(
                        notification['time'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF7D8CA3),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification['message'],
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForType(String type) {
    switch (type) {
      case 'booking':
        return Icons.calendar_today;
      case 'promotion':
        return Icons.local_offer;
      case 'service':
        return Icons.home_repair_service;
      case 'payment':
        return Icons.payment;
      default:
        return Icons.notifications;
    }
  }

  Color _getColorForType(String type) {
    switch (type) {
      case 'booking':
        return const Color(0xFF4ECDC4);
      case 'promotion':
        return const Color(0xFFFFD166);
      case 'service':
        return const Color(0xFF6C63FF);
      case 'payment':
        return const Color(0xFF2D4059);
      default:
        return const Color(0xFF7D8CA3);
    }
  }
}
