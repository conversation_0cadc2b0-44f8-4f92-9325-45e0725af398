/// A utility class for form field validation
class FormValidators {
  /// Validates an email address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your email';
    }
    
    // Basic email validation
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }
  
  /// Validates a password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your password';
    }
    
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    
    return null;
  }
  
  /// Validates a mobile number (Indian format)
  static String? validateMobileNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your mobile number';
    }
    
    // Basic validation for Indian mobile numbers
    if (!RegExp(r'^(\+91[\-\s]?)?[0]?(91)?[6789]\d{9}$').hasMatch(value)) {
      return 'Please enter a valid mobile number';
    }
    
    return null;
  }
  
  /// Validates a name field
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your name';
    }
    
    if (value.length < 2) {
      return 'Name must be at least 2 characters';
    }
    
    return null;
  }
  
  /// Validates an address field
  static String? validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your address';
    }
    
    if (value.length < 5) {
      return 'Address must be at least 5 characters';
    }
    
    return null;
  }
  
  /// Validates a pincode (Indian format)
  static String? validatePincode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your pincode';
    }
    
    // Basic validation for Indian pincodes
    if (!RegExp(r'^\d{6}$').hasMatch(value)) {
      return 'Please enter a valid 6-digit pincode';
    }
    
    return null;
  }
  
  /// Validates a credit card number
  static String? validateCreditCardNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your card number';
    }
    
    // Remove spaces and dashes
    final cleanValue = value.replaceAll(RegExp(r'[\s-]'), '');
    
    // Check if it's all digits
    if (!RegExp(r'^\d+$').hasMatch(cleanValue)) {
      return 'Card number should contain only digits';
    }
    
    // Check length (most cards are 16 digits, but some are 13-19)
    if (cleanValue.length < 13 || cleanValue.length > 19) {
      return 'Card number should be 13-19 digits';
    }
    
    // Luhn algorithm check (optional)
    if (!_validateLuhn(cleanValue)) {
      return 'Invalid card number';
    }
    
    return null;
  }
  
  /// Validates a card expiry date in MM/YY format
  static String? validateCardExpiry(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter expiry date';
    }
    
    // Check format
    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
      return 'Use MM/YY format';
    }
    
    final parts = value.split('/');
    final month = int.tryParse(parts[0]);
    final year = int.tryParse(parts[1]);
    
    if (month == null || year == null) {
      return 'Invalid expiry date';
    }
    
    if (month < 1 || month > 12) {
      return 'Invalid month';
    }
    
    // Check if card is expired
    final now = DateTime.now();
    final cardYear = 2000 + year; // Assuming 20xx
    
    if (cardYear < now.year || (cardYear == now.year && month < now.month)) {
      return 'Card has expired';
    }
    
    return null;
  }
  
  /// Validates a CVV code
  static String? validateCVV(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter CVV';
    }
    
    if (!RegExp(r'^\d{3,4}$').hasMatch(value)) {
      return 'CVV should be 3 or 4 digits';
    }
    
    return null;
  }
  
  /// Validates a UPI ID
  static String? validateUpiId(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter UPI ID';
    }
    
    // Basic UPI ID validation
    if (!RegExp(r'^[\w.-]+@[\w.-]+$').hasMatch(value)) {
      return 'Please enter a valid UPI ID';
    }
    
    return null;
  }
  
  /// Validates an OTP code
  static String? validateOTP(String? value, {int length = 6}) {
    if (value == null || value.isEmpty) {
      return 'Please enter OTP';
    }
    
    if (value.length != length) {
      return 'OTP should be $length digits';
    }
    
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return 'OTP should contain only digits';
    }
    
    return null;
  }
  
  // Luhn algorithm implementation for credit card validation
  static bool _validateLuhn(String value) {
    int sum = 0;
    bool alternate = false;
    
    for (int i = value.length - 1; i >= 0; i--) {
      int digit = int.parse(value[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }
}
