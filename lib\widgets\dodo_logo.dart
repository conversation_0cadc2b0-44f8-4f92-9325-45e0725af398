import 'package:flutter/material.dart';

class DodoLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final Color? backgroundColor;
  final Color textColor;

  const DodoLogo({
    super.key,
    this.size = 100,
    this.showText = true,
    this.backgroundColor,
    this.textColor = Colors.black,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size * 0.6, // Adjust height for the logo aspect ratio
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.black,
            borderRadius: BorderRadius.circular(size / 10),
          ),
          child: Center(
            child: Text(
              'DODO',
              style: TextStyle(
                color: Colors.white,
                fontSize: size * 0.3,
                fontWeight: FontWeight.bold,
                letterSpacing: 2,
              ),
            ),
          ),
        ),
        if (showText) ...[
          const SizedBox(height: 8),
          Text(
            'DODO Booker',
            style: TextStyle(
              color: textColor,
              fontSize: size * 0.2,
              fontWeight: FontWeight.bold,
              letterSpacing: 1,
            ),
          ),
        ],
      ],
    );
  }
}
