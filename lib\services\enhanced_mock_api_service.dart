import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'base_mock_api_service.dart';

/// Enhanced Mock API Service that provides separate endpoints for different data types
/// with realtime data simulation and proper logging
class EnhancedMockApiService extends BaseMockApiService {
  // Singleton pattern
  static final EnhancedMockApiService _instance =
      EnhancedMockApiService._internal();
  factory EnhancedMockApiService() => _instance;

  // In-memory database for realtime data
  final Map<String, dynamic> _database = {};

  // Stream controllers for realtime updates
  final StreamController<List<Map<String, dynamic>>>
      _categoriesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _subcategoriesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _productsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<Map<String, dynamic>> _cartStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _bookingsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<Map<String, dynamic>> _profileStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _chatMessagesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();

  // Getters for streams
  Stream<List<Map<String, dynamic>>> get categoriesStream =>
      _categoriesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get subcategoriesStream =>
      _subcategoriesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get productsStream =>
      _productsStreamController.stream;
  Stream<Map<String, dynamic>> get cartStream => _cartStreamController.stream;
  Stream<List<Map<String, dynamic>>> get bookingsStream =>
      _bookingsStreamController.stream;
  Stream<Map<String, dynamic>> get profileStream =>
      _profileStreamController.stream;
  Stream<List<Map<String, dynamic>>> get chatMessagesStream =>
      _chatMessagesStreamController.stream;

  // Current user ID (set after authentication)
  String? _currentUserId;

  // Mock chat support agent
  final Map<String, dynamic> _supportAgent = {
    'id': 'agent-001',
    'name': 'Support Agent',
    'avatar': 'https://randomuser.me/api/portraits/women/44.jpg',
    'isOnline': true,
  };

  EnhancedMockApiService._internal() {
    // Initialize the in-memory database
    _initializeDatabase();

    // Set up periodic updates to simulate realtime data changes
    Timer.periodic(const Duration(minutes: 5), (_) {
      _simulateDataUpdates();
    });

    // Set up automated chat responses
    Timer.periodic(const Duration(seconds: 30), (_) {
      _simulateAgentResponses();
    });
  }

  // Initialize the in-memory database with data
  void _initializeDatabase() {
    _initializeCategories();
    _initializeProducts();
    _initializeUsers();
    _initializeBookings();
    _initializeCart();
    _initializeChatMessages();
    _initializeNotifications();
    _initializeBanners();

    // Notify listeners about initial data
    _categoriesStreamController.add(_database['categories']);
    _subcategoriesStreamController.add(_database['subcategories']);
    _productsStreamController.add(_database['products']);
  }

  // Initialize categories and subcategories
  void _initializeCategories() {
    final List<Map<String, dynamic>> allCategories = [
      {
        'id': '1',
        'name': 'Cleaning',
        'icon': Icons.cleaning_services.codePoint,
        'color': const Color(0xFF4ECDC4).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
        'description': 'Professional cleaning services for homes and offices',
      },
      {
        'id': '2',
        'name': 'Plumbing',
        'icon': Icons.plumbing.codePoint,
        'color': const Color(0xFF6C63FF).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
        'description': 'Expert plumbing services for all your needs',
      },
      {
        'id': '3',
        'name': 'Electrical',
        'icon': Icons.electrical_services.codePoint,
        'color': const Color(0xFFFF6B6B).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-2147734355.jpg',
        'description': 'Safe and reliable electrical services',
      },
      {
        'id': '4',
        'name': 'Carpenter',
        'icon': Icons.handyman.codePoint,
        'color': const Color(0xFFFFB347).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/carpenter-working-with-equipment-wooden-table-workshop_176474-7920.jpg',
        'description': 'Custom carpentry and furniture repair',
      },
      {
        'id': '5',
        'name': 'Appliances',
        'icon': Icons.kitchen.codePoint,
        'color': const Color(0xFF5D5FEF).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-repairing-refrigerator_23-2149176085.jpg',
        'description': 'Repair and maintenance for all home appliances',
      },
      {
        'id': '6',
        'name': 'Painting',
        'icon': Icons.format_paint.codePoint,
        'color': const Color(0xFF00C2A8).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/painter-painting-walls-blue-color_23-2147694133.jpg',
        'description': 'Professional painting services for your home',
      },
      {
        'id': '7',
        'name': 'AC Services',
        'icon': Icons.ac_unit.codePoint,
        'color': const Color(0xFF4A90E2).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-installing-air-conditioner_23-2149176105.jpg',
        'description': 'Installation, repair, and maintenance for all AC units',
      },
      {
        'id': '8',
        'name': 'Pest Control',
        'icon': Icons.bug_report.codePoint,
        'color': const Color(0xFFE57373).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149326846.jpg',
        'description': 'Effective pest control solutions for your home',
      },
      {
        'id': '9',
        'name': 'Car Wash',
        'icon': Icons.local_car_wash.codePoint,
        'color': const Color(0xFF42A5F5).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/man-washing-his-car-garage_1157-26072.jpg',
        'description': 'Professional car washing and detailing services',
      },
      {
        'id': '10',
        'name': 'Packers & Movers',
        'icon': Icons.local_shipping.codePoint,
        'color': const Color(0xFF9575CD).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/delivery-concept-handsome-african-american-delivery-man-carrying-package-box_1258-26678.jpg',
        'description': 'Reliable packing and moving services',
      },
    ];

    final List<Map<String, dynamic>> allSubcategories = [
      // Cleaning subcategories
      {
        'id': '1-1',
        'parentId': '1',
        'name': 'Furnished Apartment',
        'icon': Icons.apartment.codePoint,
        'color': const Color(0xFF4ECDC4).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/modern-studio-apartment-design-with-bedroom-living-space_1262-12375.jpg',
        'hasProducts': true,
      },
      {
        'id': '1-2',
        'parentId': '1',
        'name': 'Office Cleaning',
        'icon': Icons.business.codePoint,
        'color': const Color(0xFF4ECDC4).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/cleaning-service-office_23-2149374132.jpg',
        'hasProducts': true,
      },
      {
        'id': '1-3',
        'parentId': '1',
        'name': 'Deep Cleaning',
        'icon': Icons.cleaning_services.codePoint,
        'color': const Color(0xFF4ECDC4).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-bathroom_23-2148113510.jpg',
        'hasProducts': true,
      },

      // Plumbing subcategories
      {
        'id': '2-1',
        'parentId': '2',
        'name': 'Pipe Repair',
        'icon': Icons.plumbing.codePoint,
        'color': const Color(0xFF6C63FF).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
        'hasProducts': true,
      },
      {
        'id': '2-2',
        'parentId': '2',
        'name': 'Tap Installation',
        'icon': Icons.water.codePoint,
        'color': const Color(0xFF6C63FF).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-installing-new-faucet-bathroom_23-2149138755.jpg',
        'hasProducts': true,
      },
      {
        'id': '2-3',
        'parentId': '2',
        'name': 'Toilet Repair',
        'icon': Icons.wc.codePoint,
        'color': const Color(0xFF6C63FF).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-fixing-toilet_23-2149138756.jpg',
        'hasProducts': true,
      },

      // Electrical subcategories
      {
        'id': '3-1',
        'parentId': '3',
        'name': 'Switch Installation',
        'icon': Icons.toggle_on.codePoint,
        'color': const Color(0xFFFF6B6B).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-installing-electrical-socket_23-2149176104.jpg',
        'hasProducts': true,
      },
      {
        'id': '3-2',
        'parentId': '3',
        'name': 'Wiring',
        'icon': Icons.cable.codePoint,
        'color': const Color(0xFFFF6B6B).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-working-with-wires_23-2149176103.jpg',
        'hasProducts': true,
      },
      {
        'id': '3-3',
        'parentId': '3',
        'name': 'Fan Installation',
        'icon': Icons.air.codePoint,
        'color': const Color(0xFFFF6B6B).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-installing-ceiling-fan_23-2149176102.jpg',
        'hasProducts': true,
      },

      // Add more subcategories for other categories...
      // Carpenter subcategories
      {
        'id': '4-1',
        'parentId': '4',
        'name': 'Furniture Repair',
        'icon': Icons.chair.codePoint,
        'color': const Color(0xFFFFB347).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/carpenter-repairing-wooden-chair_23-2149176101.jpg',
        'hasProducts': true,
      },
      {
        'id': '4-2',
        'parentId': '4',
        'name': 'Door Installation',
        'icon': Icons.door_sliding.codePoint,
        'color': const Color(0xFFFFB347).toARGB32(),
        'imageUrl':
            'https://img.freepik.com/free-photo/carpenter-installing-door_23-2149176100.jpg',
        'hasProducts': true,
      },
    ];

    // Store categories and subcategories separately
    _database['categories'] = allCategories;
    _database['subcategories'] = allSubcategories;
  }

  // Initialize products
  void _initializeProducts() {
    final List<Map<String, dynamic>> allProducts = [];

    // Cleaning products - Furnished Apartment
    allProducts.addAll([
      {
        'id': 'clean-1',
        'categoryId': '1-1',
        'name': 'Basic Apartment Cleaning',
        'description':
            'Standard cleaning service for furnished apartments including dusting, vacuuming, and bathroom cleaning.',
        'price': 999,
        'rating': 4.5,
        'reviews': 1200,
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
        'includes': [
          'Dusting all accessible surfaces',
          'Vacuuming carpets and floors',
          'Bathroom cleaning',
          'Kitchen countertop cleaning',
          'Trash removal'
        ],
        'excludes': [
          'Window cleaning',
          'Laundry',
          'Dish washing',
          'Inside cabinet cleaning',
          'Balcony cleaning'
        ],
        'duration': '2 hours',
        'inStock': true,
      },
      {
        'id': 'clean-2',
        'categoryId': '1-1',
        'name': 'Premium Apartment Cleaning',
        'description':
            'Comprehensive cleaning service for furnished apartments including all basic services plus window cleaning, inside cabinet cleaning, and more.',
        'price': 1499,
        'rating': 4.8,
        'reviews': 850,
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-window_23-2148113511.jpg',
        'includes': [
          'All basic cleaning services',
          'Window cleaning',
          'Inside cabinet cleaning',
          'Balcony cleaning',
          'Refrigerator cleaning',
          'Oven cleaning'
        ],
        'excludes': [
          'Laundry',
          'Dish washing',
          'Wall cleaning',
          'Ceiling fan cleaning'
        ],
        'duration': '4 hours',
        'inStock': true,
      },
    ]);

    // Plumbing products - Pipe Repair
    allProducts.addAll([
      {
        'id': 'plumb-1',
        'categoryId': '2-1',
        'name': 'Pipe Leak Repair',
        'description':
            'Professional repair service for leaking pipes. Our technicians will identify and fix the leak to prevent water damage.',
        'price': 799,
        'rating': 4.7,
        'reviews': 950,
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
        'includes': [
          'Leak detection',
          'Basic pipe repair',
          'Water pressure testing',
          'Basic materials',
          '7-day service guarantee'
        ],
        'excludes': [
          'Pipe replacement (if needed)',
          'Wall repair after access',
          'Premium materials',
          'Water damage restoration'
        ],
        'duration': '1-2 hours',
        'inStock': true,
      },
      {
        'id': 'plumb-2',
        'categoryId': '2-1',
        'name': 'Pipe Replacement',
        'description':
            'Complete pipe replacement service for damaged or old pipes. Includes removal of old pipes and installation of new ones.',
        'price': 1499,
        'rating': 4.6,
        'reviews': 720,
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-replacing-pipes_23-2149138753.jpg',
        'includes': [
          'Old pipe removal',
          'New pipe installation',
          'Water pressure testing',
          'Basic materials',
          '30-day service guarantee'
        ],
        'excludes': [
          'Wall repair after access',
          'Premium materials',
          'Water damage restoration'
        ],
        'duration': '3-4 hours',
        'inStock': true,
      },
    ]);

    // Electrical products - Switch Installation
    allProducts.addAll([
      {
        'id': 'elec-1',
        'categoryId': '3-1',
        'name': 'Basic Switch Installation',
        'description':
            'Professional installation of light switches, fan switches, or power outlets. Includes removal of old switch if needed.',
        'price': 399,
        'rating': 4.8,
        'reviews': 1500,
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-installing-electrical-socket_23-2149176104.jpg',
        'includes': [
          'Old switch removal',
          'New switch installation',
          'Basic testing',
          'Standard switch',
          '7-day service guarantee'
        ],
        'excludes': [
          'Premium switches',
          'Wiring repair/replacement',
          'Wall repair'
        ],
        'duration': '30-45 minutes',
        'inStock': true,
      },
      {
        'id': 'elec-2',
        'categoryId': '3-1',
        'name': 'Smart Switch Installation',
        'description':
            'Installation of smart switches that can be controlled via smartphone or voice assistants. Includes setup and configuration.',
        'price': 899,
        'rating': 4.9,
        'reviews': 650,
        'imageUrl':
            'https://img.freepik.com/free-photo/smart-home-automation-system-mobile-phone_23-2149176106.jpg',
        'includes': [
          'Old switch removal',
          'Smart switch installation',
          'WiFi connection setup',
          'App configuration',
          'Usage demonstration',
          '15-day service guarantee'
        ],
        'excludes': [
          'Smart switch cost',
          'Wiring repair/replacement',
          'Wall repair'
        ],
        'duration': '1-1.5 hours',
        'inStock': true,
      },
    ]);

    // Store products
    _database['products'] = allProducts;
  }

  // Initialize user profiles
  void _initializeUsers() {
    _database['profiles'] = [
      {
        'userId': 'user-001',
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '7412589633',
        'addresses': [
          {
            'id': 'addr-001',
            'type': 'Home',
            'address': '123 Main St, Apartment 4B',
            'city': 'Mumbai',
            'state': 'Maharashtra',
            'pincode': '400001',
            'isDefault': true,
          },
          {
            'id': 'addr-002',
            'type': 'Work',
            'address': '456 Office Park, Building C',
            'city': 'Mumbai',
            'state': 'Maharashtra',
            'pincode': '400051',
            'isDefault': false,
          },
        ],
        'preferences': {
          'notifications': true,
          'emailUpdates': false,
          'darkMode': false,
        },
      },
    ];
  }

  // Initialize bookings
  void _initializeBookings() {
    _database['bookings'] = [];
  }

  // Initialize cart
  void _initializeCart() {
    _database['carts'] = {};
  }

  // Initialize chat messages
  void _initializeChatMessages() {
    _database['chatMessages'] = [];
  }

  // Initialize notifications
  void _initializeNotifications() {
    _database['notifications'] = [];
  }

  // Initialize banners
  void _initializeBanners() {
    _database['banners'] = [
      {
        'id': 'banner-1',
        'title': 'Professional Electrical Services',
        'subtitle': 'Reliable and safe solutions',
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-2147734355.jpg',
        'actionText': 'Explore',
        'targetCategoryId': '3',
        'isActive': true,
        'priority': 1,
      },
      {
        'id': 'banner-2',
        'title': 'Home Cleaning Special',
        'subtitle': 'Get 15% off on your first booking',
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
        'actionText': 'Clean Now',
        'targetCategoryId': '1',
        'isActive': true,
        'priority': 2,
      },
      {
        'id': 'banner-3',
        'title': 'Plumbing Services',
        'subtitle': 'Fix leaks and repairs',
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
        'actionText': 'Book Now',
        'targetCategoryId': '2',
        'isActive': true,
        'priority': 3,
      },
    ];
  }

  // Set current user after authentication
  void setCurrentUser(String userId) {
    _currentUserId = userId;

    // Initialize carts if it doesn't exist
    if (!_database.containsKey('carts')) {
      _database['carts'] = <String, dynamic>{};
    }

    // Ensure carts is a Map<String, dynamic>
    Map<String, dynamic> carts;
    if (_database['carts'] is Map) {
      carts = Map<String, dynamic>.from(_database['carts'] as Map);
    } else {
      carts = <String, dynamic>{};
      _database['carts'] = carts;
    }

    // Initialize user's cart if it doesn't exist
    if (!carts.containsKey(userId)) {
      carts[userId] = {
        'userId': userId,
        'items': [],
        'totalAmount': 0,
        'discount': 0,
        'tax': 0,
        'finalAmount': 0,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      _database['carts'] = carts;
    }

    // Send initial profile data
    final profiles = List<Map<String, dynamic>>.from(_database['profiles']);
    try {
      final profile = profiles.firstWhere(
        (p) => p['userId'] == userId,
      );
      _profileStreamController.add(profile);
    } catch (e) {
      debugPrint('EnhancedMockAPI: No profile found for user $userId');
    }

    // Send initial cart data
    final userCarts = Map<String, dynamic>.from(_database['carts'] as Map);
    _cartStreamController.add(userCarts[userId]);

    // Send initial bookings data
    final bookings = List<Map<String, dynamic>>.from(_database['bookings']);
    final userBookings =
        bookings.where((booking) => booking['userId'] == userId).toList();
    _bookingsStreamController.add(userBookings);

    // Send initial chat messages
    final chatMessages =
        List<Map<String, dynamic>>.from(_database['chatMessages']);
    final userMessages = chatMessages
        .where((msg) => msg['userId'] == userId || msg['recipientId'] == userId)
        .toList();
    _chatMessagesStreamController.add(userMessages);

    debugPrint('EnhancedMockAPI: Set current user to $userId');
  }

  // Simulate realtime data updates
  void _simulateDataUpdates() {
    // Simulate price changes for random products (10% of products)
    final products = List<Map<String, dynamic>>.from(_database['products']);
    final numProductsToUpdate = (products.length * 0.1).round();

    // Use the base class method to simulate price updates
    simulatePriceUpdates(products, numProductsToUpdate);

    // Update the database
    _database['products'] = products;

    // Notify listeners about the updates
    _productsStreamController.add(products);

    debugPrint(
        'EnhancedMockAPI: Simulated realtime updates for $numProductsToUpdate products');
  }

  // Simulate automated chat responses from support agent
  void _simulateAgentResponses() {
    if (_currentUserId == null) return;

    final chatMessages =
        List<Map<String, dynamic>>.from(_database['chatMessages']);

    // Check if there are any unresponded user messages
    final userMessages = chatMessages
        .where((msg) =>
            msg['userId'] == _currentUserId &&
            msg['recipientId'] == _supportAgent['id'] &&
            !msg['isRead'])
        .toList();

    if (userMessages.isEmpty) return;

    // Mark messages as read
    for (final msg in userMessages) {
      msg['isRead'] = true;
    }

    // Generate a response to the latest message
    final latestMessage = userMessages.reduce((a, b) =>
        DateTime.parse(a['timestamp']).isAfter(DateTime.parse(b['timestamp']))
            ? a
            : b);

    // Add agent's response
    final response = _generateAgentResponse(latestMessage['content']);
    chatMessages.add({
      'id': 'msg-${DateTime.now().millisecondsSinceEpoch}',
      'userId': _supportAgent['id'],
      'recipientId': _currentUserId!,
      'content': response,
      'timestamp': DateTime.now().toIso8601String(),
      'isRead': false,
    });

    // Update the database
    _database['chatMessages'] = chatMessages;

    // Notify listeners
    final userRelatedMessages = chatMessages
        .where((msg) =>
            msg['userId'] == _currentUserId ||
            msg['recipientId'] == _currentUserId)
        .toList();
    _chatMessagesStreamController.add(userRelatedMessages);

    debugPrint('EnhancedMockAPI: Generated agent response to user message');
  }

  // Generate a response based on user message
  String _generateAgentResponse(String userMessage) {
    final lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.contains('hello') || lowerMessage.contains('hi')) {
      return 'Hello! How can I help you today with your home services?';
    } else if (lowerMessage.contains('booking') ||
        lowerMessage.contains('appointment')) {
      return 'I can help you with your booking. Could you please provide more details about the service you need?';
    } else if (lowerMessage.contains('cancel')) {
      return 'I understand you want to cancel a booking. Please provide the booking ID, and I\'ll assist you with the cancellation process.';
    } else if (lowerMessage.contains('refund')) {
      return 'For refund inquiries, we typically process refunds within 5-7 business days. Is there a specific refund you\'re asking about?';
    } else if (lowerMessage.contains('price') ||
        lowerMessage.contains('cost')) {
      return 'Our pricing depends on the specific service and requirements. Could you tell me which service you\'re interested in?';
    } else if (lowerMessage.contains('thank')) {
      return 'You\'re welcome! Is there anything else I can help you with today?';
    } else {
      return 'Thank you for your message. Our team will review your query and get back to you shortly. Is there anything specific you need immediate assistance with?';
    }
  }

  // Debug logging helper
  void _logApiCall(String endpoint, {Map<String, dynamic>? params}) {
    logApiCall(endpoint, params: params, isEnhanced: true);
  }

  // Simulate network delay - wrapper around the base class method
  Future<void> _simulateNetworkDelay() async {
    await simulateNetworkDelay();
  }

  //
  // CATEGORY API METHODS
  //

  // Get all categories
  Future<Map<String, dynamic>> getCategories() async {
    _logApiCall('getCategories');
    await _simulateNetworkDelay();

    final result = {
      'status': 'success',
      'data': _database['categories'],
    };

    debugPrint(
        'EnhancedMockAPI: getCategories returned ${(result['data'] as List).length} categories');
    return result;
  }

  // Get categories with realtime updates
  Stream<Map<String, dynamic>> getCategoriesStream() {
    return categoriesStream.map((categories) => {
          'status': 'success',
          'data': categories,
        });
  }

  // Get category by ID
  Future<Map<String, dynamic>> getCategoryById(String categoryId) async {
    _logApiCall('getCategoryById', params: {'categoryId': categoryId});
    await _simulateNetworkDelay();

    final categories = List<Map<String, dynamic>>.from(_database['categories']);
    final category = categories.firstWhere(
      (c) => c['id'] == categoryId,
      orElse: () => <String, dynamic>{},
    );

    if (category.isEmpty) {
      return {
        'status': 'error',
        'message': 'Category not found',
      };
    }

    return {
      'status': 'success',
      'data': category,
    };
  }

  //
  // SUBCATEGORY API METHODS
  //

  // Get all subcategories
  Future<Map<String, dynamic>> getSubcategories() async {
    _logApiCall('getSubcategories');
    await _simulateNetworkDelay();

    final result = {
      'status': 'success',
      'data': _database['subcategories'],
    };

    debugPrint(
        'EnhancedMockAPI: getSubcategories returned ${(result['data'] as List).length} subcategories');
    return result;
  }

  // Get subcategories by parent category ID
  Future<Map<String, dynamic>> getSubcategoriesByParentId(
      String parentId) async {
    _logApiCall('getSubcategoriesByParentId', params: {'parentId': parentId});
    await _simulateNetworkDelay();

    final subcategories =
        List<Map<String, dynamic>>.from(_database['subcategories']);
    final filteredSubcategories =
        subcategories.where((s) => s['parentId'] == parentId).toList();

    return {
      'status': 'success',
      'data': filteredSubcategories,
    };
  }

  // Get subcategories with realtime updates
  Stream<Map<String, dynamic>> getSubcategoriesStream() {
    return subcategoriesStream.map((subcategories) => {
          'status': 'success',
          'data': subcategories,
        });
  }

  //
  // PRODUCT API METHODS
  //

  // Get all products
  Future<Map<String, dynamic>> getProducts() async {
    _logApiCall('getProducts');
    await _simulateNetworkDelay();

    final result = {
      'status': 'success',
      'data': _database['products'],
    };

    debugPrint(
        'EnhancedMockAPI: getProducts returned ${(result['data'] as List).length} products');
    return result;
  }

  // Get products by category ID
  Future<Map<String, dynamic>> getProductsByCategory(String categoryId) async {
    _logApiCall('getProductsByCategory', params: {'categoryId': categoryId});
    await _simulateNetworkDelay();

    // Simulate occasional server errors (1% chance)
    final random = Random();
    if (random.nextInt(100) == 0) {
      debugPrint(
          'EnhancedMockAPI: Simulating server error for getProductsByCategory');
      return createErrorResponse(
          'Server temporarily unavailable. Please try again.');
    }

    // Get products from the in-memory database
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Filter products by category ID
    final products = allProducts.where((product) {
      // Check if the product has a categoryId field
      if (product.containsKey('categoryId')) {
        return product['categoryId'] == categoryId;
      }

      // If no categoryId field, check if the id starts with the category prefix
      final id = product['id'] as String;
      if (categoryId.contains('-')) {
        // For subcategories (e.g., "1-1")
        return id.startsWith(categoryId.split('-')[0]);
      } else {
        // For main categories (e.g., "1")
        return id.startsWith(categoryId);
      }
    }).toList();

    // Add some randomness to simulate dynamic data
    products.shuffle(random);

    // Simulate some products being out of stock
    for (var product in products) {
      if (!product.containsKey('inStock')) {
        product['inStock'] =
            random.nextDouble() > 0.2; // 80% chance of being in stock
      }
    }

    final result = {
      'status': 'success',
      'data': products,
    };

    debugPrint(
        'EnhancedMockAPI: getProductsByCategory returned ${products.length} products for category $categoryId');
    return result;
  }

  // Get product details
  Future<Map<String, dynamic>> getProductDetails(String productId) async {
    _logApiCall('getProductDetails', params: {'productId': productId});
    await _simulateNetworkDelay();

    // Get products from the in-memory database
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Find the product by ID
    final product = allProducts.firstWhere(
      (p) => p['id'] == productId,
      orElse: () => <String, dynamic>{},
    );

    if (product.isEmpty) {
      return {
        'status': 'error',
        'message': 'Product not found',
      };
    }

    // Add view count to simulate analytics
    if (!product.containsKey('viewCount')) {
      product['viewCount'] = 0;
    }
    product['viewCount'] = (product['viewCount'] as int) + 1;

    // Add last viewed timestamp
    product['lastViewed'] = DateTime.now().toIso8601String();

    // Update the product in the database
    final productIndex = allProducts.indexWhere((p) => p['id'] == productId);
    if (productIndex != -1) {
      allProducts[productIndex] = product;
      _database['products'] = allProducts;

      // Notify listeners about the update
      _productsStreamController.add(allProducts);
    }

    return {
      'status': 'success',
      'data': product,
    };
  }

  // Get products with realtime updates
  Stream<Map<String, dynamic>> getProductsStream() {
    return productsStream.map((products) => {
          'status': 'success',
          'data': products,
        });
  }

  // Search products
  Future<Map<String, dynamic>> searchProducts(String query) async {
    _logApiCall('searchProducts', params: {'query': query});
    await _simulateNetworkDelay();

    if (query.isEmpty) {
      return {
        'status': 'success',
        'data': [],
      };
    }

    // Get products from the in-memory database
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Normalize the search query
    final searchQuery = query.toLowerCase().trim();

    final results = allProducts.where((product) {
      final name = product['name'].toString().toLowerCase();
      final description = product['description'].toString().toLowerCase();

      return name.contains(searchQuery) || description.contains(searchQuery);
    }).toList();

    // Simulate search relevance by sorting results
    results.sort((a, b) {
      final aName = a['name'].toString().toLowerCase();
      final bName = b['name'].toString().toLowerCase();

      // If one contains the exact query and the other doesn't, prioritize the exact match
      final aExactMatch = aName.contains(searchQuery);
      final bExactMatch = bName.contains(searchQuery);

      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;

      // Otherwise sort by name
      return aName.compareTo(bName);
    });

    return {
      'status': 'success',
      'data': results,
    };
  }

  //
  // CART API METHODS
  //

  // Get user's cart
  Future<Map<String, dynamic>> getCart() async {
    _logApiCall('getCart');
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final carts = Map<String, dynamic>.from(_database['carts'] as Map);
    final cart = carts[_currentUserId!];

    return {
      'status': 'success',
      'data': cart,
    };
  }

  // Add item to cart
  Future<Map<String, dynamic>> addToCart(String productId, int quantity) async {
    _logApiCall('addToCart',
        params: {'productId': productId, 'quantity': quantity});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    // Get the product details
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);
    final product = allProducts.firstWhere(
      (p) => p['id'] == productId,
      orElse: () => <String, dynamic>{},
    );

    if (product.isEmpty) {
      return {
        'status': 'error',
        'message': 'Product not found',
      };
    }

    // Get the user's cart
    final cart = Map<String, dynamic>.from(
        (_database['carts'] as Map<String, dynamic>)[_currentUserId!]);
    final items = List<Map<String, dynamic>>.from(cart['items']);

    // Check if the product is already in the cart
    final existingItemIndex =
        items.indexWhere((item) => item['productId'] == productId);

    if (existingItemIndex != -1) {
      // Update existing item
      final item = Map<String, dynamic>.from(items[existingItemIndex]);
      item['quantity'] = quantity;
      item['totalPrice'] = product['price'] * quantity;
      items[existingItemIndex] = item;
    } else {
      // Add new item
      items.add({
        'productId': productId,
        'name': product['name'],
        'price': product['price'],
        'quantity': quantity,
        'totalPrice': product['price'] * quantity,
        'imageUrl': product['imageUrl'],
        'addedAt': DateTime.now().toIso8601String(),
      });
    }

    // Recalculate cart totals
    final totalAmount =
        items.fold<int>(0, (sum, item) => sum + (item['totalPrice'] as int));
    final tax = (totalAmount * 0.05).round(); // 5% tax
    final finalAmount = totalAmount + tax;

    // Update the cart
    cart['items'] = items;
    cart['totalAmount'] = totalAmount;
    cart['tax'] = tax;
    cart['finalAmount'] = finalAmount;
    cart['lastUpdated'] = DateTime.now().toIso8601String();

    // Save to database
    (_database['carts'] as Map<String, dynamic>)[_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return {
      'status': 'success',
      'data': cart,
      'message': 'Item added to cart',
    };
  }

  // Remove item from cart
  Future<Map<String, dynamic>> removeFromCart(String productId) async {
    _logApiCall('removeFromCart', params: {'productId': productId});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    // Get the user's cart
    final cart = Map<String, dynamic>.from(
        (_database['carts'] as Map<String, dynamic>)[_currentUserId!]);
    final items = List<Map<String, dynamic>>.from(cart['items']);

    // Remove the item
    final newItems =
        items.where((item) => item['productId'] != productId).toList();

    if (newItems.length == items.length) {
      return {
        'status': 'error',
        'message': 'Item not found in cart',
      };
    }

    // Recalculate cart totals
    final totalAmount =
        newItems.fold<int>(0, (sum, item) => sum + (item['totalPrice'] as int));
    final tax = (totalAmount * 0.05).round(); // 5% tax
    final finalAmount = totalAmount + tax;

    // Update the cart
    cart['items'] = newItems;
    cart['totalAmount'] = totalAmount;
    cart['tax'] = tax;
    cart['finalAmount'] = finalAmount;
    cart['lastUpdated'] = DateTime.now().toIso8601String();

    // Save to database
    (_database['carts'] as Map<String, dynamic>)[_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return {
      'status': 'success',
      'data': cart,
      'message': 'Item removed from cart',
    };
  }

  // Clear cart
  Future<Map<String, dynamic>> clearCart() async {
    _logApiCall('clearCart');
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    // Create empty cart
    final cart = {
      'userId': _currentUserId,
      'items': [],
      'totalAmount': 0,
      'discount': 0,
      'tax': 0,
      'finalAmount': 0,
      'lastUpdated': DateTime.now().toIso8601String(),
    };

    // Save to database
    (_database['carts'] as Map<String, dynamic>)[_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return {
      'status': 'success',
      'data': cart,
      'message': 'Cart cleared',
    };
  }

  // Apply coupon to cart
  Future<Map<String, dynamic>> applyCoupon(String couponCode) async {
    _logApiCall('applyCoupon', params: {'couponCode': couponCode});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    // Get the user's cart
    final cart = Map<String, dynamic>.from(
        (_database['carts'] as Map<String, dynamic>)[_currentUserId!]);

    // Validate coupon code
    int discountPercentage;
    if (couponCode == 'WELCOME10') {
      discountPercentage = 10;
    } else if (couponCode == 'SUMMER20') {
      discountPercentage = 20;
    } else if (couponCode == 'FESTIVAL25') {
      discountPercentage = 25;
    } else {
      return {
        'status': 'error',
        'message': 'Invalid coupon code',
      };
    }

    // Calculate discount
    final totalAmount = cart['totalAmount'] as int;
    final discount = (totalAmount * discountPercentage / 100).round();
    final tax = ((totalAmount - discount) * 0.05)
        .round(); // 5% tax on discounted amount
    final finalAmount = totalAmount - discount + tax;

    // Update the cart
    cart['discount'] = discount;
    cart['discountCode'] = couponCode;
    cart['discountPercentage'] = discountPercentage;
    cart['tax'] = tax;
    cart['finalAmount'] = finalAmount;
    cart['lastUpdated'] = DateTime.now().toIso8601String();

    // Save to database
    (_database['carts'] as Map<String, dynamic>)[_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return {
      'status': 'success',
      'data': cart,
      'message': 'Coupon applied successfully',
    };
  }

  // Get cart with realtime updates
  Stream<Map<String, dynamic>> getCartStream() {
    return cartStream.map((cart) => {
          'status': 'success',
          'data': cart,
        });
  }

  //
  // BOOKING API METHODS
  //

  // Create a booking from cart
  Future<Map<String, dynamic>> createBooking(
      Map<String, dynamic> bookingDetails) async {
    _logApiCall('createBooking', params: {'bookingDetails': bookingDetails});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    // Get the user's cart
    final cart = Map<String, dynamic>.from(
        (_database['carts'] as Map<String, dynamic>)[_currentUserId!]);

    if ((cart['items'] as List).isEmpty) {
      return {
        'status': 'error',
        'message': 'Cart is empty',
      };
    }

    // Create a new booking
    final bookingId = 'booking-${DateTime.now().millisecondsSinceEpoch}';
    final booking = {
      'id': bookingId,
      'userId': _currentUserId,
      'items': cart['items'],
      'totalAmount': cart['totalAmount'],
      'discount': cart['discount'] ?? 0,
      'discountCode': cart['discountCode'],
      'tax': cart['tax'],
      'finalAmount': cart['finalAmount'],
      'status': 'pending',
      'paymentMethod': bookingDetails['paymentMethod'],
      'paymentStatus':
          bookingDetails['paymentMethod'] == 'COD' ? 'pending' : 'paid',
      'address': bookingDetails['address'],
      'scheduledDate': bookingDetails['scheduledDate'],
      'scheduledTime': bookingDetails['scheduledTime'],
      'notes': bookingDetails['notes'],
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };

    // Add to bookings database
    final bookings = List<Map<String, dynamic>>.from(_database['bookings']);
    bookings.add(booking);
    _database['bookings'] = bookings;

    // Clear the cart
    await clearCart();

    // Notify listeners
    final userBookings =
        bookings.where((b) => b['userId'] == _currentUserId).toList();
    _bookingsStreamController.add(userBookings);

    return {
      'status': 'success',
      'data': booking,
      'message': 'Booking created successfully',
    };
  }

  // Get user's bookings
  Future<Map<String, dynamic>> getBookings() async {
    _logApiCall('getBookings');
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final bookings = List<Map<String, dynamic>>.from(_database['bookings']);
    final userBookings =
        bookings.where((b) => b['userId'] == _currentUserId).toList();

    // Sort by creation date (newest first)
    userBookings.sort((a, b) => DateTime.parse(b['createdAt'])
        .compareTo(DateTime.parse(a['createdAt'])));

    return {
      'status': 'success',
      'data': userBookings,
    };
  }

  // Get booking details
  Future<Map<String, dynamic>> getBookingDetails(String bookingId) async {
    _logApiCall('getBookingDetails', params: {'bookingId': bookingId});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final bookings = List<Map<String, dynamic>>.from(_database['bookings']);
    final booking = bookings.firstWhere(
      (b) => b['id'] == bookingId && b['userId'] == _currentUserId,
      orElse: () => <String, dynamic>{},
    );

    if (booking.isEmpty) {
      return {
        'status': 'error',
        'message': 'Booking not found',
      };
    }

    return {
      'status': 'success',
      'data': booking,
    };
  }

  // Cancel booking
  Future<Map<String, dynamic>> cancelBooking(String bookingId) async {
    _logApiCall('cancelBooking', params: {'bookingId': bookingId});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final bookings = List<Map<String, dynamic>>.from(_database['bookings']);
    final bookingIndex = bookings.indexWhere(
        (b) => b['id'] == bookingId && b['userId'] == _currentUserId);

    if (bookingIndex == -1) {
      return {
        'status': 'error',
        'message': 'Booking not found',
      };
    }

    final booking = Map<String, dynamic>.from(bookings[bookingIndex]);

    // Check if booking can be cancelled
    if (booking['status'] != 'pending') {
      return {
        'status': 'error',
        'message': 'Only pending bookings can be cancelled',
      };
    }

    // Update booking status
    booking['status'] = 'cancelled';
    booking['updatedAt'] = DateTime.now().toIso8601String();

    // Save to database
    bookings[bookingIndex] = booking;
    _database['bookings'] = bookings;

    // Notify listeners
    final userBookings =
        bookings.where((b) => b['userId'] == _currentUserId).toList();
    _bookingsStreamController.add(userBookings);

    return {
      'status': 'success',
      'data': booking,
      'message': 'Booking cancelled successfully',
    };
  }

  // Get bookings with realtime updates
  Stream<Map<String, dynamic>> getBookingsStream() {
    return bookingsStream.map((bookings) => {
          'status': 'success',
          'data': bookings,
        });
  }

  //
  // PROFILE API METHODS
  //

  // Get user profile
  Future<Map<String, dynamic>> getProfile() async {
    _logApiCall('getProfile');
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final profiles = List<Map<String, dynamic>>.from(_database['profiles']);
    final profile = profiles.firstWhere(
      (p) => p['userId'] == _currentUserId,
      orElse: () => <String, dynamic>{},
    );

    if (profile.isEmpty) {
      return {
        'status': 'error',
        'message': 'Profile not found',
      };
    }

    return {
      'status': 'success',
      'data': profile,
    };
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile(
      Map<String, dynamic> profileData) async {
    _logApiCall('updateProfile', params: {'profileData': profileData});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final profiles = List<Map<String, dynamic>>.from(_database['profiles']);
    final profileIndex =
        profiles.indexWhere((p) => p['userId'] == _currentUserId);

    if (profileIndex == -1) {
      return {
        'status': 'error',
        'message': 'Profile not found',
      };
    }

    final profile = Map<String, dynamic>.from(profiles[profileIndex]);

    // Update profile fields
    if (profileData.containsKey('name')) {
      profile['name'] = profileData['name'];
    }
    if (profileData.containsKey('email')) {
      profile['email'] = profileData['email'];
    }
    if (profileData.containsKey('phone')) {
      profile['phone'] = profileData['phone'];
    }
    if (profileData.containsKey('preferences')) {
      profile['preferences'] =
          Map<String, dynamic>.from(profileData['preferences']);
    }

    // Save to database
    profiles[profileIndex] = profile;
    _database['profiles'] = profiles;

    // Notify listeners
    _profileStreamController.add(profile);

    return {
      'status': 'success',
      'data': profile,
      'message': 'Profile updated successfully',
    };
  }

  // Add address to profile
  Future<Map<String, dynamic>> addAddress(Map<String, dynamic> address) async {
    _logApiCall('addAddress', params: {'address': address});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final profiles = List<Map<String, dynamic>>.from(_database['profiles']);
    final profileIndex =
        profiles.indexWhere((p) => p['userId'] == _currentUserId);

    if (profileIndex == -1) {
      return {
        'status': 'error',
        'message': 'Profile not found',
      };
    }

    final profile = Map<String, dynamic>.from(profiles[profileIndex]);
    final addresses = List<Map<String, dynamic>>.from(profile['addresses']);

    // Generate address ID
    final addressId = 'addr-${DateTime.now().millisecondsSinceEpoch}';

    // Add new address
    final newAddress = Map<String, dynamic>.from(address);
    newAddress['id'] = addressId;

    // If this is the first address or marked as default, set as default
    if (addresses.isEmpty || newAddress['isDefault'] == true) {
      // Set all existing addresses to non-default
      for (final addr in addresses) {
        addr['isDefault'] = false;
      }
      newAddress['isDefault'] = true;
    }

    addresses.add(newAddress);
    profile['addresses'] = addresses;

    // Save to database
    profiles[profileIndex] = profile;
    _database['profiles'] = profiles;

    // Notify listeners
    _profileStreamController.add(profile);

    return {
      'status': 'success',
      'data': profile,
      'message': 'Address added successfully',
    };
  }

  // Get profile with realtime updates
  Stream<Map<String, dynamic>> getProfileStream() {
    return profileStream.map((profile) => {
          'status': 'success',
          'data': profile,
        });
  }

  //
  // CHAT SUPPORT API METHODS
  //

  // Get chat messages
  Future<Map<String, dynamic>> getChatMessages() async {
    _logApiCall('getChatMessages');
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final chatMessages =
        List<Map<String, dynamic>>.from(_database['chatMessages']);
    final userMessages = chatMessages
        .where((msg) =>
            msg['userId'] == _currentUserId ||
            msg['recipientId'] == _currentUserId)
        .toList();

    // Sort by timestamp
    userMessages.sort((a, b) => DateTime.parse(a['timestamp'])
        .compareTo(DateTime.parse(b['timestamp'])));

    return {
      'status': 'success',
      'data': {
        'messages': userMessages,
        'supportAgent': _supportAgent,
      },
    };
  }

  // Send chat message
  Future<Map<String, dynamic>> sendChatMessage(String message) async {
    _logApiCall('sendChatMessage', params: {'message': message});
    await _simulateNetworkDelay();

    if (_currentUserId == null) {
      return {
        'status': 'error',
        'message': 'User not authenticated',
      };
    }

    final chatMessages =
        List<Map<String, dynamic>>.from(_database['chatMessages']);

    // Create new message
    final newMessage = {
      'id': 'msg-${DateTime.now().millisecondsSinceEpoch}',
      'userId': _currentUserId,
      'recipientId': _supportAgent['id'],
      'content': message,
      'timestamp': DateTime.now().toIso8601String(),
      'isRead': false,
    };

    // Add to database
    chatMessages.add(newMessage);
    _database['chatMessages'] = chatMessages;

    // Notify listeners
    final userMessages = chatMessages
        .where((msg) =>
            msg['userId'] == _currentUserId ||
            msg['recipientId'] == _currentUserId)
        .toList();
    _chatMessagesStreamController.add(userMessages);

    return {
      'status': 'success',
      'data': newMessage,
      'message': 'Message sent successfully',
    };
  }

  // Get chat messages with realtime updates
  Stream<Map<String, dynamic>> getChatMessagesStream() {
    return chatMessagesStream.map((messages) => {
          'status': 'success',
          'data': {
            'messages': messages,
            'supportAgent': _supportAgent,
          },
        });
  }
}
