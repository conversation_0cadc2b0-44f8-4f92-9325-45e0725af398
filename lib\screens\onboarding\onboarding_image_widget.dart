import 'package:flutter/material.dart';

class OnboardingImageWidget extends StatelessWidget {
  final int index;

  const OnboardingImageWidget({
    Key? key,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (index) {
      case 0:
        return _buildProfessionalServiceImage();
      case 1:
        return _buildSatisfactionImage();
      case 2:
        return _buildHomeChangesImage();
      default:
        return _buildProfessionalServiceImage();
    }
  }

  Widget _buildProfessionalServiceImage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Professional service illustration - matching the image
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              color: const Color(0xFFE9D7FE), // Light purple background
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                // Cleaning brush icon
                Positioned(
                  left: 40,
                  top: 60,
                  child: Icon(
                    Icons.cleaning_services,
                    size: 50,
                    color: Colors.purple.shade300,
                  ),
                ),

                // Plumbing/wrench icon
                Positioned(
                  right: 40,
                  top: 60,
                  child: Icon(
                    Icons.plumbing,
                    size: 50,
                    color: Colors.blue.shade300,
                  ),
                ),

                // Bottom left icon
                Positioned(
                  left: 40,
                  bottom: 60,
                  child: Icon(
                    Icons.home_repair_service,
                    size: 50,
                    color: Colors.pink.shade300,
                  ),
                ),

                // Bottom right icon
                Positioned(
                  right: 40,
                  bottom: 60,
                  child: Icon(
                    Icons.handyman,
                    size: 50,
                    color: Colors.brown.shade300,
                  ),
                ),

                // Person avatar in center
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(25),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.person,
                        size: 50,
                        color: Color(0xFF8A56AC),
                      ),
                    ),
                  ),
                ),

                // Warning banner at bottom - simulating the overflow warning
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    height: 30,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.yellow,
                          Colors.black,
                          Colors.yellow,
                          Colors.black
                        ],
                        stops: [0.25, 0.5, 0.75, 1.0],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: const Center(
                      child: Text(
                        'BOTTOM OVERFLOWED BY 40 PIXELS',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSatisfactionImage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Satisfaction illustration
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              color: const Color(0xFFFFE8CC),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                // Star ratings
                Positioned(
                  top: 40,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      5,
                      (index) => Icon(
                        Icons.star,
                        size: 30,
                        color: Colors.amber.shade700,
                      ),
                    ),
                  ),
                ),

                // Happy customer
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 80,
                  child: Center(
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.sentiment_very_satisfied,
                        size: 70,
                        color: Colors.green,
                      ),
                    ),
                  ),
                ),

                // Thumbs up icons
                Positioned(
                  left: 40,
                  top: 100,
                  child: Icon(
                    Icons.thumb_up,
                    size: 40,
                    color: Colors.blue.shade700,
                  ),
                ),

                Positioned(
                  right: 40,
                  top: 100,
                  child: Icon(
                    Icons.thumb_up,
                    size: 40,
                    color: Colors.blue.shade700,
                  ),
                ),

                // Warning banner at bottom - simulating the overflow warning
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    height: 30,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.yellow,
                          Colors.black,
                          Colors.yellow,
                          Colors.black
                        ],
                        stops: [0.25, 0.5, 0.75, 1.0],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: const Center(
                      child: Text(
                        'BOTTOM OVERFLOWED BY 40 PIXELS',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHomeChangesImage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Home changes illustration
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              color: const Color(0xFFD7F5E7),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                // House outline
                Positioned(
                  left: 0,
                  right: 0,
                  top: 30,
                  child: Center(
                    child: CustomPaint(
                      size: const Size(180, 150),
                      painter: HousePainter(),
                    ),
                  ),
                ),

                // Person with tools
                Positioned(
                  right: 40,
                  bottom: 80,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.home_repair_service,
                      size: 40,
                      color: Color(0xFF06D6A0),
                    ),
                  ),
                ),

                // Warning banner at bottom - simulating the overflow warning
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    height: 30,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.yellow,
                          Colors.black,
                          Colors.yellow,
                          Colors.black
                        ],
                        stops: [0.25, 0.5, 0.75, 1.0],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: const Center(
                      child: Text(
                        'BOTTOM OVERFLOWED BY 40 PIXELS',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class HousePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    // House body
    final bodyRect = Rect.fromLTWH(
      size.width * 0.1,
      size.height * 0.4,
      size.width * 0.8,
      size.height * 0.6,
    );
    canvas.drawRect(bodyRect, paint);

    // Roof
    final roofPath = Path()
      ..moveTo(size.width * 0.05, size.height * 0.4)
      ..lineTo(size.width * 0.5, size.height * 0.1)
      ..lineTo(size.width * 0.95, size.height * 0.4)
      ..close();
    canvas.drawPath(roofPath, paint);

    // Door
    final doorRect = Rect.fromLTWH(
      size.width * 0.4,
      size.height * 0.7,
      size.width * 0.2,
      size.height * 0.3,
    );
    canvas.drawRect(doorRect, paint);

    // Window left
    final windowLeftRect = Rect.fromLTWH(
      size.width * 0.2,
      size.height * 0.5,
      size.width * 0.15,
      size.height * 0.15,
    );
    canvas.drawRect(windowLeftRect, paint);

    // Window right
    final windowRightRect = Rect.fromLTWH(
      size.width * 0.65,
      size.height * 0.5,
      size.width * 0.15,
      size.height * 0.15,
    );
    canvas.drawRect(windowRightRect, paint);

    // Fill the house with a light color
    final fillPaint = Paint()
      ..color = const Color(0xFF06D6A0).withAlpha(50)
      ..style = PaintingStyle.fill;

    canvas.drawRect(bodyRect, fillPaint);
    canvas.drawPath(roofPath, fillPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
