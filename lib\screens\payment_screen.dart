import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart_item.dart';
import '../providers/real_time_api_provider.dart';
import '../screens/main_navigation_screen.dart';
import 'order_confirmation_screen.dart';

class PaymentScreen extends StatefulWidget {
  final List<CartItem> cartItems;
  final Map<String, dynamic> address;
  final DateTime date;
  final String time;
  final double totalAmount;

  const PaymentScreen({
    super.key,
    required this.cartItems,
    required this.address,
    required this.date,
    required this.time,
    required this.totalAmount,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final List<Map<String, dynamic>> _paymentMethods = [
    {
      'id': '1',
      'type': 'Cash on Delivery',
      'description': 'Pay when service is completed',
      'icon': Icons.money,
      'isDefault': true,
      'isSelected': true,
    },
    {
      'id': '2',
      'type': 'Credit/Debit Card',
      'description': 'Pay with Visa, Mastercard, etc.',
      'icon': Icons.credit_card,
      'isDefault': false,
      'isSelected': false,
    },
    {
      'id': '3',
      'type': 'Google Pay',
      'description': 'Fast and secure payment',
      'icon': Icons.account_balance_wallet,
      'isDefault': false,
      'isSelected': false,
    },
    {
      'id': '4',
      'type': 'PhonePe',
      'description': 'UPI payment method',
      'icon': Icons.phone_android,
      'isDefault': false,
      'isSelected': false,
    },
    {
      'id': '5',
      'type': 'Paytm',
      'description': 'India\'s popular payment app',
      'icon': Icons.payment,
      'isDefault': false,
      'isSelected': false,
    },
  ];

  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _expiryDateController = TextEditingController();
  final TextEditingController _cvvController = TextEditingController();
  final TextEditingController _cardHolderNameController =
      TextEditingController();
  final TextEditingController _upiIdController = TextEditingController();

  bool _isAddingNewCard = false;
  bool _isShowingUpiInput = false;
  bool _isProcessing = false;
  String _selectedPaymentMethod = 'Cash on Delivery';

  // Get selected payment method
  String get selectedPaymentMethodType {
    final selectedMethod = _paymentMethods.firstWhere(
      (method) => method['isSelected'] == true,
      orElse: () => _paymentMethods.first,
    );
    return selectedMethod['type'] as String;
  }

  @override
  void initState() {
    super.initState();
    // Ensure user is authenticated when payment screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureAuthentication();
    });
  }

  Future<void> _ensureAuthentication() async {
    final apiProvider =
        Provider.of<RealTimeApiProvider>(context, listen: false);

    // Clear any previous errors
    apiProvider.clearError();

    // Check if user is authenticated, if not, authenticate with default user
    if (!apiProvider.isAuthenticated) {
      // Directly set the default user as authenticated
      apiProvider.setCurrentUser('user_default');
    }
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    _cardHolderNameController.dispose();
    _upiIdController.dispose();
    super.dispose();
  }

  void _selectPaymentMethod(String id) {
    final selectedMethod = _paymentMethods.firstWhere(
      (method) => method['id'] == id,
      orElse: () => _paymentMethods.first,
    );

    setState(() {
      // Reset all states
      _isAddingNewCard = false;
      _isShowingUpiInput = false;

      // Update selected method
      for (var method in _paymentMethods) {
        method['isSelected'] = method['id'] == id;
      }

      _selectedPaymentMethod = selectedMethod['type'] as String;

      // Show appropriate input form based on payment method
      if (_selectedPaymentMethod == 'Credit/Debit Card') {
        _isAddingNewCard = true;
      } else if (_selectedPaymentMethod == 'Google Pay' ||
          _selectedPaymentMethod == 'PhonePe' ||
          _selectedPaymentMethod == 'Paytm') {
        _isShowingUpiInput = true;
      }
    });
  }

  void _addNewCard() {
    if (_cardNumberController.text.isEmpty ||
        _expiryDateController.text.isEmpty ||
        _cvvController.text.isEmpty ||
        _cardHolderNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all card details'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final newCard = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'type': 'Credit Card',
      'cardNumber':
          '**** **** **** ${_cardNumberController.text.substring(_cardNumberController.text.length - 4)}',
      'expiryDate': _expiryDateController.text,
      'cardHolderName': _cardHolderNameController.text,
      'isDefault': false,
      'isSelected': true,
    };

    setState(() {
      for (var method in _paymentMethods) {
        method['isSelected'] = false;
      }
      _paymentMethods.add(newCard);
      _isAddingNewCard = false;
      _cardNumberController.clear();
      _expiryDateController.clear();
      _cvvController.clear();
      _cardHolderNameController.clear();
    });
  }

  // Tax is already included in the totalAmount from the cart page
  double get _subtotal => widget.totalAmount;
  double get _total => _subtotal; // No additional tax calculation

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: const Text(
          'Payment',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order summary
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Order Summary',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: const BoxDecoration(
                              color: Color(0xFFE0F7FA),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8)),
                            ),
                            child: const Icon(
                              Icons.shopping_cart,
                              size: 25,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Items: ${widget.cartItems.length}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF2D4059),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Total Quantity: ${widget.cartItems.fold(0, (sum, item) => sum + item.quantity)}',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF7D8CA3),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          Text(
                            '₹${widget.totalAmount.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2D4059),
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 24),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 20,
                            color: Color(0xFF7D8CA3),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.address['type'],
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF2D4059),
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  widget.address['address'],
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF7D8CA3),
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 24),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 20,
                            color: Color(0xFF7D8CA3),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${_getDayName(widget.date.weekday)}, ${widget.date.day} ${_getMonthName(widget.date.month)} ${widget.date.year} • ${widget.time}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF2D4059),
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Total',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2D4059),
                            ),
                          ),
                          Text(
                            '₹${_total.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Error message display
              Consumer<RealTimeApiProvider>(
                builder: (context, apiProvider, child) {
                  if (apiProvider.error != null) {
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.error_outline,
                              color: Colors.red, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              apiProvider.error!,
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Payment methods
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Payment Method',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _isAddingNewCard = !_isAddingNewCard;
                      });
                    },
                    icon: Icon(
                      _isAddingNewCard ? Icons.close : Icons.add,
                      color: Theme.of(context).primaryColor,
                      size: 18,
                    ),
                    label: Text(
                      _isAddingNewCard ? 'Cancel' : 'Add New',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Payment method list
              Column(
                children: _paymentMethods.map((method) {
                  final isSelected = method['isSelected'] as bool;
                  final icon = method['icon'] as IconData;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: InkWell(
                        onTap: () => _selectPaymentMethod(method['id']),
                        borderRadius: BorderRadius.circular(12),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            children: [
                              Radio(
                                value: true,
                                groupValue: isSelected,
                                onChanged: (_) =>
                                    _selectPaymentMethod(method['id']),
                                activeColor: Colors.black,
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: const BoxDecoration(
                                  color: Color(0xFFE0F7FA),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  icon,
                                  size: 20,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      method['type'],
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF2D4059),
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      method['description'],
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF7D8CA3),
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              if (method['isDefault'])
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFEEEEEE),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Text(
                                    'Default',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),

              // Credit/Debit Card Form
              if (_isAddingNewCard)
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Enter Card Details',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: _cardNumberController,
                          decoration: const InputDecoration(
                            hintText: 'Card Number',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _expiryDateController,
                                decoration: const InputDecoration(
                                  hintText: 'MM/YY',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 16),
                                ),
                                keyboardType: TextInputType.datetime,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextField(
                                controller: _cvvController,
                                decoration: const InputDecoration(
                                  hintText: 'CVV',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 16),
                                ),
                                keyboardType: TextInputType.number,
                                obscureText: true,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        TextField(
                          controller: _cardHolderNameController,
                          decoration: const InputDecoration(
                            hintText: 'Card Holder Name',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                          ),
                          textCapitalization: TextCapitalization.words,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Amount: ₹${_total.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2D4059),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: _addNewCard,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text(
                                'Save Card',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

              // UPI Payment Form
              if (_isShowingUpiInput)
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter $_selectedPaymentMethod Details',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: _upiIdController,
                          decoration: const InputDecoration(
                            hintText: 'Enter UPI ID (e.g., name@upi)',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                          ),
                          keyboardType: TextInputType.text,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Amount: ₹${_total.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2D4059),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                if (_upiIdController.text.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Please enter UPI ID'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  return;
                                }
                                setState(() {
                                  _isShowingUpiInput = false;
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text(
                                'Verify',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color(0xFFCFD8DC),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: _isProcessing
              ? null
              : () async {
                  // Store the selected payment method
                  final selectedMethod = _paymentMethods.firstWhere(
                    (method) => method['isSelected'] == true,
                    orElse: () => _paymentMethods.first,
                  )['type'] as String;

                  // Validate UPI ID if needed
                  if ((selectedMethod == 'Google Pay' ||
                          selectedMethod == 'PhonePe' ||
                          selectedMethod == 'Paytm') &&
                      _isShowingUpiInput &&
                      _upiIdController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter UPI ID'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  setState(() {
                    _isProcessing = true;
                  });

                  try {
                    // Get the API provider
                    final apiProvider = Provider.of<RealTimeApiProvider>(
                        context,
                        listen: false);

                    // Debug: Log current user info
                    print(
                        'PaymentScreen: Current user: ${apiProvider.currentUser}');
                    print(
                        'PaymentScreen: Is authenticated: ${apiProvider.isAuthenticated}');

                    // Clear any previous errors
                    apiProvider.clearError();

                    // Get the selected payment method
                    final paymentMethodName = _paymentMethods.firstWhere(
                      (method) => method['isSelected'] == true,
                      orElse: () => _paymentMethods.first,
                    )['type'] as String;

                    // Create booking data - format expected by RealTimeMockApiService
                    final firstItem = widget.cartItems.first;
                    final bookingData = {
                      'service': firstItem.service.name,
                      'category': firstItem.category.name,
                      'price': _total,
                      'date': widget.date.toIso8601String(),
                      'time': widget.time,
                      'address': widget.address,
                      'paymentMethod': paymentMethodName,
                      'icon': firstItem.category.icon.codePoint,
                      'color': firstItem.category.color.toARGB32(),
                    };

                    // Create the booking
                    final success =
                        await apiProvider.createBooking(bookingData);

                    if (!mounted) return;

                    setState(() {
                      _isProcessing = false;
                    });

                    if (success) {
                      // Generate order ID
                      final orderId =
                          'ORD-${DateTime.now().millisecondsSinceEpoch}';

                      // Navigate to order confirmation
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => OrderConfirmationScreen(
                            cartItems: widget.cartItems,
                            address: widget.address,
                            date: widget.date,
                            time: widget.time,
                            totalAmount: _total,
                            orderId: orderId,
                            paymentMethod: paymentMethodName,
                          ),
                        ),
                      );
                    } else {
                      // Show error message
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              apiProvider.error ?? 'Failed to create booking'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } catch (e) {
                    if (!mounted) return;

                    setState(() {
                      _isProcessing = false;
                    });

                    // Show error message
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error creating booking: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            disabledBackgroundColor: Colors.grey,
          ),
          child: _isProcessing
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  'Proceed to Payment',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
        ),
      ),
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }
}
