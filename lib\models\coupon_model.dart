class CouponModel {
  final String id;
  final String code;
  final String title;
  final String description;
  final double discountPercentage;
  final double maxDiscount;
  final double minOrderValue;
  final DateTime validUntil;
  final bool isActive;

  CouponModel({
    required this.id,
    required this.code,
    required this.title,
    required this.description,
    required this.discountPercentage,
    required this.maxDiscount,
    required this.minOrderValue,
    required this.validUntil,
    this.isActive = true,
  });

  factory CouponModel.fromJson(Map<String, dynamic> json) {
    return CouponModel(
      id: json['id'],
      code: json['code'],
      title: json['title'],
      description: json['description'],
      discountPercentage: json['discountPercentage'],
      maxDiscount: json['maxDiscount'],
      minOrderValue: json['minOrderValue'],
      validUntil: DateTime.parse(json['validUntil']),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'title': title,
      'description': description,
      'discountPercentage': discountPercentage,
      'maxDiscount': maxDiscount,
      'minOrderValue': minOrderValue,
      'validUntil': validUntil.toIso8601String(),
      'isActive': isActive,
    };
  }

  double calculateDiscount(double orderTotal) {
    if (orderTotal < minOrderValue || !isActive) {
      return 0;
    }

    final calculatedDiscount = orderTotal * (discountPercentage / 100);
    return calculatedDiscount > maxDiscount ? maxDiscount : calculatedDiscount;
  }

  bool isValid() {
    return isActive && validUntil.isAfter(DateTime.now());
  }
}
