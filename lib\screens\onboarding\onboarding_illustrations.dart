import 'package:flutter/material.dart';

class OnboardingIllustration extends StatelessWidget {
  final int index;
  final Color primaryColor;
  final Color secondaryColor;

  const OnboardingIllustration({
    Key? key,
    required this.index,
    this.primaryColor = const Color(0xFF8A56AC),
    this.secondaryColor = const Color(0xFFE9D7FE),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (index) {
      case 0:
        return _buildProfessionalServiceIllustration();
      case 1:
        return _buildSatisfactionIllustration();
      case 2:
        return _buildHomeChangesIllustration();
      default:
        return _buildProfessionalServiceIllustration();
    }
  }

  Widget _buildProfessionalServiceIllustration() {
    return CustomPaint(
      size: const Size(300, 300),
      painter: ProfessionalServicePainter(
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
      ),
    );
  }

  Widget _buildSatisfactionIllustration() {
    return CustomPaint(
      size: const Size(300, 300),
      painter: SatisfactionPainter(
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
      ),
    );
  }

  Widget _buildHomeChangesIllustration() {
    return CustomPaint(
      size: const Size(300, 300),
      painter: HomeChangesPainter(
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
      ),
    );
  }
}

class ProfessionalServicePainter extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;

  ProfessionalServicePainter({
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;

    // Draw background shape
    final backgroundPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(20, 40, size.width - 40, size.height - 80),
        const Radius.circular(20),
      ));
    canvas.drawPath(backgroundPath, paint);

    // Draw person
    final personPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    // Head
    canvas.drawCircle(
      Offset(size.width - 100, 120),
      30,
      personPaint,
    );

    // Body
    final bodyPath = Path()
      ..moveTo(size.width - 120, 150)
      ..lineTo(size.width - 80, 150)
      ..lineTo(size.width - 70, 250)
      ..lineTo(size.width - 130, 250)
      ..close();
    canvas.drawPath(bodyPath, personPaint);

    // Briefcase
    final briefcasePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(size.width - 160, 200, 40, 30),
      briefcasePaint,
    );

    // Document lines (representing tasks)
    final linePaint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 4; i++) {
      canvas.drawRect(
        Rect.fromLTWH(50, 80 + (i * 30), 100, 10),
        linePaint,
      );
    }

    // Trash bin
    final binPaint = Paint()
      ..color = primaryColor.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(80, 220, 40, 50),
      binPaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(70, 210, 60, 10),
      binPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class SatisfactionPainter extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;

  SatisfactionPainter({
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;

    // Draw background shape
    final backgroundPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(20, 40, size.width - 40, size.height - 80),
        const Radius.circular(20),
      ));
    canvas.drawPath(backgroundPath, paint);

    // Draw person
    final personPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    // Head
    canvas.drawCircle(
      Offset(size.width / 2, 120),
      30,
      personPaint,
    );

    // Body
    final bodyPath = Path()
      ..moveTo(size.width / 2 - 20, 150)
      ..lineTo(size.width / 2 + 20, 150)
      ..lineTo(size.width / 2 + 30, 250)
      ..lineTo(size.width / 2 - 30, 250)
      ..close();
    canvas.drawPath(bodyPath, personPaint);

    // Clipboard
    final clipboardPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(size.width / 2 - 80, 100, 60, 80),
      clipboardPaint,
    );

    // Clipboard border
    final clipboardBorderPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawRect(
      Rect.fromLTWH(size.width / 2 - 80, 100, 60, 80),
      clipboardBorderPaint,
    );

    // Clipboard lines
    final linePaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    for (int i = 0; i < 4; i++) {
      canvas.drawLine(
        Offset(size.width / 2 - 70, 120 + (i * 15)),
        Offset(size.width / 2 - 30, 120 + (i * 15)),
        linePaint,
      );
    }

    // Star rating
    final starPaint = Paint()
      ..color = Colors.amber
      ..style = PaintingStyle.fill;
    for (int i = 0; i < 5; i++) {
      canvas.drawCircle(
        Offset(size.width / 2 + 50 + (i * 20), 130),
        8,
        starPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class HomeChangesPainter extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;

  HomeChangesPainter({
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = secondaryColor
      ..style = PaintingStyle.fill;

    // Draw background shape
    final backgroundPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(20, 40, size.width - 40, size.height - 80),
        const Radius.circular(20),
      ));
    canvas.drawPath(backgroundPath, paint);

    // Draw house
    final housePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    // House body
    canvas.drawRect(
      Rect.fromLTWH(60, 120, 180, 120),
      housePaint,
    );

    // House roof
    final roofPath = Path()
      ..moveTo(50, 120)
      ..lineTo(150, 60)
      ..lineTo(250, 120)
      ..close();
    canvas.drawPath(roofPath, Paint()..color = primaryColor);

    // Door
    canvas.drawRect(
      Rect.fromLTWH(120, 180, 40, 60),
      Paint()..color = primaryColor.withOpacity(0.8),
    );

    // Windows
    canvas.drawRect(
      Rect.fromLTWH(80, 140, 30, 30),
      Paint()..color = Colors.lightBlue.shade100,
    );
    canvas.drawRect(
      Rect.fromLTWH(190, 140, 30, 30),
      Paint()..color = Colors.lightBlue.shade100,
    );

    // Person
    final personPaint = Paint()
      ..color = primaryColor
      ..style = PaintingStyle.fill;

    // Head
    canvas.drawCircle(
      Offset(size.width - 100, 200),
      20,
      personPaint,
    );

    // Body with raised arm
    final bodyPath = Path()
      ..moveTo(size.width - 100, 220)
      ..lineTo(size.width - 120, 270)
      ..lineTo(size.width - 80, 270)
      ..close();
    canvas.drawPath(bodyPath, personPaint);

    // Raised arm
    final armPath = Path()
      ..moveTo(size.width - 100, 230)
      ..lineTo(size.width - 70, 180)
      ..lineTo(size.width - 60, 190)
      ..lineTo(size.width - 90, 240)
      ..close();
    canvas.drawPath(armPath, personPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
