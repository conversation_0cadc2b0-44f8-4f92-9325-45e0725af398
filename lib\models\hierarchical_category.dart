import 'package:flutter/material.dart';

class HierarchicalCategory {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final String? imageUrl;
  final List<HierarchicalCategory> subcategories;
  final bool hasProducts;

  HierarchicalCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    this.imageUrl,
    this.subcategories = const [],
    this.hasProducts = false,
  });

  bool get hasSubcategories => subcategories.isNotEmpty;
}

// Sample data for hierarchical categories
class CategoryData {
  static List<HierarchicalCategory> getMainCategories() {
    return [
      HierarchicalCategory(
        id: '1',
        name: 'Cleaning',
        icon: Icons.cleaning_services,
        color: const Color(0xFF4ECDC4),
        imageUrl:
            'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '1-1',
            name: 'Furnished apartment',
            icon: Icons.apartment,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/modern-studio-apartment-design-with-bedroom-living-space_1262-12375.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '1-2',
            name: 'Unfurnished apartment',
            icon: Icons.apartment,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/empty-modern-room-with-furniture_23-2149178335.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '1-3',
            name: 'Book by room',
            icon: Icons.bed,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/luxury-bedroom-hotel_1150-10836.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '1-4',
            name: 'Furnished bungalow/duplex',
            icon: Icons.home,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/3d-rendering-loft-luxury-living-room-with-bookshelf_105762-2104.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '1-5',
            name: 'Unfurnished bungalow/duplex',
            icon: Icons.home,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/empty-room-with-chairs_1203-1384.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '1-6',
            name: 'Mini services',
            icon: Icons.cleaning_services,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/yellow-armchair-living-room-with-copy-space_43614-940.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '1-7',
            name: 'Commercial cleaning',
            icon: Icons.business,
            color: const Color(0xFF4ECDC4),
            imageUrl:
                'https://img.freepik.com/free-photo/cleaning-service-office_23-2149374132.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '2',
        name: 'Plumbing',
        icon: Icons.plumbing,
        color: const Color(0xFF6C63FF),
        imageUrl:
            'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-2149138754.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '2-1',
            name: 'Pipe Repairs',
            icon: Icons.plumbing,
            color: const Color(0xFF6C63FF),
            imageUrl:
                'https://img.freepik.com/free-photo/plumber-repairing-pipes_23-2149138767.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '2-2',
            name: 'Fixture Installation',
            icon: Icons.build,
            color: const Color(0xFF6C63FF),
            imageUrl:
                'https://img.freepik.com/free-photo/plumber-installing-sink-bathroom_23-2149138770.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '2-3',
            name: 'Leak Detection',
            icon: Icons.water_damage,
            color: const Color(0xFF6C63FF),
            imageUrl:
                'https://img.freepik.com/free-photo/plumber-checking-water-leak_23-2149138763.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '2-4',
            name: 'Bathroom Plumbing',
            icon: Icons.bathtub,
            color: const Color(0xFF6C63FF),
            imageUrl:
                'https://img.freepik.com/free-photo/plumber-fixing-bathroom-sink_23-2149138760.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '2-5',
            name: 'Kitchen Plumbing',
            icon: Icons.kitchen,
            color: const Color(0xFF6C63FF),
            imageUrl:
                'https://img.freepik.com/free-photo/plumber-fixing-kitchen-sink_23-2149138758.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '3',
        name: 'Electrical',
        icon: Icons.electrical_services,
        color: const Color(0xFFFF6B6B),
        imageUrl:
            'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-2147734355.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '3-1',
            name: 'Wiring',
            icon: Icons.cable,
            color: const Color(0xFFFF6B6B),
            imageUrl:
                'https://img.freepik.com/free-photo/electrician-working-with-wires_23-2147734359.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '3-2',
            name: 'Switch Installation',
            icon: Icons.power,
            color: const Color(0xFFFF6B6B),
            imageUrl:
                'https://img.freepik.com/free-photo/electrician-installing-light-switch_23-2147734362.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '3-3',
            name: 'Switch Replacement',
            icon: Icons.power,
            color: const Color(0xFFFF6B6B),
            imageUrl:
                'https://img.freepik.com/free-photo/electrician-replacing-light-switch_23-2147734365.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '3-4',
            name: 'Fan Installation',
            icon: Icons.air,
            color: const Color(0xFFFF6B6B),
            imageUrl:
                'https://img.freepik.com/free-photo/electrician-installing-ceiling-fan_23-2147734370.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '4',
        name: 'Carpenter',
        icon: Icons.handyman,
        color: const Color(0xFF8D6E63),
        imageUrl:
            'https://img.freepik.com/free-photo/carpenter-working-with-equipment-wooden-table-workshop_176474-7920.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '4-1',
            name: 'Furniture Assembly',
            icon: Icons.chair,
            color: const Color(0xFF8D6E63),
            imageUrl:
                'https://img.freepik.com/free-photo/carpenter-assembling-furniture_23-2149176073.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '4-2',
            name: 'Door Repair',
            icon: Icons.door_sliding,
            color: const Color(0xFF8D6E63),
            imageUrl:
                'https://img.freepik.com/free-photo/carpenter-fixing-door-hinge_23-2149176078.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '4-3',
            name: 'Custom Furniture',
            icon: Icons.weekend,
            color: const Color(0xFF8D6E63),
            imageUrl:
                'https://img.freepik.com/free-photo/carpenter-making-custom-furniture_23-2149176080.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '5',
        name: 'Appliances',
        icon: Icons.home_repair_service,
        color: const Color(0xFF118AB2),
        imageUrl:
            'https://img.freepik.com/free-photo/technician-repairing-refrigerator_23-2149176085.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '5-1',
            name: 'Refrigerator Repair',
            icon: Icons.kitchen,
            color: const Color(0xFF118AB2),
            imageUrl:
                'https://img.freepik.com/free-photo/technician-repairing-refrigerator_23-2149176085.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '5-2',
            name: 'Washing Machine Repair',
            icon: Icons.local_laundry_service,
            color: const Color(0xFF118AB2),
            imageUrl:
                'https://img.freepik.com/free-photo/technician-repairing-washing-machine_23-2149176090.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '5-3',
            name: 'Microwave Repair',
            icon: Icons.microwave,
            color: const Color(0xFF118AB2),
            imageUrl:
                'https://img.freepik.com/free-photo/technician-repairing-microwave_23-2149176095.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '6',
        name: 'Painting',
        icon: Icons.format_paint,
        color: const Color(0xFFFFD166),
        imageUrl:
            'https://img.freepik.com/free-photo/painter-painting-wall-with-roller_23-2147894089.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '6-1',
            name: 'Interior Painting',
            icon: Icons.format_paint,
            color: const Color(0xFFFFD166),
            imageUrl:
                'https://img.freepik.com/free-photo/painter-painting-interior-wall_23-2147894092.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '6-2',
            name: 'Exterior Painting',
            icon: Icons.home,
            color: const Color(0xFFFFD166),
            imageUrl:
                'https://img.freepik.com/free-photo/painter-painting-exterior-house_23-2147894095.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '6-3',
            name: 'Texture Painting',
            icon: Icons.texture,
            color: const Color(0xFFFFD166),
            imageUrl:
                'https://img.freepik.com/free-photo/painter-creating-texture-wall_23-2147894098.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '7',
        name: 'AC Services',
        icon: Icons.ac_unit,
        color: const Color(0xFF06D6A0),
        imageUrl:
            'https://img.freepik.com/free-photo/technician-servicing-air-conditioner_23-2149176100.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '7-1',
            name: 'AC Installation',
            icon: Icons.ac_unit,
            color: const Color(0xFF06D6A0),
            imageUrl:
                'https://img.freepik.com/free-photo/technician-installing-air-conditioner_23-2149176105.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '7-2',
            name: 'AC Repair',
            icon: Icons.build,
            color: const Color(0xFF06D6A0),
            imageUrl:
                'https://img.freepik.com/free-photo/technician-repairing-air-conditioner_23-2149176110.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '7-3',
            name: 'AC Maintenance',
            icon: Icons.cleaning_services,
            color: const Color(0xFF06D6A0),
            imageUrl:
                'https://img.freepik.com/free-photo/technician-cleaning-air-conditioner_23-2149176115.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '8',
        name: 'Pest Control',
        icon: Icons.pest_control,
        color: const Color(0xFFEF476F),
        imageUrl:
            'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149176120.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '8-1',
            name: 'General Pest Control',
            icon: Icons.pest_control,
            color: const Color(0xFFEF476F),
            imageUrl:
                'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149176120.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '8-2',
            name: 'Termite Control',
            icon: Icons.bug_report,
            color: const Color(0xFFEF476F),
            imageUrl:
                'https://img.freepik.com/free-photo/pest-control-worker-inspecting-termites_23-2149176125.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '8-3',
            name: 'Bed Bug Treatment',
            icon: Icons.bed,
            color: const Color(0xFFEF476F),
            imageUrl:
                'https://img.freepik.com/free-photo/pest-control-worker-treating-bed-bugs_23-2149176130.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '9',
        name: 'Car Wash',
        icon: Icons.car_repair,
        color: const Color(0xFF073B4C),
        imageUrl:
            'https://img.freepik.com/free-photo/worker-washing-car-car-wash_23-2149176135.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '9-1',
            name: 'Basic Car Wash',
            icon: Icons.local_car_wash,
            color: const Color(0xFF073B4C),
            imageUrl:
                'https://img.freepik.com/free-photo/worker-washing-car-car-wash_23-2149176135.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '9-2',
            name: 'Premium Car Wash',
            icon: Icons.car_repair,
            color: const Color(0xFF073B4C),
            imageUrl:
                'https://img.freepik.com/free-photo/worker-polishing-car-car-wash_23-2149176140.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '9-3',
            name: 'Interior Cleaning',
            icon: Icons.airline_seat_recline_normal,
            color: const Color(0xFF073B4C),
            imageUrl:
                'https://img.freepik.com/free-photo/worker-cleaning-car-interior_23-2149176145.jpg',
            hasProducts: true,
          ),
        ],
      ),
      HierarchicalCategory(
        id: '10',
        name: 'Packers & Movers',
        icon: Icons.local_shipping,
        color: const Color(0xFF9B5DE5),
        imageUrl:
            'https://img.freepik.com/free-photo/movers-carrying-boxes_23-2149176150.jpg',
        subcategories: [
          HierarchicalCategory(
            id: '10-1',
            name: 'Local Moving',
            icon: Icons.local_shipping,
            color: const Color(0xFF9B5DE5),
            imageUrl:
                'https://img.freepik.com/free-photo/movers-carrying-boxes_23-2149176150.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '10-2',
            name: 'Long Distance Moving',
            icon: Icons.moving,
            color: const Color(0xFF9B5DE5),
            imageUrl:
                'https://img.freepik.com/free-photo/moving-truck-with-boxes_23-2149176155.jpg',
            hasProducts: true,
          ),
          HierarchicalCategory(
            id: '10-3',
            name: 'Packing Services',
            icon: Icons.inventory_2,
            color: const Color(0xFF9B5DE5),
            imageUrl:
                'https://img.freepik.com/free-photo/people-packing-boxes_23-2149176160.jpg',
            hasProducts: true,
          ),
        ],
      ),
    ];
  }

  // Sample product data for the Furnished apartment category
  static List<Map<String, dynamic>> getFurnishedApartmentProducts() {
    return [
      {
        'id': 'fa-1',
        'name': '1 BHK furnished apartment cleaning',
        'description':
            'Professional cleaning service for 1 BHK furnished apartments. Our trained professionals will clean all rooms, kitchen, bathroom, and balcony.',
        'price': 3199,
        'rating': 4.8,
        'reviews': 13000,
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
        'includes': [
          '1 bedroom, 1 bathroom, 1 hall, 1 kitchen & 1 balcony',
          'Dusting and wiping of all surfaces',
          'Floor cleaning and mopping',
          'Bathroom deep cleaning',
          'Kitchen cleaning including cabinets',
          'Window cleaning from inside'
        ],
        'excludes': [
          'Excludes terrace cleaning & paint marks removal',
          'Wall cleaning is not included',
          'Cleaning of chandeliers and ceiling fans above 10 feet',
          'Furniture polishing is not included'
        ],
        'options': 3,
      },
      {
        'id': 'fa-2',
        'name': '2 BHK furnished apartment cleaning',
        'description':
            'Complete cleaning solution for 2 BHK furnished apartments. Our service includes deep cleaning of all rooms, kitchen, bathrooms, and balconies.',
        'price': 4299,
        'rating': 4.7,
        'reviews': 8500,
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-living-room_23-2148113512.jpg',
        'includes': [
          '2 bedrooms, 2 bathrooms, 1 hall, 1 kitchen & 2 balconies',
          'Dusting and wiping of all surfaces',
          'Floor cleaning and mopping',
          'Bathroom deep cleaning',
          'Kitchen cleaning including cabinets',
          'Window cleaning from inside',
          'Ceiling fan and light fixture cleaning'
        ],
        'excludes': [
          'Excludes terrace cleaning & paint marks removal',
          'Wall cleaning is not included',
          'Cleaning of chandeliers and ceiling fans above 10 feet',
          'Furniture polishing is not included'
        ],
        'options': 3,
      },
      {
        'id': 'fa-3',
        'name': '3 BHK furnished apartment cleaning',
        'description':
            'Premium cleaning service for 3 BHK furnished apartments. Our comprehensive service covers all rooms, kitchen, bathrooms, and balconies with attention to detail.',
        'price': 5499,
        'rating': 4.9,
        'reviews': 6200,
        'imageUrl':
            'https://img.freepik.com/free-photo/woman-cleaning-kitchen_23-2148113515.jpg',
        'includes': [
          '3 bedrooms, 2-3 bathrooms, 1 hall, 1 kitchen & 2 balconies',
          'Dusting and wiping of all surfaces',
          'Floor cleaning and mopping',
          'Bathroom deep cleaning',
          'Kitchen cleaning including cabinets',
          'Window cleaning from inside',
          'Ceiling fan and light fixture cleaning',
          'Sofa and carpet vacuuming'
        ],
        'excludes': [
          'Excludes terrace cleaning & paint marks removal',
          'Wall cleaning is not included',
          'Cleaning of chandeliers and ceiling fans above 10 feet',
          'Furniture polishing is not included',
          'Carpet shampooing is not included'
        ],
        'options': 3,
      },
    ];
  }

  // Sample product data for AC Services
  static List<Map<String, dynamic>> getACProducts() {
    return [
      {
        'id': 'ac-1',
        'name': 'AC Installation',
        'description':
            'Professional AC installation service for all types of air conditioners. Our technicians will install your AC unit with proper mounting, electrical connections, and testing.',
        'price': 1499,
        'rating': 4.7,
        'reviews': 5200,
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-installing-air-conditioner_23-2149176105.jpg',
        'includes': [
          'Installation of indoor and outdoor units',
          'Mounting brackets and hardware',
          'Basic electrical connections',
          'Pressure testing and leak detection',
          'System testing and demonstration'
        ],
        'excludes': [
          'Additional wiring beyond 3 meters',
          'Wall breaking or civil work',
          'Stabilizer installation',
          'Removal of old AC unit'
        ],
        'options': 2,
        'categoryId': '7-1',
      },
      {
        'id': 'ac-2',
        'name': 'AC Repair',
        'description':
            'Comprehensive AC repair service for all brands and models. Our expert technicians diagnose and fix issues with cooling, electrical problems, gas leakage, and more.',
        'price': 799,
        'rating': 4.8,
        'reviews': 7800,
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-repairing-air-conditioner_23-2149176110.jpg',
        'includes': [
          'Complete system diagnosis',
          'Repair of cooling issues',
          'Electrical fault detection and repair',
          'Gas leakage detection and repair',
          'Performance testing after repair'
        ],
        'excludes': [
          'Replacement parts (charged separately)',
          'Compressor replacement',
          'PCB board repair or replacement',
          'Warranty on parts not replaced'
        ],
        'options': 3,
        'categoryId': '7-2',
      },
      {
        'id': 'ac-3',
        'name': 'AC Maintenance',
        'description':
            'Regular AC maintenance service to keep your air conditioner running efficiently. Includes cleaning, filter replacement, and performance optimization.',
        'price': 599,
        'rating': 4.9,
        'reviews': 9500,
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-cleaning-air-conditioner_23-2149176115.jpg',
        'includes': [
          'Indoor and outdoor unit cleaning',
          'Filter cleaning or replacement',
          'Drain pipe cleaning',
          'Coil cleaning',
          'Performance check and optimization'
        ],
        'excludes': [
          'Replacement filters (charged separately)',
          'Gas refilling',
          'Repair of any existing issues',
          'Electrical wiring issues'
        ],
        'options': 2,
        'categoryId': '7-3',
      },
    ];
  }

  // Sample product data for Pest Control Services
  static List<Map<String, dynamic>> getPestControlProducts() {
    return [
      {
        'id': 'pc-1',
        'name': 'General Pest Control',
        'description':
            'Comprehensive pest control service to eliminate common household pests like cockroaches, ants, spiders, and silverfish. Safe for families and pets.',
        'price': 999,
        'rating': 4.6,
        'reviews': 4800,
        'imageUrl':
            'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149176120.jpg',
        'includes': [
          'Treatment for cockroaches, ants, and spiders',
          'Gel treatment for kitchen and bathrooms',
          'Spray treatment for living areas',
          'Safe, odorless chemicals',
          '30-day service guarantee'
        ],
        'excludes': [
          'Termite treatment',
          'Bed bug treatment',
          'Rodent control',
          'Outdoor garden pest control'
        ],
        'options': 2,
        'categoryId': '8-1',
      },
      {
        'id': 'pc-2',
        'name': 'Termite Control',
        'description':
            'Specialized termite control service to protect your home from destructive termites. Includes inspection, treatment, and preventive measures.',
        'price': 4999,
        'rating': 4.8,
        'reviews': 3200,
        'imageUrl':
            'https://img.freepik.com/free-photo/pest-control-worker-inspecting-termites_23-2149176125.jpg',
        'includes': [
          'Detailed termite inspection',
          'Chemical barrier treatment',
          'Wood treatment',
          'Preventive measures',
          '1-year warranty'
        ],
        'excludes': [
          'Structural repairs for termite damage',
          'Treatment for other pests',
          'Outdoor garden treatment',
          'Furniture treatment'
        ],
        'options': 3,
        'categoryId': '8-2',
      },
      {
        'id': 'pc-3',
        'name': 'Bed Bug Treatment',
        'description':
            'Effective bed bug elimination service using advanced techniques and safe chemicals. Complete treatment for bedrooms and living spaces.',
        'price': 2499,
        'rating': 4.7,
        'reviews': 2800,
        'imageUrl':
            'https://img.freepik.com/free-photo/pest-control-worker-treating-bed-bugs_23-2149176130.jpg',
        'includes': [
          'Thorough inspection of infested areas',
          'Chemical treatment for mattresses and furniture',
          'Spray treatment for cracks and crevices',
          'Follow-up treatment after 7 days',
          '30-day service guarantee'
        ],
        'excludes': [
          'Treatment for other pests',
          'Replacement of heavily infested items',
          'Cleaning services',
          'Outdoor treatment'
        ],
        'options': 2,
        'categoryId': '8-3',
      },
    ];
  }

  // Sample product data for Plumbing Services
  static List<Map<String, dynamic>> getPlumbingProducts() {
    return [
      {
        'id': 'pl-1',
        'name': 'Pipe Repair',
        'description':
            'Professional pipe repair service for leaking or damaged pipes. Our plumbers fix all types of pipe issues including leaks, bursts, and blockages.',
        'price': 799,
        'rating': 4.7,
        'reviews': 6300,
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-repairing-pipes_23-2149138767.jpg',
        'includes': [
          'Inspection and diagnosis',
          'Repair of leaking or damaged pipes',
          'Joint sealing',
          'Basic pipe replacement (up to 2 feet)',
          'Testing after repair'
        ],
        'excludes': [
          'Extensive pipe replacement (beyond 2 feet)',
          'Wall breaking or civil work',
          'Replacement of concealed pipes',
          'Drainage line repairs'
        ],
        'options': 3,
        'categoryId': '2-1',
      },
      {
        'id': 'pl-2',
        'name': 'Fixture Installation',
        'description':
            'Expert installation service for bathroom and kitchen fixtures including faucets, showers, sinks, and toilets. Professional fitting with leak-proof guarantee.',
        'price': 599,
        'rating': 4.8,
        'reviews': 5400,
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-installing-sink-bathroom_23-2149138770.jpg',
        'includes': [
          'Installation of faucets, showers, or sinks',
          'Toilet installation or replacement',
          'Connection to existing water lines',
          'Sealing and waterproofing',
          'Testing for leaks'
        ],
        'excludes': [
          'Cost of fixtures (customer to provide)',
          'Modification of existing plumbing lines',
          'Wall or floor tiling work',
          'Disposal of old fixtures'
        ],
        'options': 4,
        'categoryId': '2-2',
      },
      {
        'id': 'pl-3',
        'name': 'Leak Detection',
        'description':
            'Advanced leak detection service to identify hidden leaks in walls, floors, and ceilings. Non-invasive techniques to minimize damage.',
        'price': 1299,
        'rating': 4.9,
        'reviews': 3800,
        'imageUrl':
            'https://img.freepik.com/free-photo/plumber-checking-water-leak_23-2149138763.jpg',
        'includes': [
          'Comprehensive inspection of plumbing system',
          'Advanced leak detection equipment',
          'Thermal imaging (where applicable)',
          'Moisture meter testing',
          'Detailed report of findings'
        ],
        'excludes': [
          'Repair of detected leaks (quoted separately)',
          'Wall opening or breaking',
          'Repair of water damage',
          'Replacement of pipes'
        ],
        'options': 2,
        'categoryId': '2-3',
      },
    ];
  }

  // Sample product data for Electrical Services
  static List<Map<String, dynamic>> getElectricalProducts() {
    return [
      {
        'id': 'el-1',
        'name': 'Wiring Installation',
        'description':
            'Professional electrical wiring installation service for homes and offices. Safe and code-compliant installation with quality materials.',
        'price': 2499,
        'rating': 4.8,
        'reviews': 4200,
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-working-with-wires_23-2147734359.jpg',
        'includes': [
          'New wiring installation',
          'Circuit design and planning',
          'Conduit installation',
          'Connection to distribution board',
          'Testing and certification'
        ],
        'excludes': [
          'Wall breaking or civil work',
          'Electrical fixtures (lights, fans, etc.)',
          'Upgrading main electrical panel',
          'Permits and inspections (if required)'
        ],
        'options': 3,
        'categoryId': '3-1',
      },
      {
        'id': 'el-2',
        'name': 'Switch Installation',
        'description':
            'Expert installation of switches, sockets, and electrical outlets. Upgrade your electrical points with safe and reliable installation.',
        'price': 399,
        'rating': 4.7,
        'reviews': 7800,
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-installing-light-switch_23-2147734362.jpg',
        'includes': [
          'Installation of switches or sockets',
          'Connection to existing wiring',
          'Testing for proper function',
          'Basic troubleshooting',
          'Safety check'
        ],
        'excludes': [
          'Cost of switches/sockets (customer to provide)',
          'New wiring installation',
          'Wall cutting or modifications',
          'Repair of existing electrical issues'
        ],
        'options': 5,
        'categoryId': '3-2',
      },
      {
        'id': 'el-3',
        'name': 'Fan Installation',
        'description':
            'Professional ceiling fan installation service. Our electricians ensure secure mounting, proper wiring, and balanced operation.',
        'price': 599,
        'rating': 4.6,
        'reviews': 5600,
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-installing-ceiling-fan_23-2147734370.jpg',
        'includes': [
          'Ceiling fan installation',
          'Assembly of fan parts',
          'Connection to existing wiring',
          'Installation of regulator',
          'Balancing and testing'
        ],
        'excludes': [
          'Cost of fan (customer to provide)',
          'New wiring installation',
          'Reinforcement of ceiling mount',
          'Removal of old fan'
        ],
        'options': 2,
        'categoryId': '3-4',
      },
      {
        'id': 'el-4',
        'name': 'Switch Replacement',
        'description':
            'Professional replacement of old or damaged switches and sockets. Upgrade to modern switches with safe installation.',
        'price': 349,
        'rating': 4.8,
        'reviews': 4200,
        'imageUrl':
            'https://img.freepik.com/free-photo/electrician-replacing-light-switch_23-2147734365.jpg',
        'includes': [
          'Removal of old switch/socket',
          'Installation of new switch/socket',
          'Connection to existing wiring',
          'Testing for proper function',
          'Safety check'
        ],
        'excludes': [
          'Cost of switches/sockets (customer to provide)',
          'New wiring installation',
          'Wall repairs',
          'Repair of existing electrical issues'
        ],
        'options': 4,
        'categoryId': '3-3',
      },
    ];
  }

  // Sample product data for Carpenter Services
  static List<Map<String, dynamic>> getCarpenterProducts() {
    return [
      {
        'id': 'cp-1',
        'name': 'Furniture Assembly',
        'description':
            'Professional furniture assembly service for all types of flat-pack furniture. Our skilled carpenters ensure proper assembly and stability.',
        'price': 799,
        'rating': 4.7,
        'reviews': 5800,
        'imageUrl':
            'https://img.freepik.com/free-photo/carpenter-assembling-furniture_23-2149176073.jpg',
        'includes': [
          'Assembly of flat-pack furniture',
          'Installation of hardware and fittings',
          'Proper alignment and leveling',
          'Stability testing',
          'Cleanup of packaging materials'
        ],
        'excludes': [
          'Cost of furniture (customer to provide)',
          'Modification of furniture pieces',
          'Wall mounting (quoted separately)',
          'Repair of damaged furniture parts'
        ],
        'options': 3,
        'categoryId': '4-1',
      },
      {
        'id': 'cp-2',
        'name': 'Door Repair',
        'description':
            'Expert door repair service for wooden, glass, and composite doors. Fix issues with hinges, handles, alignment, and more.',
        'price': 599,
        'rating': 4.8,
        'reviews': 4200,
        'imageUrl':
            'https://img.freepik.com/free-photo/carpenter-fixing-door-hinge_23-2149176078.jpg',
        'includes': [
          'Inspection and diagnosis',
          'Hinge repair or replacement',
          'Handle and lock repair',
          'Door alignment adjustment',
          'Basic wood repairs'
        ],
        'excludes': [
          'Cost of new door (if replacement needed)',
          'Major structural repairs',
          'Glass replacement',
          'Painting or finishing'
        ],
        'options': 4,
        'categoryId': '4-2',
      },
      {
        'id': 'cp-3',
        'name': 'Custom Furniture',
        'description':
            'Custom furniture design and creation service. Our master carpenters craft bespoke furniture pieces tailored to your specifications.',
        'price': 4999,
        'rating': 4.9,
        'reviews': 3200,
        'imageUrl':
            'https://img.freepik.com/free-photo/carpenter-making-custom-furniture_23-2149176080.jpg',
        'includes': [
          'Design consultation',
          'Material selection assistance',
          'Custom fabrication',
          'Assembly and installation',
          'Finishing and polishing'
        ],
        'excludes': [
          'Cost of premium materials (quoted separately)',
          'Structural modifications to home',
          'Removal of existing furniture',
          'Extensive design changes after production begins'
        ],
        'options': 5,
        'categoryId': '4-3',
      },
    ];
  }

  // Sample product data for Appliance Repair Services
  static List<Map<String, dynamic>> getApplianceProducts() {
    return [
      {
        'id': 'ap-1',
        'name': 'Refrigerator Repair',
        'description':
            'Professional refrigerator repair service for all brands and models. Fix cooling issues, leaks, noise problems, and more.',
        'price': 899,
        'rating': 4.7,
        'reviews': 6200,
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-repairing-refrigerator_23-2149176085.jpg',
        'includes': [
          'Diagnostic inspection',
          'Repair of common issues',
          'Replacement of basic parts',
          'Performance testing',
          '30-day service guarantee'
        ],
        'excludes': [
          'Cost of major replacement parts',
          'Gas refilling (charged separately)',
          'Compressor replacement',
          'Cosmetic repairs'
        ],
        'options': 3,
        'categoryId': '5-1',
      },
      {
        'id': 'ap-2',
        'name': 'Washing Machine Repair',
        'description':
            'Expert washing machine repair service for all types and brands. Fix leaks, drainage issues, motor problems, and more.',
        'price': 799,
        'rating': 4.8,
        'reviews': 5400,
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-repairing-washing-machine_23-2149176090.jpg',
        'includes': [
          'Comprehensive diagnosis',
          'Repair of mechanical issues',
          'Electrical system checks',
          'Replacement of basic parts',
          'Performance testing'
        ],
        'excludes': [
          'Cost of major replacement parts',
          'Motor replacement',
          'Drum replacement',
          'Cosmetic repairs'
        ],
        'options': 4,
        'categoryId': '5-2',
      },
      {
        'id': 'ap-3',
        'name': 'Microwave Repair',
        'description':
            'Professional microwave oven repair service. Our technicians fix heating issues, turntable problems, control panel malfunctions, and more.',
        'price': 599,
        'rating': 4.6,
        'reviews': 3800,
        'imageUrl':
            'https://img.freepik.com/free-photo/technician-repairing-microwave_23-2149176095.jpg',
        'includes': [
          'Safety inspection',
          'Diagnosis of issues',
          'Repair of common problems',
          'Replacement of basic parts',
          'Performance and safety testing'
        ],
        'excludes': [
          'Cost of major replacement parts',
          'Magnetron replacement',
          'Control board replacement',
          'Cosmetic repairs'
        ],
        'options': 2,
        'categoryId': '5-3',
      },
    ];
  }
}
