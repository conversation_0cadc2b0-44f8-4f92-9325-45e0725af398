{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\DodoBooker\\android\\app\\.cxx\\Debug\\3x5i2n1x\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\DodoBooker\\android\\app\\.cxx\\Debug\\3x5i2n1x\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}