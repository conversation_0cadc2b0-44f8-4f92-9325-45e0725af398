import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../config/supabase_config.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  late final SupabaseClient _client;

  // Getter for the Supabase client
  SupabaseClient get client => _client;

  // Initialize Supabase
  Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
    _client = Supabase.instance.client;

    // Google Sign-In will be handled via Supabase OAuth for all platforms
  }

  // Check if Supabase is properly configured
  bool get isConfigured {
    return SupabaseConfig.supabaseUrl != 'YOUR_SUPABASE_URL_HERE' &&
        SupabaseConfig.supabaseAnonKey != 'YOUR_SUPABASE_ANON_KEY_HERE';
  }

  // Test Supabase connection
  Future<Map<String, dynamic>> testConnection() async {
    try {
      print('Testing Supabase connection...');
      print('URL: ${SupabaseConfig.supabaseUrl}');
      print('Configured: $isConfigured');

      // Try a simple query to test connection
      final response = await _client
          .from(SupabaseConfig.categoriesTable)
          .select('count')
          .limit(1);

      print('Supabase connection test successful');
      return {
        'status': 'success',
        'message': 'Connected to Supabase successfully',
        'configured': isConfigured,
        'url': SupabaseConfig.supabaseUrl,
      };
    } catch (e) {
      print('Supabase connection test failed: $e');
      return {
        'status': 'error',
        'message': 'Failed to connect to Supabase: $e',
        'configured': isConfigured,
        'url': SupabaseConfig.supabaseUrl,
      };
    }
  }

  // Authentication methods
  Future<AuthResponse> signUp(String email, String password) async {
    return await _client.auth.signUp(
      email: email,
      password: password,
    );
  }

  Future<AuthResponse> signIn(String email, String password) async {
    return await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  User? get currentUser => _client.auth.currentUser;

  // Categories methods
  Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      final response = await _client
          .from(SupabaseConfig.categoriesTable)
          .select('*')
          .eq('is_active', true)
          .order('display_order');

      final categories = List<Map<String, dynamic>>.from(response);
      print('Supabase: Loaded ${categories.length} categories from database');

      // If no categories found, create sample data
      if (categories.isEmpty) {
        print('Supabase: No categories found, creating sample data...');
        await _createSampleCategories();

        // Try again after creating sample data
        final retryResponse = await _client
            .from(SupabaseConfig.categoriesTable)
            .select('*')
            .eq('is_active', true)
            .order('display_order');

        return List<Map<String, dynamic>>.from(retryResponse);
      }

      return categories;
    } catch (e) {
      print('Supabase: Error loading categories: $e');
      // Return empty list instead of throwing error
      return [];
    }
  }

  // Create sample categories if none exist
  Future<void> _createSampleCategories() async {
    try {
      final sampleCategories = [
        {
          'id': 'cleaning',
          'name': 'Cleaning',
          'description': 'Professional cleaning services for your home',
          'image_url':
              'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-2148113509.jpg',
          'icon_code_point': 58826, // Icons.cleaning_services
          'color_hex': '#4ECDC4',
          'is_active': true,
          'display_order': 1,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 'plumbing',
          'name': 'Plumbing',
          'description': 'Expert plumbing services and repairs',
          'image_url':
              'https://img.freepik.com/free-photo/plumber-fixing-pipe_23-2148113509.jpg',
          'icon_code_point': 58827, // Icons.plumbing
          'color_hex': '#FF6B6B',
          'is_active': true,
          'display_order': 2,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 'electrical',
          'name': 'Electrical',
          'description': 'Safe and reliable electrical services',
          'image_url':
              'https://img.freepik.com/free-photo/electrician-working_23-2148113509.jpg',
          'icon_code_point': 58828, // Icons.electrical_services
          'color_hex': '#FFD93D',
          'is_active': true,
          'display_order': 3,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
      ];

      await _client
          .from(SupabaseConfig.categoriesTable)
          .insert(sampleCategories);
      print('Supabase: Sample categories created successfully');
    } catch (e) {
      print('Supabase: Error creating sample categories: $e');
    }
  }

  Future<Map<String, dynamic>> createCategory(
      Map<String, dynamic> categoryData) async {
    final response =
        await _client.from('categories').insert(categoryData).select().single();
    return response;
  }

  Future<Map<String, dynamic>> updateCategory(
      String id, Map<String, dynamic> categoryData) async {
    final response = await _client
        .from('categories')
        .update(categoryData)
        .eq('id', id)
        .select()
        .single();
    return response;
  }

  Future<void> deleteCategory(String id) async {
    await _client.from('categories').update({'is_active': false}).eq('id', id);
  }

  // Subcategories methods
  Future<List<Map<String, dynamic>>> getSubcategories(String categoryId) async {
    try {
      final response = await _client
          .from('subcategories')
          .select('*')
          .eq('category_id', categoryId)
          .eq('is_active', true)
          .order('display_order');

      final subcategories = List<Map<String, dynamic>>.from(response);
      print(
          'Supabase: Loaded ${subcategories.length} subcategories for category $categoryId');
      return subcategories;
    } catch (e) {
      print('Supabase: Error loading subcategories: $e');
      return [];
    }
  }

  // Get all subcategories
  Future<List<Map<String, dynamic>>> getAllSubcategories() async {
    try {
      final response = await _client
          .from('subcategories')
          .select('*')
          .eq('is_active', true)
          .order('category_id, display_order');

      final subcategories = List<Map<String, dynamic>>.from(response);
      print('Supabase: Loaded ${subcategories.length} total subcategories');
      return subcategories;
    } catch (e) {
      print('Supabase: Error loading all subcategories: $e');
      return [];
    }
  }

  // Sub-subcategories methods
  Future<List<Map<String, dynamic>>> getSubSubcategories(
      String subcategoryId) async {
    try {
      final response = await _client
          .from('sub_subcategories')
          .select('*')
          .eq('subcategory_id', subcategoryId)
          .eq('is_active', true)
          .order('display_order');

      final subSubcategories = List<Map<String, dynamic>>.from(response);
      print(
          'Supabase: Loaded ${subSubcategories.length} sub-subcategories for subcategory $subcategoryId');
      return subSubcategories;
    } catch (e) {
      print('Supabase: Error loading sub-subcategories: $e');
      return [];
    }
  }

  // Get all sub-subcategories
  Future<List<Map<String, dynamic>>> getAllSubSubcategories() async {
    try {
      final response = await _client
          .from('sub_subcategories')
          .select('*')
          .eq('is_active', true)
          .order('subcategory_id, display_order');

      final subSubcategories = List<Map<String, dynamic>>.from(response);
      print(
          'Supabase: Loaded ${subSubcategories.length} total sub-subcategories');
      return subSubcategories;
    } catch (e) {
      print('Supabase: Error loading all sub-subcategories: $e');
      return [];
    }
  }

  // Products methods
  Future<List<Map<String, dynamic>>> getProducts(
      {String? categoryId,
      String? subcategoryId,
      String? subSubcategoryId}) async {
    try {
      var query = _client.from('products').select('*').eq('is_active', true);

      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      if (subcategoryId != null) {
        query = query.eq('subcategory_id', subcategoryId);
      }

      if (subSubcategoryId != null) {
        query = query.eq('sub_subcategory_id', subSubcategoryId);
      }

      final response = await query.order('display_order');
      final products = List<Map<String, dynamic>>.from(response);

      print(
          'Supabase: Loaded ${products.length} products (category: $categoryId, subcategory: $subcategoryId, sub-subcategory: $subSubcategoryId)');
      return products;
    } catch (e) {
      print('Supabase: Error loading products: $e');
      return [];
    }
  }

  // Get products with category and subcategory details
  Future<List<Map<String, dynamic>>> getProductsWithDetails(
      {String? categoryId, String? subcategoryId}) async {
    try {
      var query = _client.from('products').select('''
            *,
            categories:category_id(id, name, description),
            subcategories:subcategory_id(id, name, description)
          ''').eq('is_active', true);

      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      if (subcategoryId != null) {
        query = query.eq('subcategory_id', subcategoryId);
      }

      final response = await query.order('display_order');
      final products = List<Map<String, dynamic>>.from(response);

      print('Supabase: Loaded ${products.length} products with details');
      return products;
    } catch (e) {
      print('Supabase: Error loading products with details: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> getProduct(String productId) async {
    final response =
        await _client.from('products').select('*').eq('id', productId).single();
    return response;
  }

  // Cart methods
  Future<List<Map<String, dynamic>>> getCartItems(String userId) async {
    final response = await _client.from('cart_items').select('''
          *,
          products (
            id,
            name,
            price,
            image_url,
            description
          )
        ''').eq('user_id', userId);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> addToCart(
      String userId, String productId, int quantity) async {
    // Check if item already exists in cart
    final existingItems = await _client
        .from('cart_items')
        .select('*')
        .eq('user_id', userId)
        .eq('product_id', productId);

    if (existingItems.isNotEmpty) {
      // Update existing item
      final response = await _client
          .from('cart_items')
          .update({
            'quantity': quantity,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('user_id', userId)
          .eq('product_id', productId)
          .select()
          .single();
      return response;
    } else {
      // Add new item
      final response = await _client
          .from('cart_items')
          .insert({
            'user_id': userId,
            'product_id': productId,
            'quantity': quantity,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();
      return response;
    }
  }

  Future<void> removeFromCart(String userId, String productId) async {
    await _client
        .from('cart_items')
        .delete()
        .eq('user_id', userId)
        .eq('product_id', productId);
  }

  Future<void> clearCart(String userId) async {
    await _client.from('cart_items').delete().eq('user_id', userId);
  }

  // Bookings methods
  Future<List<Map<String, dynamic>>> getBookings(String userId) async {
    final response = await _client.from('bookings').select('''
          *,
          booking_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          )
        ''').eq('user_id', userId).order('created_at', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> createBooking(
      Map<String, dynamic> bookingData) async {
    final response =
        await _client.from('bookings').insert(bookingData).select().single();
    return response;
  }

  // User profile methods
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      final response = await _client
          .from('user_profiles')
          .select('*')
          .eq('id', userId)
          .single();
      return response;
    } catch (e) {
      return null;
    }
  }

  // Get user by email from auth.users table
  Future<Map<String, dynamic>?> getUserByEmail(String email) async {
    try {
      // Try to get user profile first (simpler approach)
      final profileResponse = await _client
          .from('user_profiles')
          .select('*')
          .eq('email', email)
          .maybeSingle();

      if (profileResponse != null) {
        return {
          'id': profileResponse['id'],
          'email': profileResponse['email'],
          'raw_user_meta_data': {'name': profileResponse['name']},
          'email_confirmed_at': DateTime.now().toIso8601String(),
        };
      }

      return null;
    } catch (e) {
      print('Error getting user by email: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>> updateUserProfile(
      String userId, Map<String, dynamic> profileData) async {
    try {
      print('Creating/updating user profile for user: $userId');
      print('Profile data: $profileData');

      // Ensure we have all required fields
      final completeProfileData = {
        'id': userId,
        'name': profileData['name'] ?? 'User',
        'email': profileData['email'] ?? '',
        'phone': profileData['phone'] ?? '',
        'profile_pic': profileData['profile_pic'] ?? '',
        'is_profile_complete': profileData['is_profile_complete'] ?? false,
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Complete profile data: $completeProfileData');

      final response = await _client
          .from('user_profiles')
          .upsert(completeProfileData)
          .select()
          .single();

      print('Profile created successfully: $response');
      return response;
    } catch (e) {
      print('Error creating user profile: $e');
      print('Error type: ${e.runtimeType}');

      // Try to get more specific error information
      if (e.toString().contains('relation "user_profiles" does not exist')) {
        print('ERROR: user_profiles table does not exist in database!');
        print('Please run the SQL commands to create the table.');
      }

      rethrow;
    }
  }

  // Addresses methods
  Future<List<Map<String, dynamic>>> getUserAddresses(String userId) async {
    final response = await _client
        .from('user_addresses')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_default', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> addUserAddress(
      Map<String, dynamic> addressData) async {
    final response = await _client
        .from('user_addresses')
        .insert(addressData)
        .select()
        .single();
    return response;
  }

  // Coupons methods
  Future<List<Map<String, dynamic>>> getActiveCoupons() async {
    final now = DateTime.now().toIso8601String();
    final response = await _client
        .from('coupons')
        .select('*')
        .eq('is_active', true)
        .lte('valid_from', now)
        .gte('valid_until', now);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>?> getCouponByCode(String code) async {
    try {
      final response = await _client
          .from('coupons')
          .select('*')
          .eq('code', code)
          .eq('is_active', true)
          .single();
      return response;
    } catch (e) {
      return null;
    }
  }
}
