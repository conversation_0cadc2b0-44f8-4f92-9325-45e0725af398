import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../config/supabase_config.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  late final SupabaseClient _client;

  // Getter for the Supabase client
  SupabaseClient get client => _client;

  // Initialize Supabase
  Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
    _client = Supabase.instance.client;

    // Google Sign-In will be handled via Supabase OAuth for all platforms
  }

  // Check if Supabase is properly configured
  bool get isConfigured {
    return SupabaseConfig.supabaseUrl != 'YOUR_SUPABASE_URL_HERE' &&
        SupabaseConfig.supabaseAnonKey != 'YOUR_SUPABASE_ANON_KEY_HERE';
  }

  // Authentication methods
  Future<AuthResponse> signUp(String email, String password) async {
    return await _client.auth.signUp(
      email: email,
      password: password,
    );
  }

  Future<AuthResponse> signIn(String email, String password) async {
    return await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  User? get currentUser => _client.auth.currentUser;

  // Categories methods
  Future<List<Map<String, dynamic>>> getCategories() async {
    final response = await _client
        .from(SupabaseConfig.categoriesTable)
        .select('*')
        .eq('is_active', true)
        .order('display_order');
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> createCategory(
      Map<String, dynamic> categoryData) async {
    final response =
        await _client.from('categories').insert(categoryData).select().single();
    return response;
  }

  Future<Map<String, dynamic>> updateCategory(
      String id, Map<String, dynamic> categoryData) async {
    final response = await _client
        .from('categories')
        .update(categoryData)
        .eq('id', id)
        .select()
        .single();
    return response;
  }

  Future<void> deleteCategory(String id) async {
    await _client.from('categories').update({'is_active': false}).eq('id', id);
  }

  // Subcategories methods
  Future<List<Map<String, dynamic>>> getSubcategories(String categoryId) async {
    final response = await _client
        .from('subcategories')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('display_order');
    return List<Map<String, dynamic>>.from(response);
  }

  // Products methods
  Future<List<Map<String, dynamic>>> getProducts(
      {String? categoryId, String? subcategoryId}) async {
    var query = _client.from('products').select('*').eq('is_active', true);

    if (categoryId != null) {
      query = query.eq('category_id', categoryId);
    }

    if (subcategoryId != null) {
      query = query.eq('subcategory_id', subcategoryId);
    }

    final response = await query.order('display_order');
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> getProduct(String productId) async {
    final response =
        await _client.from('products').select('*').eq('id', productId).single();
    return response;
  }

  // Cart methods
  Future<List<Map<String, dynamic>>> getCartItems(String userId) async {
    final response = await _client.from('cart_items').select('''
          *,
          products (
            id,
            name,
            price,
            image_url,
            description
          )
        ''').eq('user_id', userId);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> addToCart(
      String userId, String productId, int quantity) async {
    // Check if item already exists in cart
    final existingItems = await _client
        .from('cart_items')
        .select('*')
        .eq('user_id', userId)
        .eq('product_id', productId);

    if (existingItems.isNotEmpty) {
      // Update existing item
      final response = await _client
          .from('cart_items')
          .update({
            'quantity': quantity,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('user_id', userId)
          .eq('product_id', productId)
          .select()
          .single();
      return response;
    } else {
      // Add new item
      final response = await _client
          .from('cart_items')
          .insert({
            'user_id': userId,
            'product_id': productId,
            'quantity': quantity,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();
      return response;
    }
  }

  Future<void> removeFromCart(String userId, String productId) async {
    await _client
        .from('cart_items')
        .delete()
        .eq('user_id', userId)
        .eq('product_id', productId);
  }

  Future<void> clearCart(String userId) async {
    await _client.from('cart_items').delete().eq('user_id', userId);
  }

  // Bookings methods
  Future<List<Map<String, dynamic>>> getBookings(String userId) async {
    final response = await _client.from('bookings').select('''
          *,
          booking_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          )
        ''').eq('user_id', userId).order('created_at', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> createBooking(
      Map<String, dynamic> bookingData) async {
    final response =
        await _client.from('bookings').insert(bookingData).select().single();
    return response;
  }

  // User profile methods
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      final response = await _client
          .from('user_profiles')
          .select('*')
          .eq('id', userId)
          .single();
      return response;
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>> updateUserProfile(
      String userId, Map<String, dynamic> profileData) async {
    final response = await _client
        .from('user_profiles')
        .upsert({
          'id': userId,
          ...profileData,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .select()
        .single();
    return response;
  }

  // Addresses methods
  Future<List<Map<String, dynamic>>> getUserAddresses(String userId) async {
    final response = await _client
        .from('user_addresses')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_default', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> addUserAddress(
      Map<String, dynamic> addressData) async {
    final response = await _client
        .from('user_addresses')
        .insert(addressData)
        .select()
        .single();
    return response;
  }

  // Coupons methods
  Future<List<Map<String, dynamic>>> getActiveCoupons() async {
    final now = DateTime.now().toIso8601String();
    final response = await _client
        .from('coupons')
        .select('*')
        .eq('is_active', true)
        .lte('valid_from', now)
        .gte('valid_until', now);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>?> getCouponByCode(String code) async {
    try {
      final response = await _client
          .from('coupons')
          .select('*')
          .eq('code', code)
          .eq('is_active', true)
          .single();
      return response;
    } catch (e) {
      return null;
    }
  }
}
