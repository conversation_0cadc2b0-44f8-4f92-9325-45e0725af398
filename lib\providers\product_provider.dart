import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/cleaning_product.dart';
import '../models/hierarchical_category.dart';
import '../services/mock_api_service.dart';

class ProductProvider with ChangeNotifier {
  final MockApiService _apiService = MockApiService();

  // Product cache
  final Map<String, List<CleaningProduct>> _productCache = {};

  // Currently active product streams
  final Map<String, StreamSubscription<Map<String, dynamic>>> _productStreams =
      {};

  // Loading states
  bool _isLoading = false;
  String? _error;

  // Analytics data
  final Map<String, int> _productViewCounts = {};
  final List<String> _recentlyViewedProducts = [];
  final Map<String, DateTime> _lastViewedTimestamps = {};

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<String> get recentlyViewedProducts => [..._recentlyViewedProducts];

  // Initialize products
  void loadProducts() {
    // No need to preload all products, we'll load them on demand
    // Instead, we'll subscribe to product updates when they're requested
  }

  // Get products by category ID
  Future<List<CleaningProduct>> fetchProductsByCategoryId(
      String categoryId) async {
    // Check if we already have the products in cache
    if (_productCache.containsKey(categoryId)) {
      return _productCache[categoryId]!;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getProductsByCategory(categoryId);

      if (response['status'] == 'success') {
        final List<dynamic> productsData = response['data'];
        final products = productsData
            .map((product) => CleaningProduct.fromMap(
                product, product['categoryId'] as String? ?? categoryId))
            .toList();

        // Cache the products
        _productCache[categoryId] = products;

        _isLoading = false;
        notifyListeners();

        // Subscribe to realtime updates for this category
        _subscribeToProductUpdates(categoryId);

        return products;
      } else {
        _error = response['message'] ?? 'Failed to load products';
        _isLoading = false;
        notifyListeners();
        return [];
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  // Subscribe to realtime product updates for a category
  void _subscribeToProductUpdates(String categoryId) {
    // Cancel any existing subscription for this category
    _productStreams[categoryId]?.cancel();

    // Subscribe to the products stream for this category
    _productStreams[categoryId] =
        _apiService.getProductsByCategoryStream(categoryId).listen((response) {
      if (response['status'] == 'success') {
        final List<dynamic> productsData = response['data'];
        final products = productsData
            .map((product) => CleaningProduct.fromMap(
                product, product['categoryId'] as String? ?? categoryId))
            .toList();

        // Update the cache
        _productCache[categoryId] = products;

        // Notify listeners about the update
        notifyListeners();
      }
    }, onError: (error) {
      // Just log the error but don't update the UI state
      // In a production app, use a proper logging framework
      debugPrint('Stream error for category $categoryId: $error');
    });
  }

  // Get products by category ID (synchronous version that uses cache)
  List<CleaningProduct> getProductsByCategoryId(String categoryId) {
    return _productCache[categoryId] ?? [];
  }

  // Get product by ID
  Future<CleaningProduct?> fetchProductById(String id) async {
    // Check if the product is in any of our cached categories
    for (final products in _productCache.values) {
      try {
        final product = products.firstWhere((product) => product.id == id);

        // Track product view for analytics
        _trackProductView(id);

        return product;
      } catch (e) {
        // Product not found in this category, continue searching
      }
    }

    // If not found in cache, fetch from API
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getProductDetails(id);

      if (response['status'] == 'success') {
        final productData = response['data'];
        final categoryId = productData['categoryId'] as String? ?? '';
        final product = CleaningProduct.fromMap(productData, categoryId);

        // Track product view for analytics
        _trackProductView(id);

        // Subscribe to realtime updates for this product
        _subscribeToProductDetailUpdates(id);

        _isLoading = false;
        notifyListeners();

        return product;
      } else {
        _error = response['message'] ?? 'Failed to load product details';
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  // Subscribe to realtime product detail updates
  void _subscribeToProductDetailUpdates(String productId) {
    // Cancel any existing subscription for this product
    _productStreams['product_$productId']?.cancel();

    // Subscribe to the product details stream
    _productStreams['product_$productId'] =
        _apiService.getProductDetailsStream(productId).listen((response) {
      if (response['status'] == 'success') {
        final productData = response['data'];
        final categoryId = productData['categoryId'] as String? ?? '';
        final product = CleaningProduct.fromMap(productData, categoryId);

        // Update the product in all cached categories
        for (final categoryId in _productCache.keys) {
          final products =
              List<CleaningProduct>.from(_productCache[categoryId]!);
          final index = products.indexWhere((p) => p.id == productId);

          if (index != -1) {
            products[index] = product;
            _productCache[categoryId] = products;
          }
        }

        // Notify listeners about the update
        notifyListeners();
      }
    }, onError: (error) {
      // Just log the error but don't update the UI state
      // In a production app, use a proper logging framework
      debugPrint('Stream error for product $productId: $error');
    });
  }

  // Track product view for analytics
  void _trackProductView(String productId) {
    // Increment view count
    _productViewCounts[productId] = (_productViewCounts[productId] ?? 0) + 1;

    // Update last viewed timestamp
    _lastViewedTimestamps[productId] = DateTime.now();

    // Add to recently viewed list (if not already at the top)
    _recentlyViewedProducts.remove(productId);
    _recentlyViewedProducts.insert(0, productId);

    // Limit recently viewed list to 10 items
    if (_recentlyViewedProducts.length > 10) {
      _recentlyViewedProducts.removeLast();
    }
  }

  // Get product by ID (synchronous version that uses cache)
  CleaningProduct? getProductById(String id) {
    for (final products in _productCache.values) {
      try {
        return products.firstWhere((product) => product.id == id);
      } catch (e) {
        // Product not found in this category, continue searching
      }
    }
    return null;
  }

  // Search products
  Future<List<CleaningProduct>> searchProducts(String query) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.searchProducts(query);

      if (response['status'] == 'success') {
        final List<dynamic> productsData = response['data'];
        final products = productsData
            .map((product) => CleaningProduct.fromMap(
                product, product['categoryId'] as String? ?? ''))
            .toList();

        // Subscribe to realtime updates for search results
        _subscribeToSearchUpdates(query);

        _isLoading = false;
        notifyListeners();

        return products;
      } else {
        _error = response['message'] ?? 'Failed to search products';
        _isLoading = false;
        notifyListeners();
        return [];
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  // Subscribe to realtime search updates
  void _subscribeToSearchUpdates(String query) {
    // Cancel any existing subscription for search
    _productStreams['search_$query']?.cancel();

    // Subscribe to the search results stream
    _productStreams['search_$query'] =
        _apiService.searchProductsStream(query).listen((response) {
      if (response['status'] == 'success') {
        // We don't cache search results, but we notify listeners
        // so that any UI displaying search results can update
        notifyListeners();
      }
    }, onError: (error) {
      // Just log the error but don't update the UI state
      debugPrint('Stream error for search "$query": $error');
    });
  }

  // Clear cache
  void clearCache() {
    // Cancel all active streams
    for (final subscription in _productStreams.values) {
      subscription.cancel();
    }
    _productStreams.clear();

    // Clear the product cache
    _productCache.clear();

    notifyListeners();
  }

  // Clean up resources
  @override
  void dispose() {
    // Cancel all active streams
    for (final subscription in _productStreams.values) {
      subscription.cancel();
    }
    super.dispose();
  }
}
