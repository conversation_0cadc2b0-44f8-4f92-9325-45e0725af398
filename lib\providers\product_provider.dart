import 'package:flutter/foundation.dart';
import '../models/cleaning_product.dart';
import '../services/supabase_service.dart';

class ProductProvider with ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();

  // Product cache
  final Map<String, List<CleaningProduct>> _productCache = {};

  // Loading states
  bool _isLoading = false;
  String? _error;

  // Analytics data
  final Map<String, int> _productViewCounts = {};
  final List<String> _recentlyViewedProducts = [];
  final Map<String, DateTime> _lastViewedTimestamps = {};

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<String> get recentlyViewedProducts => [..._recentlyViewedProducts];

  // Initialize products
  void loadProducts() {
    // No need to preload all products, we'll load them on demand
    // Instead, we'll subscribe to product updates when they're requested
  }

  // Get products by category ID
  Future<List<CleaningProduct>> fetchProductsByCategoryId(
      String categoryId) async {
    // Check if we already have the products in cache
    if (_productCache.containsKey(categoryId)) {
      return _productCache[categoryId]!;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Load products from Supabase
      final productsData =
          await _supabaseService.getProducts(categoryId: categoryId);

      // Convert Supabase data to CleaningProduct objects
      final products = productsData.map((productData) {
        return _supabaseToProduct(productData, categoryId);
      }).toList();

      // Cache the products
      _productCache[categoryId] = products;

      _isLoading = false;
      notifyListeners();

      return products;
    } catch (e) {
      _error = 'Failed to load products: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  // Load products by sub-subcategory ID
  Future<List<CleaningProduct>> loadProductsBySubSubcategory(
      String subSubcategoryId) async {
    // Use sub-subcategory ID as cache key
    final cacheKey = 'subsub_$subSubcategoryId';

    // Check if products are already cached
    if (_productCache.containsKey(cacheKey)) {
      return _productCache[cacheKey]!;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Load products from Supabase by sub-subcategory ID
      final productsData = await _supabaseService.getProducts(
          subSubcategoryId: subSubcategoryId);

      // Convert Supabase data to CleaningProduct objects
      final products = productsData.map((productData) {
        return _supabaseToProduct(productData, subSubcategoryId);
      }).toList();

      // Cache the products
      _productCache[cacheKey] = products;

      _isLoading = false;
      notifyListeners();

      return products;
    } catch (e) {
      _error = 'Failed to load products: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  // Convert Supabase product data to CleaningProduct
  CleaningProduct _supabaseToProduct(
      Map<String, dynamic> productData, String categoryId) {
    return CleaningProduct(
      id: productData['id'] as String,
      name: productData['name'] as String,
      description: productData['description'] as String? ?? '',
      price: (productData['price'] as num).toDouble(),
      rating: (productData['rating'] as num?)?.toDouble() ?? 0.0,
      reviews: productData['review_count'] as int? ?? 0,
      imageUrl: productData['image_url'] as String? ?? '',
      includes: [], // Default empty list, can be enhanced later
      excludes: [], // Default empty list, can be enhanced later
      options: 1, // Default value, can be enhanced later
      categoryId: categoryId,
    );
  }

  // Get products by category ID (synchronous version that uses cache)
  List<CleaningProduct> getProductsByCategoryId(String categoryId) {
    return _productCache[categoryId] ?? [];
  }

  // Get product by ID
  Future<CleaningProduct?> fetchProductById(String id) async {
    // Check if the product is in any of our cached categories
    for (final products in _productCache.values) {
      try {
        final product = products.firstWhere((product) => product.id == id);

        // Track product view for analytics
        _trackProductView(id);

        return product;
      } catch (e) {
        // Product not found in this category, continue searching
      }
    }

    // If not found in cache, we'll need to search all products
    // For now, return null - this can be enhanced later
    return null;
  }

  // Track product view for analytics
  void _trackProductView(String productId) {
    // Increment view count
    _productViewCounts[productId] = (_productViewCounts[productId] ?? 0) + 1;

    // Update last viewed timestamp
    _lastViewedTimestamps[productId] = DateTime.now();

    // Add to recently viewed list (if not already at the top)
    _recentlyViewedProducts.remove(productId);
    _recentlyViewedProducts.insert(0, productId);

    // Limit recently viewed list to 10 items
    if (_recentlyViewedProducts.length > 10) {
      _recentlyViewedProducts.removeLast();
    }
  }

  // Get product by ID (synchronous version that uses cache)
  CleaningProduct? getProductById(String id) {
    for (final products in _productCache.values) {
      try {
        return products.firstWhere((product) => product.id == id);
      } catch (e) {
        // Product not found in this category, continue searching
      }
    }
    return null;
  }

  // Search products (simplified version)
  Future<List<CleaningProduct>> searchProducts(String query) async {
    // For now, search through cached products
    // This can be enhanced later with Supabase search
    final allProducts = <CleaningProduct>[];
    for (final products in _productCache.values) {
      allProducts.addAll(products);
    }

    return allProducts
        .where((product) =>
            product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  // Clear cache
  void clearCache() {
    _productCache.clear();
    notifyListeners();
  }
}
