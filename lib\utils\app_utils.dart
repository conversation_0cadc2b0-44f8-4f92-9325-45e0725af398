import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Utility class for common functions used throughout the app
class AppUtils {
  /// Format a price in Indian Rupees
  static String formatPrice(num price) {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(price);
  }
  
  /// Format a date in a readable format
  static String formatDate(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }
  
  /// Format a time in a readable format
  static String formatTime(String time) {
    // If time is already in a readable format, return it
    if (time.contains('AM') || time.contains('PM')) {
      return time;
    }
    
    // If time is in 24-hour format, convert it to 12-hour format
    try {
      final parts = time.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      
      final period = hour >= 12 ? 'PM' : 'AM';
      final hour12 = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      
      return '$hour12:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      // If parsing fails, return the original time
      return time;
    }
  }
  
  /// Format a date and time in a readable format
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('dd MMM yyyy, hh:mm a').format(dateTime);
  }
  
  /// Get a color based on the status
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'in progress':
      case 'in_progress':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
  
  /// Get an icon based on the status
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'confirmed':
        return Icons.check_circle_outline;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'in progress':
      case 'in_progress':
        return Icons.sync;
      default:
        return Icons.help_outline;
    }
  }
  
  /// Calculate tax amount based on price and tax rate
  static double calculateTax(double price, {double taxRate = 0.05}) {
    return price * taxRate;
  }
  
  /// Calculate total amount including tax
  static double calculateTotal(double price, {double taxRate = 0.05}) {
    return price + calculateTax(price, taxRate: taxRate);
  }
  
  /// Apply discount to a price
  static double applyDiscount(double price, double discountPercentage) {
    return price * (1 - discountPercentage / 100);
  }
  
  /// Check if a service fee should be applied (for orders below ₹300)
  static bool shouldApplyServiceFee(double amount) {
    return amount < 300;
  }
  
  /// Get service fee amount
  static double getServiceFee() {
    return 49.0;
  }
  
  /// Truncate a string to a maximum length and add ellipsis if needed
  static String truncateString(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }
  
  /// Get a greeting based on the time of day
  static String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }
  
  /// Format a phone number for display
  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 10) {
      return phoneNumber;
    }
    
    return '+91 ${phoneNumber.substring(0, 5)} ${phoneNumber.substring(5)}';
  }
  
  /// Get a random placeholder image URL
  static String getRandomPlaceholderImage() {
    final imageIds = [
      '1262-12375',
      '23-2148113509',
      '23-2149138754',
      '23-2147734355',
      '176474-7920',
      '23-2149176085',
      '23-2147694133',
      '23-2149176105',
      '23-2149326846',
      '1157-26072',
      '1258-26678',
    ];
    
    final randomId = imageIds[DateTime.now().millisecond % imageIds.length];
    return 'https://img.freepik.com/free-photo/placeholder-$randomId.jpg';
  }
}
