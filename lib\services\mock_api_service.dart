import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../models/hierarchical_category.dart';
import 'base_mock_api_service.dart';

class MockApiService extends BaseMockApiService {
  // Singleton pattern
  static final MockApiService _instance = MockApiService._internal();
  factory MockApiService() => _instance;

  // In-memory database for realtime data
  final Map<String, dynamic> _database = {};

  // Stream controllers for realtime updates
  final StreamController<List<Map<String, dynamic>>>
      _categoriesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _productsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();

  // Getters for streams
  Stream<List<Map<String, dynamic>>> get categoriesStream =>
      _categoriesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get productsStream =>
      _productsStreamController.stream;

  MockApiService._internal() {
    // Initialize the in-memory database
    _initializeDatabase();

    // Set up periodic updates to simulate realtime data changes
    Timer.periodic(const Duration(minutes: 5), (_) {
      _simulateDataUpdates();
    });
  }

  // Initialize the in-memory database with data
  void _initializeDatabase() {
    // Convert categories to JSON format for the database
    final categories = CategoryData.getMainCategories();
    _database['categories'] =
        categories.map((cat) => _categoryToJson(cat)).toList();

    // Initialize products
    _database['products'] = [
      ...CategoryData.getFurnishedApartmentProducts(),
      ...CategoryData.getACProducts(),
      ...CategoryData.getPestControlProducts(),
      ...CategoryData.getPlumbingProducts(),
      ...CategoryData.getElectricalProducts(),
      ...CategoryData.getCarpenterProducts(),
      ...CategoryData.getApplianceProducts(),
    ];

    // Notify listeners about initial data
    _categoriesStreamController.add(_database['categories']);
    _productsStreamController.add(_database['products']);
  }

  // Simulate realtime data updates
  void _simulateDataUpdates() {
    // Simulate price changes for random products (10% of products)
    final products = List<Map<String, dynamic>>.from(_database['products']);
    final numProductsToUpdate = (products.length * 0.1).round();

    // Use the base class method to simulate price updates
    simulatePriceUpdates(products, numProductsToUpdate);

    // Update the database
    _database['products'] = products;

    // Notify listeners about the updates
    _productsStreamController.add(products);

    debugPrint('Simulated realtime updates for $numProductsToUpdate products');
  }

  // API endpoints
  Future<Map<String, dynamic>> getCategories() async {
    logApiCall('getCategories');
    await simulateNetworkDelay();

    // Return categories from the in-memory database
    final result = createSuccessResponse(_database['categories']);

    debugPrint(
        'MockAPI: getCategories returned ${(result['data'] as List).length} categories');
    return result;
  }

  // Get categories with realtime updates
  Stream<Map<String, dynamic>> getCategoriesStream() {
    // Transform the categories stream to include status
    return transformStreamToApiResponse(categoriesStream);
  }

  Future<Map<String, dynamic>> getProductsByCategory(String categoryId) async {
    logApiCall('getProductsByCategory', params: {'categoryId': categoryId});
    await simulateNetworkDelay();

    // Simulate occasional server errors (1% chance)
    final random = Random();
    if (random.nextInt(100) == 0) {
      debugPrint('MockAPI: Simulating server error for getProductsByCategory');
      return createErrorResponse(
          'Server temporarily unavailable. Please try again.');
    }

    // Get products from the in-memory database
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Filter products by category ID
    final products = allProducts.where((product) {
      // Check if the product has a categoryId field
      if (product.containsKey('categoryId')) {
        return product['categoryId'] == categoryId;
      }

      // If no categoryId field, check if the id starts with the category prefix
      final id = product['id'] as String;
      if (categoryId.contains('-')) {
        // For subcategories (e.g., "1-1")
        return id.startsWith(categoryId.split('-')[0]);
      } else {
        // For main categories (e.g., "1")
        return id.startsWith(categoryId);
      }
    }).toList();

    // Add some randomness to simulate dynamic data
    products.shuffle(random);

    // Simulate some products being out of stock
    for (var product in products) {
      if (!product.containsKey('inStock')) {
        product['inStock'] =
            random.nextDouble() > 0.2; // 80% chance of being in stock
      }
    }

    final result = createSuccessResponse(products);

    debugPrint(
        'MockAPI: getProductsByCategory returned ${products.length} products for category $categoryId');
    return result;
  }

  // Get products by category with realtime updates
  Stream<Map<String, dynamic>> getProductsByCategoryStream(String categoryId) {
    // Transform the products stream to filter by category and include status
    return productsStream.map((allProducts) {
      final products = allProducts.where((product) {
        // Check if the product has a categoryId field
        if (product.containsKey('categoryId')) {
          return product['categoryId'] == categoryId;
        }

        // If no categoryId field, check if the id starts with the category prefix
        final id = product['id'] as String;
        if (categoryId.contains('-')) {
          // For subcategories (e.g., "1-1")
          return id.startsWith(categoryId.split('-')[0]);
        } else {
          // For main categories (e.g., "1")
          return id.startsWith(categoryId);
        }
      }).toList();

      return createSuccessResponse(products);
    });
  }

  Future<Map<String, dynamic>> getProductDetails(String productId) async {
    await simulateNetworkDelay();

    // Simulate occasional network timeouts (2% chance)
    final random = Random();
    if (random.nextInt(50) == 0) {
      await Future.delayed(
          const Duration(seconds: 10)); // Long delay to simulate timeout
    }

    // Get products from the in-memory database
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Find the product by ID
    final product = allProducts.firstWhere(
      (p) => p['id'] == productId,
      orElse: () => <String, dynamic>{},
    );

    if (product.isEmpty) {
      return createErrorResponse('Product not found');
    }

    // Add view count to simulate analytics
    if (!product.containsKey('viewCount')) {
      product['viewCount'] = 0;
    }
    product['viewCount'] = (product['viewCount'] as int) + 1;

    // Add last viewed timestamp
    product['lastViewed'] = DateTime.now().toIso8601String();

    // Update the product in the database
    final productIndex = allProducts.indexWhere((p) => p['id'] == productId);
    if (productIndex != -1) {
      allProducts[productIndex] = product;
      _database['products'] = allProducts;

      // Notify listeners about the update
      _productsStreamController.add(allProducts);
    }

    return createSuccessResponse(product);
  }

  // Get product details with realtime updates
  Stream<Map<String, dynamic>> getProductDetailsStream(String productId) {
    // Transform the products stream to find the specific product and include status
    return productsStream.map((allProducts) {
      final product = allProducts.firstWhere(
        (p) => p['id'] == productId,
        orElse: () => <String, dynamic>{},
      );

      if (product.isEmpty) {
        return createErrorResponse('Product not found');
      }

      return createSuccessResponse(product);
    });
  }

  Future<Map<String, dynamic>> searchProducts(String query) async {
    await simulateNetworkDelay();

    if (query.isEmpty) {
      return createSuccessResponse([]);
    }

    // Get products from the in-memory database
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Normalize the search query
    final searchQuery = query.toLowerCase().trim();

    // Special handling for BHK queries
    bool isBHKQuery = searchQuery.contains("bhk");

    // Special handling for numeric queries (like "1" for "1 BHK")
    bool isNumericQuery = RegExp(r'^\d+$').hasMatch(searchQuery);

    final results = allProducts.where((product) {
      final name = product['name'].toString().toLowerCase();
      final description = product['description'].toString().toLowerCase();

      // For BHK queries, ensure we match the exact pattern (e.g., "1 bhk", "2 bhk")
      if (isBHKQuery) {
        return name.contains(searchQuery) || description.contains(searchQuery);
      }

      // For numeric queries (like "1"), check if it's part of a BHK pattern
      if (isNumericQuery) {
        final bhkPattern = "$searchQuery bhk";
        return name.contains(bhkPattern) || description.contains(bhkPattern);
      }

      // For other queries, check if any word in the name or description starts with the query
      final nameWords = name.split(' ');
      final descWords = description.split(' ');

      // Check if any word starts with the query
      bool nameMatch = nameWords.any((word) => word.startsWith(searchQuery));
      bool descMatch = descWords.any((word) => word.startsWith(searchQuery));

      // Also check for full contains match
      return nameMatch ||
          descMatch ||
          name.contains(searchQuery) ||
          description.contains(searchQuery);
    }).toList();

    // Simulate search relevance by sorting results
    results.sort((a, b) {
      final aName = a['name'].toString().toLowerCase();
      final bName = b['name'].toString().toLowerCase();

      // If one contains the exact query and the other doesn't, prioritize the exact match
      final aExactMatch = aName.contains(searchQuery);
      final bExactMatch = bName.contains(searchQuery);

      if (aExactMatch && !bExactMatch) return -1;
      if (!aExactMatch && bExactMatch) return 1;

      // Otherwise sort by name
      return aName.compareTo(bName);
    });

    // Add search metadata
    for (var product in results) {
      // Track that this product was found in a search
      if (!product.containsKey('searchCount')) {
        product['searchCount'] = 0;
      }
      product['searchCount'] = (product['searchCount'] as int) + 1;

      // Add search timestamp
      product['lastSearched'] = DateTime.now().toIso8601String();
    }

    // Update the products in the database with search metadata
    _updateProductsInDatabase(results);

    return createSuccessResponse(results);
  }

  // Get search results with realtime updates
  Stream<Map<String, dynamic>> searchProductsStream(String query) {
    // Transform the products stream to filter by search query and include status
    return productsStream.map((allProducts) {
      if (query.isEmpty) {
        return createSuccessResponse(<Map<String, dynamic>>[]);
      }

      // Normalize the search query
      final searchQuery = query.toLowerCase().trim();

      final results = allProducts.where((product) {
        final name = product['name'].toString().toLowerCase();
        final description = product['description'].toString().toLowerCase();

        return name.contains(searchQuery) || description.contains(searchQuery);
      }).toList();

      return createSuccessResponse(results);
    });
  }

  // Helper method to update products in the database
  void _updateProductsInDatabase(List<Map<String, dynamic>> updatedProducts) {
    if (updatedProducts.isEmpty) return;

    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    // Update each product in the database
    for (var updatedProduct in updatedProducts) {
      final productId = updatedProduct['id'];
      final index = allProducts.indexWhere((p) => p['id'] == productId);

      if (index != -1) {
        allProducts[index] = updatedProduct;
      }
    }

    // Update the database
    _database['products'] = allProducts;

    // Notify listeners about the updates
    _productsStreamController.add(allProducts);
  }

  // Method to refresh categories from external source
  Future<void> refreshCategoriesFromSource() async {
    // Re-initialize categories from the static data source
    final categories = CategoryData.getMainCategories();
    _database['categories'] =
        categories.map((cat) => _categoryToJson(cat)).toList();

    // Notify listeners about the update
    _categoriesStreamController.add(_database['categories']);

    debugPrint(
        'MockAPI: Categories refreshed from source, total: ${categories.length}');
  }

  // Method to manually update categories (for synchronization with other services)
  void updateCategories(List<Map<String, dynamic>> newCategories) {
    _database['categories'] = newCategories;

    // Notify listeners about the update
    _categoriesStreamController.add(_database['categories']);

    debugPrint(
        'MockAPI: Categories updated manually, total: ${newCategories.length}');
  }

  // Helper method to convert HierarchicalCategory to JSON
  Map<String, dynamic> _categoryToJson(HierarchicalCategory category) {
    return {
      'id': category.id,
      'name': category.name,
      'icon': category.icon.codePoint,
      'color':
          category.color.toARGB32(), // Use toARGB32 instead of deprecated value
      'imageUrl': category.imageUrl,
      'hasProducts': category.hasProducts,
      'subcategories': category.subcategories
          .map((subcat) => _categoryToJson(subcat))
          .toList(),
    };
  }
}
