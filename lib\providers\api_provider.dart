import 'package:flutter/foundation.dart';
import '../services/enhanced_mock_api_service.dart';
import '../models/cleaning_product.dart';
import '../models/hierarchical_category.dart';

/// Provider that interfaces with the EnhancedMockApiService
/// to provide data to the UI
class ApiProvider with ChangeNotifier {
  final EnhancedMockApiService _apiService = EnhancedMockApiService();

  // Loading states
  bool _isLoading = false;
  String? _error;

  // Data caches
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _subcategories = [];
  Map<String, List<CleaningProduct>> _productsByCategory = {};
  Map<String, dynamic>? _cart;
  List<Map<String, dynamic>> _bookings = [];
  Map<String, dynamic>? _profile;
  List<Map<String, dynamic>> _chatMessages = [];
  Map<String, dynamic>? _supportAgent;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<Map<String, dynamic>> get categories => [..._categories];
  List<Map<String, dynamic>> get subcategories => [..._subcategories];
  Map<String, dynamic>? get cart => _cart;
  List<Map<String, dynamic>> get bookings => [..._bookings];
  Map<String, dynamic>? get profile => _profile;
  List<Map<String, dynamic>> get chatMessages => [..._chatMessages];
  Map<String, dynamic>? get supportAgent => _supportAgent;

  // Initialize the provider
  ApiProvider() {
    _setupStreams();
  }

  // Set up streams for realtime updates
  void _setupStreams() {
    // Categories stream
    _apiService.getCategoriesStream().listen((response) {
      if (response['status'] == 'success') {
        _categories = List<Map<String, dynamic>>.from(response['data']);
        notifyListeners();
      }
    });

    // Subcategories stream
    _apiService.getSubcategoriesStream().listen((response) {
      if (response['status'] == 'success') {
        _subcategories = List<Map<String, dynamic>>.from(response['data']);
        notifyListeners();
      }
    });

    // Cart stream
    _apiService.getCartStream().listen((response) {
      if (response['status'] == 'success') {
        _cart = response['data'];
        notifyListeners();
      }
    });

    // Bookings stream
    _apiService.getBookingsStream().listen((response) {
      if (response['status'] == 'success') {
        _bookings = List<Map<String, dynamic>>.from(response['data']);
        notifyListeners();
      }
    });

    // Profile stream
    _apiService.getProfileStream().listen((response) {
      if (response['status'] == 'success') {
        _profile = response['data'];
        notifyListeners();
      }
    });

    // Chat messages stream
    _apiService.getChatMessagesStream().listen((response) {
      if (response['status'] == 'success') {
        final data = response['data'];
        _chatMessages = List<Map<String, dynamic>>.from(data['messages']);
        _supportAgent = data['supportAgent'];
        notifyListeners();
      }
    });
  }

  // Set current user after authentication
  void setCurrentUser(String userId) {
    _apiService.setCurrentUser(userId);
  }

  // Load categories
  Future<void> loadCategories() async {
    if (_categories.isNotEmpty) {
      return; // Already loaded
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getCategories();

      if (response['status'] == 'success') {
        _categories = List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'];
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load subcategories
  Future<void> loadSubcategories() async {
    if (_subcategories.isNotEmpty) {
      return; // Already loaded
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getSubcategories();

      if (response['status'] == 'success') {
        _subcategories = List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'];
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get subcategories by parent ID
  Future<List<Map<String, dynamic>>> getSubcategoriesByParentId(
      String parentId) async {
    try {
      final response = await _apiService.getSubcategoriesByParentId(parentId);

      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'];
        return [];
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      return [];
    }
  }

  // Get products by category ID
  Future<List<CleaningProduct>> getProductsByCategory(String categoryId) async {
    // Check cache first
    if (_productsByCategory.containsKey(categoryId)) {
      return _productsByCategory[categoryId]!;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getProductsByCategory(categoryId);

      if (response['status'] == 'success') {
        final productsData = List<Map<String, dynamic>>.from(response['data']);
        final products = productsData
            .map((product) => CleaningProduct.fromMap(
                product, product['categoryId'] as String? ?? categoryId))
            .toList();

        // Cache the products
        _productsByCategory[categoryId] = products;

        _isLoading = false;
        notifyListeners();

        return products;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return [];
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  // Get product details
  Future<CleaningProduct?> getProductDetails(String productId) async {
    // Check cache first
    for (final products in _productsByCategory.values) {
      try {
        final product = products.firstWhere((p) => p.id == productId);
        return product;
      } catch (e) {
        // Product not found in this category, continue searching
      }
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getProductDetails(productId);

      if (response['status'] == 'success') {
        final productData = response['data'];
        final categoryId = productData['categoryId'] as String? ?? '';
        final product = CleaningProduct.fromMap(productData, categoryId);

        _isLoading = false;
        notifyListeners();

        return product;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  // Search products
  Future<List<CleaningProduct>> searchProducts(String query) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.searchProducts(query);

      if (response['status'] == 'success') {
        final productsData = List<Map<String, dynamic>>.from(response['data']);
        final products = productsData
            .map((product) => CleaningProduct.fromMap(
                product, product['categoryId'] as String? ?? ''))
            .toList();

        _isLoading = false;
        notifyListeners();

        return products;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return [];
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  //
  // CART METHODS
  //

  // Get cart
  Future<Map<String, dynamic>?> getCart() async {
    if (_cart != null) {
      return _cart;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getCart();

      if (response['status'] == 'success') {
        _cart = response['data'];
        _isLoading = false;
        notifyListeners();
        return _cart;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  // Add to cart
  Future<bool> addToCart(String productId, int quantity) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.addToCart(productId, quantity);

      if (response['status'] == 'success') {
        _cart = response['data'];
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Remove from cart
  Future<bool> removeFromCart(String productId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.removeFromCart(productId);

      if (response['status'] == 'success') {
        _cart = response['data'];
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Clear cart
  Future<bool> clearCart() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.clearCart();

      if (response['status'] == 'success') {
        _cart = response['data'];
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Apply coupon
  Future<bool> applyCoupon(String couponCode) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.applyCoupon(couponCode);

      if (response['status'] == 'success') {
        _cart = response['data'];
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  //
  // BOOKING METHODS
  //

  // Create booking
  Future<Map<String, dynamic>?> createBooking(
      Map<String, dynamic> bookingDetails) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.createBooking(bookingDetails);

      if (response['status'] == 'success') {
        final booking = response['data'];
        _isLoading = false;
        notifyListeners();
        return booking;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  // Get bookings
  Future<List<Map<String, dynamic>>> getBookings() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getBookings();

      if (response['status'] == 'success') {
        _bookings = List<Map<String, dynamic>>.from(response['data']);
        _isLoading = false;
        notifyListeners();
        return _bookings;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return [];
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return [];
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.cancelBooking(bookingId);

      if (response['status'] == 'success') {
        // Update bookings list
        final updatedBooking = response['data'];
        final index = _bookings.indexWhere((b) => b['id'] == bookingId);

        if (index != -1) {
          _bookings[index] = updatedBooking;
        }

        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Get booking details
  Future<Map<String, dynamic>?> getBookingDetails(String bookingId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getBookingDetails(bookingId);

      if (response['status'] == 'success') {
        final booking = response['data'];
        _isLoading = false;
        notifyListeners();
        return booking;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  //
  // PROFILE METHODS
  //

  // Get profile
  Future<Map<String, dynamic>?> getProfile() async {
    if (_profile != null) {
      return _profile;
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getProfile();

      if (response['status'] == 'success') {
        _profile = response['data'];
        _isLoading = false;
        notifyListeners();
        return _profile;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.updateProfile(profileData);

      if (response['status'] == 'success') {
        _profile = response['data'];
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Add address
  Future<bool> addAddress(Map<String, dynamic> address) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.addAddress(address);

      if (response['status'] == 'success') {
        _profile = response['data'];
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  //
  // CHAT METHODS
  //

  // Get chat messages
  Future<Map<String, dynamic>?> getChatMessages() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.getChatMessages();

      if (response['status'] == 'success') {
        final data = response['data'];
        _chatMessages = List<Map<String, dynamic>>.from(data['messages']);
        _supportAgent = data['supportAgent'];
        _isLoading = false;
        notifyListeners();
        return data;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  // Send chat message
  Future<bool> sendChatMessage(String message) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.sendChatMessage(message);

      if (response['status'] == 'success') {
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = response['message'];
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
}
