import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OnboardingProvider with ChangeNotifier {
  bool _isFirstLaunch = true;
  bool _isLoading = true;

  bool get isFirstLaunch => _isFirstLaunch;
  bool get isLoading => _isLoading;

  // Initialize the onboarding state
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      _isFirstLaunch = prefs.getBool('isFirstLaunch') ?? true;
    } catch (e) {
      // Default to showing onboarding if there's an error
      _isFirstLaunch = true;
    }

    _isLoading = false;
    notifyListeners();
  }

  // Mark onboarding as completed
  Future<void> completeOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isFirstLaunch', false);
      _isFirstLaunch = false;
      notifyListeners();
    } catch (e) {
      // Handle error
      print('Error saving onboarding state: $e');
    }
  }

  // Reset onboarding (for testing)
  Future<void> resetOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isFirstLaunch', true);
      _isFirstLaunch = true;
      notifyListeners();
    } catch (e) {
      // Handle error
      print('Error resetting onboarding state: $e');
    }
  }
}
