import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hierarchical_category.dart';
import '../models/service.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';
import '../providers/cart_provider.dart';
import 'service_detail_screen.dart';

class HierarchicalServiceListScreen extends StatefulWidget {
  final HierarchicalCategory category;

  const HierarchicalServiceListScreen({
    super.key,
    required this.category,
  });

  @override
  State<HierarchicalServiceListScreen> createState() =>
      _HierarchicalServiceListScreenState();
}

class _HierarchicalServiceListScreenState
    extends State<HierarchicalServiceListScreen> {
  bool _isLoading = true;
  List<Service> _services = [];

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    // Simulate API call to fetch services
    await Future.delayed(const Duration(seconds: 1));

    // Generate sample services based on the category
    final List<Service> services = _generateSampleServices(widget.category);

    if (mounted) {
      setState(() {
        _services = services;
        _isLoading = false;
      });
    }
  }

  List<Service> _generateSampleServices(HierarchicalCategory category) {
    // Generate sample services based on the category
    final List<Service> services = [];

    // Add 5-10 sample services
    final int count = 5 + (category.id.hashCode % 5);
    for (int i = 1; i <= count; i++) {
      services.add(
        Service(
          id: '${category.id}-$i',
          name: '${category.name} Service $i',
          description:
              'Professional ${category.name.toLowerCase()} service with experienced technicians.',
          price: 100.0 + (i * 50.0),
          duration: 60 + (i * 15),
          categoryId: category.id,
          imageUrl: category.imageUrl,
          rating: 4.0 + (i % 2) * 0.5,
          reviewCount: 10 + (i * 5),
        ),
      );
    }

    return services;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: Text(
          '${widget.category.name} Services',
          style: const TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: widget.category.color.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          widget.category.icon,
                          size: 24,
                          color: widget.category.color,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Available ${widget.category.name} Services',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: _services.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            itemCount: _services.length,
                            itemBuilder: (context, index) {
                              final service = _services[index];
                              return _buildServiceItem(service);
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'No services available for ${widget.category.name}',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          NeumorphicButton(
            onPressed: () => Navigator.of(context).pop(),
            color: widget.category.color,
            child: const Text(
              'Go Back',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceItem(Service service) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ServiceDetailScreen(
              service: service,
              category: widget.category,
            ),
          ),
        );
      },
      child: NeumorphicContainer(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Service Image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: service.imageUrl != null
                  ? Image.network(
                      service.imageUrl!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: widget.category.color.withOpacity(0.1),
                          child: Icon(
                            widget.category.icon,
                            color: widget.category.color,
                            size: 30,
                          ),
                        );
                      },
                    )
                  : Container(
                      width: 80,
                      height: 80,
                      color: widget.category.color.withOpacity(0.1),
                      child: Icon(
                        widget.category.icon,
                        color: widget.category.color,
                        size: 30,
                      ),
                    ),
            ),
            const SizedBox(width: 12),
            // Service Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    service.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    service.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${service.rating}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${service.reviewCount})',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF7D8CA3),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '₹${service.price.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
