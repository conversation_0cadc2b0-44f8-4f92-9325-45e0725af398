import 'package:flutter/material.dart';
import 'onboarding_image_widget.dart';

class OnboardingPageData {
  final String title;
  final String image;
  final String buttonText;

  OnboardingPageData({
    required this.title,
    required this.image,
    required this.buttonText,
  });
}

class OnboardingPage extends StatelessWidget {
  final OnboardingPageData data;
  final VoidCallback onButtonPressed;

  const OnboardingPage({
    Key? key,
    required this.data,
    required this.onButtonPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Illustration
          Expanded(
            flex: 3,
            child: _buildIllustration(),
          ),
          const SizedBox(height: 40),
          // Title
          Text(
            data.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
          const Spacer(),
          // Button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: onButtonPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF8A56AC), // Purple button
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                data.buttonText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIllustration() {
    try {
      // Try to load the asset image
      return Image.asset(
        data.image,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          // If asset loading fails, use our custom illustrations
          return _buildCustomIllustration();
        },
      );
    } catch (e) {
      // Fallback to custom illustrations
      return _buildCustomIllustration();
    }
  }

  Widget _buildCustomIllustration() {
    // Determine which illustration to show based on the image path
    int illustrationIndex = 0;
    if (data.image.contains('onboarding1')) {
      illustrationIndex = 0;
    } else if (data.image.contains('onboarding2')) {
      illustrationIndex = 1;
    } else {
      illustrationIndex = 2;
    }

    return OnboardingImageWidget(
      index: illustrationIndex,
    );
  }
}
