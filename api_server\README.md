# DodoBooker API Server

This is a mock API server for the DodoBooker Flutter app that provides HTTP endpoints you can test with Postman.

## Quick Start

1. **Install Dependencies**
   ```bash
   cd api_server
   npm install
   ```

2. **Start the Server**
   ```bash
   npm start
   ```

3. **Server will be running at:** `http://localhost:3000`

## Testing with Postman

### Option 1: Import Collection File
1. Open Postman
2. Click "Import" button
3. Select the file: `DodoBooker_API.postman_collection.json`
4. The collection will be imported with all endpoints ready to test

### Option 2: Manual Setup
1. Create a new collection in Postman
2. Set up environment variables:
   - `base_url`: `http://localhost:3000`
   - `user_id`: `user_**********`
3. Add requests manually using the API documentation

## Available Endpoints

### Authentication
- `POST /auth/send-otp` - Send OTP to mobile number
- `POST /auth/verify-otp` - Verify OTP (use "123456" for testing)

### Categories & Products
- `GET /categories` - Get all service categories
- `GET /categories/{id}` - Get specific category
- `GET /subcategories` - Get subcategories (with optional categoryId filter)
- `GET /products` - Get products (with optional filters)
- `GET /products/{id}` - Get specific product

### Cart Management
- `GET /cart/{userId}` - Get user's cart
- `POST /cart/{userId}/add` - Add item to cart
- `DELETE /cart/{userId}` - Clear entire cart
- `DELETE /cart/{userId}/item/{productId}` - Remove specific item

### Profile
- `GET /profile/{userId}` - Get user profile
- `PUT /profile/{userId}` - Update user profile

### Testing
- `GET /health` - Health check
- `GET /users` - Get all users (for testing)

## Sample Test Flow

1. **Health Check**
   ```
   GET http://localhost:3000/health
   ```

2. **Authentication**
   ```
   POST http://localhost:3000/auth/send-otp
   Body: {"mobile": "**********"}
   
   POST http://localhost:3000/auth/verify-otp
   Body: {"mobile": "**********", "otp": "123456"}
   ```

3. **Browse Services**
   ```
   GET http://localhost:3000/categories
   GET http://localhost:3000/subcategories?categoryId=1
   GET http://localhost:3000/products?categoryId=1-1
   ```

4. **Cart Operations**
   ```
   GET http://localhost:3000/cart/user_**********
   
   POST http://localhost:3000/cart/user_**********/add
   Body: {"productId": "1-1-1", "quantity": 1}
   ```

## Response Format

All responses follow this format:

**Success Response:**
```json
{
  "status": "success",
  "message": "Success",
  "data": { ... },
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

**Error Response:**
```json
{
  "status": "error",
  "message": "Error description",
  "code": 400,
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

## Notes

- The server uses in-memory storage, so data resets when you restart the server
- OTP for testing is always "123456"
- User IDs are generated as "user_{mobile_number}"
- All prices are in Indian Rupees (₹)
- Tax is calculated at 5%

## Troubleshooting

1. **Port 3000 already in use:**
   - Change the PORT variable in server.js
   - Or kill the process using port 3000

2. **CORS issues:**
   - The server includes CORS middleware
   - If you still face issues, check your browser console

3. **Module not found:**
   - Make sure you ran `npm install`
   - Check that you're in the correct directory

## Development

To run in development mode with auto-restart:
```bash
npm run dev
```

This uses nodemon to automatically restart the server when files change.
