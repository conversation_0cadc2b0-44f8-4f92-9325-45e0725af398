import 'package:flutter/material.dart';

class DodoLogoWidget extends StatelessWidget {
  final double size;
  final bool showText;
  
  const DodoLogoWidget({
    Key? key, 
    this.size = 120, 
    this.showText = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: showText ? size * 1.2 : size,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Dodo bird icon
          CustomPaint(
            size: Size(size * 0.8, size * 0.6),
            painter: DodoBirdPainter(),
          ),
          
          // Text part (optional)
          if (showText) ...[
            const SizedBox(height: 8),
            Text(
              'DODO',
              style: TextStyle(
                color: Colors.yellow,
                fontSize: size * 0.18,
                fontWeight: FontWeight.bold,
                letterSpacing: 2,
              ),
            ),
            Text(
              'Booker',
              style: TextStyle(
                color: Colors.yellow,
                fontSize: size * 0.16,
                fontWeight: FontWeight.bold,
                fontStyle: FontStyle.italic,
              ),
            ),
            // Registered trademark
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: EdgeInsets.only(right: size * 0.1),
                child: Text(
                  '®',
                  style: TextStyle(
                    color: Colors.yellow,
                    fontSize: size * 0.08,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class DodoBirdPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;

    final path = Path();
    
    // Body
    path.moveTo(size.width * 0.3, size.height * 0.5);
    path.quadraticBezierTo(
      size.width * 0.2, size.height * 0.8, 
      size.width * 0.4, size.height * 0.9
    );
    path.quadraticBezierTo(
      size.width * 0.6, size.height * 1.0, 
      size.width * 0.7, size.height * 0.7
    );
    path.quadraticBezierTo(
      size.width * 0.75, size.height * 0.5, 
      size.width * 0.6, size.height * 0.4
    );
    
    // Head
    path.quadraticBezierTo(
      size.width * 0.5, size.height * 0.2, 
      size.width * 0.3, size.height * 0.3
    );
    
    // Beak
    path.lineTo(size.width * 0.15, size.height * 0.2);
    path.lineTo(size.width * 0.25, size.height * 0.4);
    
    // Close the path
    path.close();
    
    canvas.drawPath(path, paint);
    
    // Eye
    final eyePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(size.width * 0.35, size.height * 0.3),
      size.width * 0.05,
      eyePaint,
    );
    
    // Letter D in the eye
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'D',
        style: TextStyle(
          color: Colors.yellow,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas, 
      Offset(
        size.width * 0.33, 
        size.height * 0.27,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
