import 'package:flutter/material.dart';
import 'neumorphic_button.dart';

class AnimatedEmptyCart extends StatelessWidget {
  final VoidCallback onBrowseServices;

  const AnimatedEmptyCart({
    super.key,
    required this.onBrowseServices,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Simple cart icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              size: 70,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 24),
          // Text
          Column(
            children: [
              Text(
                'Your cart is empty',
                style: TextStyle(
                  fontSize: 22,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  'Add services to your cart to book them',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),
          // Button
          SizedBox(
            width: 180, // Reduced width
            child: NeumorphicButton(
              color: Colors.black,
              onPressed: onBrowseServices,
              child: const Text(
                'Browse Services',
                style: TextStyle(
                  fontSize: 14, // Smaller font size
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
