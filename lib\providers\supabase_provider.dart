import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';

class SupabaseProvider with ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();
  
  // Loading states
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _error;

  // Data
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _products = [];
  List<Map<String, dynamic>> _cartItems = [];
  List<Map<String, dynamic>> _bookings = [];
  Map<String, dynamic>? _userProfile;
  List<Map<String, dynamic>> _addresses = [];
  List<Map<String, dynamic>> _coupons = [];

  // Getters
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get error => _error;
  List<Map<String, dynamic>> get categories => _categories;
  List<Map<String, dynamic>> get products => _products;
  List<Map<String, dynamic>> get cartItems => _cartItems;
  List<Map<String, dynamic>> get bookings => _bookings;
  Map<String, dynamic>? get userProfile => _userProfile;
  List<Map<String, dynamic>> get addresses => _addresses;
  List<Map<String, dynamic>> get coupons => _coupons;
  
  // Check if Supabase is configured
  bool get isConfigured => _supabaseService.isConfigured;
  
  // Current user
  User? get currentUser => _supabaseService.currentUser;

  // Initialize Supabase
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _setLoading(true);
      _setError(null);
      
      if (!_supabaseService.isConfigured) {
        _setError('Supabase is not configured. Please update your credentials in SupabaseConfig.');
        return;
      }
      
      await _supabaseService.initialize();
      _isInitialized = true;
      
      // Load initial data
      await loadCategories();
      await loadCoupons();
      
      debugPrint('SupabaseProvider: Initialized successfully');
    } catch (e) {
      _setError('Failed to initialize Supabase: $e');
      debugPrint('SupabaseProvider: Initialization error: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Authentication methods
  Future<bool> signInWithOTP(String phone) async {
    try {
      _setLoading(true);
      _setError(null);
      
      await _supabaseService.signInWithOTP(phone);
      return true;
    } catch (e) {
      _setError('Failed to send OTP: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> verifyOTP(String phone, String token) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final response = await _supabaseService.verifyOTP(phone, token);
      
      if (response.user != null) {
        // Load user data after successful authentication
        await loadUserProfile();
        await loadCartItems();
        await loadBookings();
        await loadAddresses();
        return true;
      }
      
      return false;
    } catch (e) {
      _setError('Failed to verify OTP: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      await _supabaseService.signOut();
      
      // Clear user data
      _cartItems = [];
      _bookings = [];
      _userProfile = null;
      _addresses = [];
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to sign out: $e');
    }
  }

  // Categories methods
  Future<void> loadCategories() async {
    try {
      _setLoading(true);
      _setError(null);
      
      _categories = await _supabaseService.getCategories();
      debugPrint('SupabaseProvider: Loaded ${_categories.length} categories');
    } catch (e) {
      _setError('Failed to load categories: $e');
      debugPrint('SupabaseProvider: Error loading categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createCategory(Map<String, dynamic> categoryData) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final newCategory = await _supabaseService.createCategory(categoryData);
      _categories.add(newCategory);
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to create category: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Products methods
  Future<void> loadProducts({String? categoryId, String? subcategoryId}) async {
    try {
      _setLoading(true);
      _setError(null);
      
      _products = await _supabaseService.getProducts(
        categoryId: categoryId,
        subcategoryId: subcategoryId,
      );
      
      debugPrint('SupabaseProvider: Loaded ${_products.length} products');
    } catch (e) {
      _setError('Failed to load products: $e');
      debugPrint('SupabaseProvider: Error loading products: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Cart methods
  Future<void> loadCartItems() async {
    if (currentUser == null) return;
    
    try {
      _cartItems = await _supabaseService.getCartItems(currentUser!.id);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load cart items: $e');
    }
  }

  Future<bool> addToCart(String productId, int quantity) async {
    if (currentUser == null) {
      _setError('Please sign in to add items to cart');
      return false;
    }
    
    try {
      await _supabaseService.addToCart(currentUser!.id, productId, quantity);
      await loadCartItems(); // Refresh cart
      return true;
    } catch (e) {
      _setError('Failed to add item to cart: $e');
      return false;
    }
  }

  Future<bool> removeFromCart(String productId) async {
    if (currentUser == null) return false;
    
    try {
      await _supabaseService.removeFromCart(currentUser!.id, productId);
      await loadCartItems(); // Refresh cart
      return true;
    } catch (e) {
      _setError('Failed to remove item from cart: $e');
      return false;
    }
  }

  Future<bool> clearCart() async {
    if (currentUser == null) return false;
    
    try {
      await _supabaseService.clearCart(currentUser!.id);
      _cartItems = [];
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to clear cart: $e');
      return false;
    }
  }

  // Bookings methods
  Future<void> loadBookings() async {
    if (currentUser == null) return;
    
    try {
      _bookings = await _supabaseService.getBookings(currentUser!.id);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load bookings: $e');
    }
  }

  Future<bool> createBooking(Map<String, dynamic> bookingData) async {
    if (currentUser == null) {
      _setError('Please sign in to create booking');
      return false;
    }
    
    try {
      bookingData['user_id'] = currentUser!.id;
      await _supabaseService.createBooking(bookingData);
      await loadBookings(); // Refresh bookings
      return true;
    } catch (e) {
      _setError('Failed to create booking: $e');
      return false;
    }
  }

  // User profile methods
  Future<void> loadUserProfile() async {
    if (currentUser == null) return;
    
    try {
      _userProfile = await _supabaseService.getUserProfile(currentUser!.id);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load user profile: $e');
    }
  }

  Future<bool> updateUserProfile(Map<String, dynamic> profileData) async {
    if (currentUser == null) {
      _setError('Please sign in to update profile');
      return false;
    }
    
    try {
      _userProfile = await _supabaseService.updateUserProfile(currentUser!.id, profileData);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update user profile: $e');
      return false;
    }
  }

  // Addresses methods
  Future<void> loadAddresses() async {
    if (currentUser == null) return;
    
    try {
      _addresses = await _supabaseService.getUserAddresses(currentUser!.id);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load addresses: $e');
    }
  }

  // Coupons methods
  Future<void> loadCoupons() async {
    try {
      _coupons = await _supabaseService.getActiveCoupons();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load coupons: $e');
    }
  }
}
