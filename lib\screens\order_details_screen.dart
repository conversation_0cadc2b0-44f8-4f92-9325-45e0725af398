import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/order.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';
import 'package:url_launcher/url_launcher.dart';
import 'chat_support_screen.dart';

class OrderDetailsScreen extends StatelessWidget {
  final Order order;

  const OrderDetailsScreen({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: const Text(
          'Order Details',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.black),
            onPressed: () => _shareOrderDetails(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order ID and Status
              _buildOrderHeader(context),
              const SizedBox(height: 16),

              // Service Details
              _buildServiceDetails(context),
              const SizedBox(height: 16),

              // Schedule Details
              _buildScheduleDetails(context),
              const SizedBox(height: 16),

              // Address Details
              _buildAddressDetails(context),
              const SizedBox(height: 16),

              // Payment Details
              _buildPaymentDetails(context),
              const SizedBox(height: 16),

              // Technician Details (if assigned)
              if (order.technician != null) _buildTechnicianDetails(context),
              if (order.technician != null) const SizedBox(height: 16),

              // Notes (if any)
              if (order.notes != null && order.notes!.isNotEmpty)
                _buildNotesSection(context),
              if (order.notes != null && order.notes!.isNotEmpty)
                const SizedBox(height: 16),

              // Cancellation Reason (if cancelled)
              if (order.status == OrderStatus.cancelled)
                _buildCancellationReason(context),
              if (order.status == OrderStatus.cancelled)
                const SizedBox(height: 16),

              // Action Buttons
              _buildActionButtons(context),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeader(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Order ID',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7D8CA3),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          order.id,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                        const SizedBox(width: 8),
                        InkWell(
                          onTap: () {
                            Clipboard.setData(ClipboardData(text: order.id));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Order ID copied to clipboard'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          },
                          child: const Icon(
                            Icons.copy,
                            size: 16,
                            color: Color(0xFF7D8CA3),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: order.statusColor.withAlpha(30),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  order.statusText,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: order.statusColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Divider(color: Color(0xFFEEEEEE)),
          const SizedBox(height: 8),
          Row(
            children: [
              const Text(
                'Ordered on: ',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF7D8CA3),
                ),
              ),
              Text(
                '${order.createdAt.day}/${order.createdAt.month}/${order.createdAt.year} at ${order.createdAt.hour}:${order.createdAt.minute.toString().padLeft(2, '0')}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2D4059),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildServiceDetails(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Service Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 16),
          ...order.items
              .map((item) => _buildServiceItem(context, item))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildServiceItem(BuildContext context, dynamic item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: item.category.color.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              item.category.icon,
              color: item.category.color,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.service.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D4059),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  item.category.name,
                  style: TextStyle(
                    fontSize: 14,
                    color: item.category.color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Quantity: ${item.quantity}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7D8CA3),
                  ),
                ),
                if (item.addons.isNotEmpty) const SizedBox(height: 8),
                if (item.addons.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Add-ons:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF7D8CA3),
                        ),
                      ),
                      const SizedBox(height: 4),
                      ...item.addons.map((addon) => Padding(
                            padding:
                                const EdgeInsets.only(left: 8.0, bottom: 2.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  addon.name,
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFF7D8CA3),
                                  ),
                                ),
                                Text(
                                  '₹${addon.price.toStringAsFixed(2)}',
                                  style: const TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF2D4059),
                                  ),
                                ),
                              ],
                            ),
                          )),
                    ],
                  ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '₹${item.subtotal.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D4059),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleDetails(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Schedule',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: order.categoryColor.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.calendar_today,
                  size: 20,
                  color: order.categoryColor,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Service Date & Time',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${order.date.day}/${order.date.month}/${order.date.year} at ${order.time}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddressDetails(BuildContext context) {
    final address = order.address;
    final formattedAddress = address['formattedAddress'] ??
        '${address['street']}, ${address['city']}, ${address['state']} ${address['zipCode']}';

    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Service Address',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: order.categoryColor.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.location_on,
                  size: 20,
                  color: order.categoryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (address['name'] != null)
                      Text(
                        address['name'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                    if (address['name'] != null) const SizedBox(height: 4),
                    Text(
                      formattedAddress,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7D8CA3),
                      ),
                    ),
                    if (address['landmark'] != null) const SizedBox(height: 4),
                    if (address['landmark'] != null)
                      Text(
                        'Landmark: ${address['landmark']}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF7D8CA3),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 16),
          _buildPaymentRow('Subtotal', '₹${order.subtotal.toStringAsFixed(2)}'),
          const SizedBox(height: 8),
          _buildPaymentRow('Tax (5%)', '₹${order.tax.toStringAsFixed(2)}'),
          if (order.smallOrderFee > 0) const SizedBox(height: 8),
          if (order.smallOrderFee > 0)
            _buildPaymentRow('Small Order Fee',
                '₹${order.smallOrderFee.toStringAsFixed(2)}'),
          if (order.discount > 0) const SizedBox(height: 8),
          if (order.discount > 0)
            _buildPaymentRow(
                'Discount', '-₹${order.discount.toStringAsFixed(2)}',
                isDiscount: true),
          const SizedBox(height: 12),
          const Divider(color: Color(0xFFEEEEEE)),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total Amount',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D4059),
                ),
              ),
              Text(
                '₹${order.total.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: order.categoryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: order.categoryColor.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getPaymentIcon(order.paymentMethod),
                  size: 20,
                  color: order.categoryColor,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Payment Method',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    order.paymentMethod,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color:
                      _getPaymentStatusColor(order.paymentStatus).withAlpha(30),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  order.paymentStatus,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _getPaymentStatusColor(order.paymentStatus),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTechnicianDetails(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Technician Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: order.categoryColor.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  size: 20,
                  color: order.categoryColor,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Assigned Technician',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    order.technician!,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (order.technicianPhone != null)
            NeumorphicButton(
              onPressed: () => _callTechnician(context),
              color: order.categoryColor,
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.call,
                    color: Colors.white,
                    size: 18,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Call Technician',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Notes',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            order.notes!,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF7D8CA3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCancellationReason(BuildContext context) {
    return NeumorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cancellation Reason',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            order.cancellationReason ?? 'No reason provided',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFFF44336),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Only show cancel button for pending orders
        if (order.status == OrderStatus.pending)
          NeumorphicButton(
            onPressed: () => _showCancelDialog(context),
            color: Colors.red,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cancel,
                  color: Colors.white,
                  size: 18,
                ),
                SizedBox(width: 8),
                Text(
                  'Cancel Order',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        if (order.status == OrderStatus.pending) const SizedBox(height: 12),
        NeumorphicButton(
          onPressed: () => _navigateToChatSupport(context),
          color: Colors.black,
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.headset_mic,
                color: Colors.white,
                size: 18,
              ),
              SizedBox(width: 8),
              Text(
                'Contact Support',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentRow(String label, String value,
      {bool isDiscount = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF7D8CA3),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDiscount ? Colors.green : const Color(0xFF2D4059),
          ),
        ),
      ],
    );
  }

  IconData _getPaymentIcon(String paymentMethod) {
    switch (paymentMethod) {
      case 'Cash on Delivery':
        return Icons.money;
      case 'Credit/Debit Card':
        return Icons.credit_card;
      case 'Google Pay':
        return Icons.account_balance_wallet;
      case 'PhonePe':
        return Icons.account_balance_wallet;
      case 'Paytm':
        return Icons.account_balance_wallet;
      default:
        return Icons.payment;
    }
  }

  Color _getPaymentStatusColor(String status) {
    switch (status) {
      case 'Paid':
        return const Color(0xFF4CAF50);
      case 'Pending':
        return const Color(0xFFFFA000);
      case 'Failed':
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF7D8CA3);
    }
  }

  void _shareOrderDetails(BuildContext context) {
    // Implementation for sharing order details
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing order details...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _callTechnician(BuildContext context) async {
    if (order.technicianPhone != null) {
      final Uri phoneUri = Uri(
        scheme: 'tel',
        path: order.technicianPhone,
      );
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not launch phone dialer'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  void _navigateToChatSupport(BuildContext context) {
    // Navigate to the chat support screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ChatSupportScreen(),
      ),
    );
  }

  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text(
          'Are you sure you want to cancel this order? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showCancellationReasonDialog(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _showCancellationReasonDialog(BuildContext context) {
    final TextEditingController reasonController = TextEditingController();
    String? selectedReason;

    final List<String> cancellationReasons = [
      'Change of plans',
      'Found a better service provider',
      'Emergency came up',
      'Service no longer needed',
      'Pricing concerns',
      'Scheduling conflict',
      'Other',
    ];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Cancellation Reason'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Please select a reason for cancellation:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 16),
                // Reason dropdown
                DropdownButtonFormField<String>(
                  value: selectedReason,
                  decoration: const InputDecoration(
                    labelText: 'Select Reason',
                    border: OutlineInputBorder(),
                  ),
                  items: cancellationReasons.map((reason) {
                    return DropdownMenuItem(
                      value: reason,
                      child: Text(reason),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedReason = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                // Additional comments field
                TextField(
                  controller: reasonController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Additional Comments (Optional)',
                    hintText: 'Please provide any additional details...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Back'),
            ),
            ElevatedButton(
              onPressed: selectedReason != null
                  ? () {
                      Navigator.pop(context);

                      // Show success message with reason
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Order cancelled successfully.\nReason: $selectedReason',
                          ),
                          backgroundColor: Colors.green,
                          duration: const Duration(seconds: 3),
                        ),
                      );

                      // Log the cancellation reason for future API integration
                      print(
                          'Order ${order.id} cancelled with reason: $selectedReason');
                      if (reasonController.text.trim().isNotEmpty) {
                        print(
                            'Additional comments: ${reasonController.text.trim()}');
                      }

                      Navigator.pop(context); // Go back to previous screen
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Submit & Cancel'),
            ),
          ],
        ),
      ),
    );
  }
}
