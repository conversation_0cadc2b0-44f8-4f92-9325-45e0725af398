import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';
import '../widgets/dodo_logo.dart';
import '../providers/auth_provider.dart';
import '../screens/main_navigation_screen.dart';

class NeumorphicLoginScreen extends StatefulWidget {
  const NeumorphicLoginScreen({super.key});

  @override
  State<NeumorphicLoginScreen> createState() => _NeumorphicLoginScreenState();
}

class _NeumorphicLoginScreenState extends State<NeumorphicLoginScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(
    6,
    (index) => FocusNode(),
  );

  // State variables
  bool _isLoading = false;
  bool _otpSent = false;
  int _resendCountdown = 0;
  Timer? _timer;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();

    // Initialize auth provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AuthProvider>(context, listen: false).initialize();
    });
  }

  @override
  void dispose() {
    _mobileController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startResendTimer() {
    _timer?.cancel();
    setState(() {
      _resendCountdown = 30;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _sendOtp() async {
    print("_sendOtp called");
    if (_formKey.currentState!.validate()) {
      print("Form validation passed");
      setState(() {
        _isLoading = true;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      print(
          "Calling authProvider.sendOtp with mobile: ${_mobileController.text}");
      final success = await authProvider.sendOtp(_mobileController.text);
      print("sendOtp result: $success");
      print("Auth status: ${authProvider.status}");
      print("Mock OTP: ${authProvider.mockOtp}");

      if (mounted) {
        print("Setting state - _otpSent will be set to: $success");
        setState(() {
          _isLoading = false;
          if (success) {
            _otpSent = true;
            print("_otpSent is now: $_otpSent");
            _startResendTimer();

            // Auto-fill OTP fields for testing
            if (authProvider.mockOtp != null &&
                authProvider.mockOtp!.length == 6) {
              print("Auto-filling OTP fields with: ${authProvider.mockOtp}");
              for (int i = 0; i < 6; i++) {
                _otpControllers[i].text = authProvider.mockOtp![i];
              }
            } else {
              print(
                  "Not auto-filling OTP fields. mockOtp: ${authProvider.mockOtp}");
            }
          } else {
            print("Success was false, not setting _otpSent to true");
          }
        });

        // Show success message
        if (success) {
          String message = 'OTP sent successfully.';
          if (authProvider.mockOtp != null) {
            message += ' Use ${authProvider.mockOtp} for testing.';
          }

          print("Showing success message: $message");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
            ),
          );
        } else if (authProvider.errorMessage != null) {
          print("Showing error message: ${authProvider.errorMessage}");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authProvider.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      print("Form validation failed");
    }
  }

  void _verifyOtp() async {
    // Validate OTP fields
    bool isValid = true;
    for (var controller in _otpControllers) {
      if (controller.text.isEmpty) {
        isValid = false;
        break;
      }
    }

    if (!isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter the complete OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final otp = _otpControllers.map((controller) => controller.text).join();
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.verifyOtp(otp);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        // Navigate to main screen on successful verification
        Navigator.of(context).pushAndRemoveUntil(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const MainNavigationScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOutCubic;
              var tween = Tween(begin: begin, end: end).chain(
                CurveTween(curve: curve),
              );
              var offsetAnimation = animation.drive(tween);
              return SlideTransition(position: offsetAnimation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 800),
          ),
          (route) => false, // Remove all previous routes
        );
      } else if (authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resendOtp() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.resendOtp();

    if (mounted) {
      setState(() {
        _isLoading = false;

        // Auto-fill OTP fields for testing
        if (success &&
            authProvider.mockOtp != null &&
            authProvider.mockOtp!.length == 6) {
          for (int i = 0; i < 6; i++) {
            _otpControllers[i].text = authProvider.mockOtp![i];
          }
        }
      });

      if (success) {
        _startResendTimer();

        String message = 'OTP resent successfully.';
        if (authProvider.mockOtp != null) {
          message += ' Use ${authProvider.mockOtp} for testing.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      } else if (authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print("Building login screen, _otpSent: $_otpSent");
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: size.height * 0.05),
                    // Logo and App Name
                    Center(
                      child: NeumorphicContainer(
                        width: 150,
                        height: 150,
                        borderRadius: 20,
                        padding: EdgeInsets.zero,
                        child: const DodoLogo(
                          size: 150,
                          showText: false,
                          textColor: Colors.black,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'DODO Booker',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Your home services, simplified',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF7D8CA3),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: size.height * 0.06),

                    // Mobile Number Field
                    NeumorphicContainer(
                      child: TextFormField(
                        controller: _mobileController,
                        keyboardType: TextInputType.phone,
                        enabled: !_otpSent, // Disable when OTP is sent
                        decoration: const InputDecoration(
                          hintText: 'Mobile Number',
                          prefixIcon: Icon(Icons.phone_android,
                              color: Color(0xFF7D8CA3)),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(vertical: 16),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your mobile number';
                          }
                          // Basic validation for Indian mobile numbers
                          if (!RegExp(r'^(\+91[\-\s]?)?[0]?(91)?[6789]\d{9}$')
                              .hasMatch(value)) {
                            return 'Please enter a valid mobile number';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          // Format the mobile number to ensure it has the country code
                          if (value.isNotEmpty && !value.startsWith('+91')) {
                            if (value.startsWith('91')) {
                              _mobileController.text = '+$value';
                              _mobileController.selection =
                                  TextSelection.fromPosition(
                                TextPosition(
                                    offset: _mobileController.text.length),
                              );
                            } else if (value.length >= 10 &&
                                !value.startsWith('+') &&
                                !value.startsWith('91')) {
                              _mobileController.text = '+91$value';
                              _mobileController.selection =
                                  TextSelection.fromPosition(
                                TextPosition(
                                    offset: _mobileController.text.length),
                              );
                            }
                          }
                        },
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Mobile number info text
                    if (!_otpSent)
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          'We will send you a one-time password (OTP) to verify your mobile number',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF7D8CA3),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // OTP Input Fields (shown after OTP is sent)
                    if (_otpSent) ...[
                      const SizedBox(height: 20),
                      const Text(
                        'Enter the OTP sent to your mobile',
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xFF7D8CA3),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(
                          6,
                          (index) => SizedBox(
                            width: 45,
                            height: 55,
                            child: NeumorphicContainer(
                              padding: EdgeInsets.zero,
                              child: TextFormField(
                                controller: _otpControllers[index],
                                focusNode: _focusNodes[index],
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                maxLength: 1,
                                decoration: const InputDecoration(
                                  counterText: '',
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.zero,
                                ),
                                onChanged: (value) {
                                  if (value.isNotEmpty && index < 5) {
                                    _focusNodes[index + 1].requestFocus();
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      // Resend OTP option
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            "Didn't receive the OTP? ",
                            style: TextStyle(
                              color: Color(0xFF7D8CA3),
                            ),
                          ),
                          TextButton(
                            onPressed: _resendCountdown > 0 ? null : _resendOtp,
                            style: TextButton.styleFrom(
                              foregroundColor: Theme.of(context).primaryColor,
                            ),
                            child: Text(
                              _resendCountdown > 0
                                  ? 'Resend in $_resendCountdown s'
                                  : 'Resend OTP',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: _resendCountdown > 0
                                    ? const Color(0xFF7D8CA3)
                                    : Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],

                    const SizedBox(height: 24),

                    // Button (Send OTP or Verify OTP)
                    NeumorphicButton(
                      onPressed: _isLoading
                          ? null
                          : (_otpSent ? _verifyOtp : _sendOtp),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              _otpSent ? 'Verify' : 'Send OTP',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                    ),

                    const SizedBox(height: 24),

                    // Error message
                    Consumer<AuthProvider>(
                      builder: (ctx, auth, _) {
                        if (auth.errorMessage != null &&
                            auth.status == AuthStatus.error) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: Text(
                              auth.errorMessage!,
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),

                    const SizedBox(height: 24),

                    // Info text
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                        'By continuing, you agree to our Terms of Service and Privacy Policy',
                        style: TextStyle(
                          color: Color(0xFF7D8CA3),
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
