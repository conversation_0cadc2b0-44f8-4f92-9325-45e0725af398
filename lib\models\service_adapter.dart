import 'package:flutter/material.dart';
import 'cleaning_product.dart';
import 'service.dart';
import 'service_category.dart';

// Adapter to convert CleaningProduct to Service
Service convertToService(CleaningProduct product) {
  return Service(
    id: product.id,
    categoryId: product.categoryId,
    name: product.name,
    description: product.description,
    price: product.price,
    rating: product.rating,
    reviewCount: product.reviews,
    imageUrl: product.imageUrl,
    durationMinutes: 60, // Default duration
  );
}

// Create a simple ServiceCategory from a CleaningProduct
ServiceCategory createServiceCategory(CleaningProduct product) {
  return ServiceCategory(
    id: product.categoryId,
    name: product.name.split(' ').first, // Simple category name
    icon: Icons.cleaning_services,
    color: Colors.blue,
  );
}
