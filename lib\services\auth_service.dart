import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../models/user_model.dart' as models;
import '../models/api_response.dart';
import '../config/supabase_config.dart';
import 'supabase_service.dart';

class AuthService {
  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // User data storage key
  static const String _userDataKey = 'user_data';

  // Supabase service
  final SupabaseService _supabaseService = SupabaseService();

  // Flag to determine which backend to use
  bool _useSupabase = false; // Set to true to use Supabase, false for mock API
  // Note: Set to false until phone authentication is configured in Supabase

  // Method to change backend preference
  void setUseSupabase(bool useSupabase) {
    _useSupabase = useSupabase;
    print(
        "AuthService: Backend changed to ${useSupabase ? 'Supabase' : 'Mock API'}");
  }

  // Initialize the service
  Future<void> initialize() async {
    if (_useSupabase) {
      await _supabaseService.initialize();
    }
  }

  // Send OTP for customer login
  Future<ApiResponse<String>> sendOtp(String mobile) async {
    print("AuthService.sendOtp: _useSupabase = $_useSupabase");
    if (_useSupabase) {
      try {
        await _supabaseService.signInWithOTP(mobile);
        return ApiResponse(
          status: 'success',
          message: 'OTP sent successfully. Use 123456 for testing.',
          data: '123456', // Mock OTP for testing
        );
      } catch (e) {
        return ApiResponse(
          status: 'error',
          message: 'Failed to send OTP: $e',
          data: null,
        );
      }
    } else {
      // Fallback to mock API - return mock response
      await Future.delayed(
          const Duration(seconds: 1)); // Simulate network delay
      return ApiResponse(
        status: 'success',
        message: 'OTP sent successfully. Use 123456 for testing.',
        data: '123456',
      );
    }
  }

  // Resend OTP for customer login
  Future<ApiResponse<String>> resendOtp(String mobile) async {
    // Same logic as sendOtp
    return await sendOtp(mobile);
  }

  // Google Sign-in
  Future<ApiResponse<models.User>> signInWithGoogle() async {
    if (_useSupabase) {
      try {
        final authResponse = await _supabaseService.signInWithGoogle();

        if (authResponse.user != null) {
          final supabaseUser = authResponse.user!;

          // Check if user profile exists
          var userProfile =
              await _supabaseService.getUserProfile(supabaseUser.id);

          if (userProfile == null) {
            // Create new user profile from Google data
            userProfile =
                await _supabaseService.updateUserProfile(supabaseUser.id, {
              'name': supabaseUser.userMetadata?['full_name'] ??
                  supabaseUser.userMetadata?['name'] ??
                  'User',
              'email': supabaseUser.email ?? '',
              'phone': supabaseUser.phone ?? '',
              'profile_pic': supabaseUser.userMetadata?['avatar_url'] ??
                  supabaseUser.userMetadata?['picture'] ??
                  '',
              'is_profile_complete': true,
            });
          }

          // Convert to our User model
          final user = models.User(
            id: supabaseUser.id,
            mobile: userProfile['phone'],
            userType: 'CUSTOMER',
            firstName: userProfile['name'] ?? 'User',
            email: userProfile['email'],
          );

          // Store user data locally
          await _saveUserData(user);

          return ApiResponse(
            status: 'success',
            message: 'Google sign-in successful',
            data: user,
          );
        }

        return ApiResponse(
          status: 'error',
          message: 'Google authentication failed',
          data: null,
        );
      } catch (e) {
        return ApiResponse(
          status: 'error',
          message: 'Google sign-in failed: $e',
          data: null,
        );
      }
    } else {
      // Fallback to mock Google authentication
      await Future.delayed(const Duration(seconds: 2));

      final user = models.User(
        id: 'google_user_${DateTime.now().millisecondsSinceEpoch}',
        mobile: '',
        userType: 'CUSTOMER',
        firstName: 'Google User',
        email: '<EMAIL>',
      );

      await _saveUserData(user);

      return ApiResponse(
        status: 'success',
        message: 'Google sign-in successful (mock)',
        data: user,
      );
    }
  }

  // Verify OTP and login
  Future<ApiResponse<models.User>> verifyOtp(String mobile, String otp) async {
    if (_useSupabase) {
      try {
        // For testing, accept 123456 as valid OTP
        if (otp != '123456') {
          return ApiResponse(
            status: 'error',
            message: 'Invalid OTP. Use 123456 for testing.',
            data: null,
          );
        }

        final authResponse = await _supabaseService.verifyOTP(mobile, otp);

        if (authResponse.user != null) {
          // Create or get user profile
          final supabaseUser = authResponse.user!;

          // Check if user profile exists
          var userProfile =
              await _supabaseService.getUserProfile(supabaseUser.id);

          if (userProfile == null) {
            // Create new user profile
            userProfile =
                await _supabaseService.updateUserProfile(supabaseUser.id, {
              'name': 'User',
              'phone': mobile,
              'email': supabaseUser.email ?? '$<EMAIL>',
              'is_profile_complete': false,
            });
          }

          // Convert to our User model
          final user = models.User(
            id: supabaseUser.id,
            mobile: mobile,
            userType: 'CUSTOMER',
            firstName: userProfile['name'] ?? 'User',
            email: userProfile['email'],
          );

          // Store user data locally
          await _saveUserData(user);

          return ApiResponse(
            status: 'success',
            message: 'Login successful',
            data: user,
          );
        }

        return ApiResponse(
          status: 'error',
          message: 'Authentication failed',
          data: null,
        );
      } catch (e) {
        return ApiResponse(
          status: 'error',
          message: 'Failed to verify OTP: $e',
          data: null,
        );
      }
    } else {
      // Fallback to mock authentication
      await Future.delayed(const Duration(seconds: 1));

      if (otp != '123456') {
        return ApiResponse(
          status: 'error',
          message: 'Invalid OTP. Use 123456 for testing.',
          data: null,
        );
      }

      final user = models.User(
        id: 'user_$mobile',
        mobile: mobile,
        userType: 'CUSTOMER',
        firstName: 'Demo User',
        email: '$<EMAIL>',
      );

      await _saveUserData(user);

      return ApiResponse(
        status: 'success',
        message: 'Login successful',
        data: user,
      );
    }
  }

  // Get user info
  Future<ApiResponse<models.User>> getUserInfo() async {
    if (_useSupabase) {
      try {
        final currentUser = _supabaseService.currentUser;
        if (currentUser == null) {
          return ApiResponse(
            status: 'error',
            message: 'Not authenticated',
            data: null,
          );
        }

        final userProfile =
            await _supabaseService.getUserProfile(currentUser.id);
        if (userProfile != null) {
          final user = models.User(
            id: currentUser.id,
            mobile: userProfile['phone'],
            userType: 'CUSTOMER',
            firstName: userProfile['name'],
            email: userProfile['email'],
          );

          await _saveUserData(user);
          return ApiResponse(
            status: 'success',
            message: 'User info retrieved',
            data: user,
          );
        }

        return ApiResponse(
          status: 'error',
          message: 'User profile not found',
          data: null,
        );
      } catch (e) {
        return ApiResponse(
          status: 'error',
          message: 'Failed to get user info: $e',
          data: null,
        );
      }
    } else {
      // Fallback to local storage
      final user = await getCurrentUser();
      if (user != null) {
        return ApiResponse(
          status: 'success',
          message: 'User info retrieved',
          data: user,
        );
      }

      return ApiResponse(
        status: 'error',
        message: 'Not authenticated',
        data: null,
      );
    }
  }

  // Logout
  Future<void> logout() async {
    print("AuthService: Starting logout process");
    try {
      if (_useSupabase) {
        await _supabaseService.signOut();
      }
      print("AuthService: Logout successful");
    } catch (e) {
      // Ignore errors, we'll clear local storage anyway
      print("AuthService: Error during logout: $e");
    } finally {
      // Clear local storage
      await _clearUserData();
      print("AuthService: Local storage cleared");
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    if (_useSupabase) {
      return _supabaseService.currentUser != null;
    } else {
      final user = await getCurrentUser();
      return user != null;
    }
  }

  // Get current user
  Future<models.User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(_userDataKey);

    if (userData != null) {
      return models.User.fromJson(jsonDecode(userData));
    }

    return null;
  }

  // Save user data to shared preferences
  Future<void> _saveUserData(models.User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDataKey, jsonEncode(user.toJson()));
  }

  // Clear user data from shared preferences
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userDataKey);
  }
}
