import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../models/user_model.dart' as models;
import '../models/api_response.dart';
import '../config/supabase_config.dart';
import 'supabase_service.dart';

class AuthService {
  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // User data storage key
  static const String _userDataKey = 'user_data';

  // Supabase service
  final SupabaseService _supabaseService = SupabaseService();

  // Always use Supabase for Google authentication only
  bool _useSupabase = true; // Always true - using Google OAuth only

  // Initialize the service
  Future<void> initialize() async {
    if (_useSupabase) {
      await _supabaseService.initialize();
    }
  }

  // Sign up with email and password
  Future<ApiResponse<models.User>> signUp(
      String email, String password, String name) async {
    try {
      final authResponse = await _supabaseService.signUp(email, password);

      if (authResponse.user != null) {
        final supabaseUser = authResponse.user!;

        try {
          // Create user profile
          await _supabaseService.updateUserProfile(supabaseUser.id, {
            'name': name,
            'email': email,
            'phone': '',
            'profile_pic': '',
            'is_profile_complete': true,
          });

          // Convert to our User model
          final user = models.User(
            id: supabaseUser.id,
            mobile: '',
            userType: 'CUSTOMER',
            firstName: name,
            email: email,
          );

          // Store user data locally
          await _saveUserData(user);

          return ApiResponse(
            status: 'success',
            message: 'Account created successfully',
            data: user,
          );
        } catch (profileError) {
          // If profile creation fails, still return success but with basic user data
          final user = models.User(
            id: supabaseUser.id,
            mobile: '',
            userType: 'CUSTOMER',
            firstName: name,
            email: email,
          );

          // Store user data locally
          await _saveUserData(user);

          return ApiResponse(
            status: 'success',
            message: 'Account created successfully',
            data: user,
          );
        }
      }

      return ApiResponse(
        status: 'error',
        message: 'Failed to create account',
        data: null,
      );
    } catch (e) {
      String errorMessage = 'Sign up failed';

      // Handle specific Supabase errors
      if (e.toString().contains('over_email_send_rate_limit')) {
        errorMessage = 'Please wait a moment before trying again';
      } else if (e.toString().contains('User already registered')) {
        errorMessage = 'An account with this email already exists';
      } else if (e.toString().contains('Invalid email')) {
        errorMessage = 'Please enter a valid email address';
      } else if (e.toString().contains('Password should be at least')) {
        errorMessage = 'Password should be at least 6 characters';
      }

      return ApiResponse(
        status: 'error',
        message: errorMessage,
        data: null,
      );
    }
  }

  // Sign in with email and password
  Future<ApiResponse<models.User>> signIn(String email, String password) async {
    try {
      final authResponse = await _supabaseService.signIn(email, password);

      if (authResponse.user != null) {
        final supabaseUser = authResponse.user!;

        // Get user profile
        var userProfile =
            await _supabaseService.getUserProfile(supabaseUser.id);

        if (userProfile == null) {
          // Create basic profile if it doesn't exist
          userProfile =
              await _supabaseService.updateUserProfile(supabaseUser.id, {
            'name': 'User',
            'email': email,
            'phone': '',
            'profile_pic': '',
            'is_profile_complete': false,
          });
        }

        // Convert to our User model
        final user = models.User(
          id: supabaseUser.id,
          mobile: userProfile['phone'] ?? '',
          userType: 'CUSTOMER',
          firstName: userProfile['name'] ?? 'User',
          email: userProfile['email'] ?? email,
        );

        // Store user data locally
        await _saveUserData(user);

        return ApiResponse(
          status: 'success',
          message: 'Login successful',
          data: user,
        );
      }

      return ApiResponse(
        status: 'error',
        message: 'Invalid email or password',
        data: null,
      );
    } catch (e) {
      String errorMessage = 'Login failed';

      // Handle specific Supabase errors
      if (e.toString().contains('Invalid login credentials')) {
        errorMessage = 'Invalid email or password';
      } else if (e.toString().contains('Email not confirmed') ||
          e.toString().contains('email_not_confirmed') ||
          e.toString().contains('signup_disabled')) {
        // For development: Try to create a temporary user session
        try {
          print('Handling unconfirmed email for development...');

          final tempUser = models.User(
            id: 'dev_${DateTime.now().millisecondsSinceEpoch}',
            mobile: '',
            userType: 'CUSTOMER',
            firstName: 'User',
            email: email,
          );

          await _saveUserData(tempUser);

          return ApiResponse(
            status: 'success',
            message: 'Login successful (development mode)',
            data: tempUser,
          );
        } catch (devError) {
          print('Development login failed: $devError');
          errorMessage = 'Please check your email and confirm your account';
        }
      } else if (e.toString().contains('over_email_send_rate_limit')) {
        errorMessage = 'Please wait a moment before trying again';
      }

      return ApiResponse(
        status: 'error',
        message: errorMessage,
        data: null,
      );
    }
  }

  // Get user info
  Future<ApiResponse<models.User>> getUserInfo() async {
    if (_useSupabase) {
      try {
        final currentUser = _supabaseService.currentUser;
        if (currentUser == null) {
          return ApiResponse(
            status: 'error',
            message: 'Not authenticated',
            data: null,
          );
        }

        final userProfile =
            await _supabaseService.getUserProfile(currentUser.id);
        if (userProfile != null) {
          final user = models.User(
            id: currentUser.id,
            mobile: userProfile['phone'],
            userType: 'CUSTOMER',
            firstName: userProfile['name'],
            email: userProfile['email'],
          );

          await _saveUserData(user);
          return ApiResponse(
            status: 'success',
            message: 'User info retrieved',
            data: user,
          );
        }

        return ApiResponse(
          status: 'error',
          message: 'User profile not found',
          data: null,
        );
      } catch (e) {
        return ApiResponse(
          status: 'error',
          message: 'Failed to get user info: $e',
          data: null,
        );
      }
    } else {
      // Fallback to local storage
      final user = await getCurrentUser();
      if (user != null) {
        return ApiResponse(
          status: 'success',
          message: 'User info retrieved',
          data: user,
        );
      }

      return ApiResponse(
        status: 'error',
        message: 'Not authenticated',
        data: null,
      );
    }
  }

  // Logout
  Future<void> logout() async {
    print("AuthService: Starting logout process");
    try {
      if (_useSupabase) {
        await _supabaseService.signOut();
      }
      print("AuthService: Logout successful");
    } catch (e) {
      // Ignore errors, we'll clear local storage anyway
      print("AuthService: Error during logout: $e");
    } finally {
      // Clear local storage
      await _clearUserData();
      print("AuthService: Local storage cleared");
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    if (_useSupabase) {
      return _supabaseService.currentUser != null;
    } else {
      final user = await getCurrentUser();
      return user != null;
    }
  }

  // Get current user
  Future<models.User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(_userDataKey);

    if (userData != null) {
      return models.User.fromJson(jsonDecode(userData));
    }

    return null;
  }

  // Save user data to shared preferences
  Future<void> _saveUserData(models.User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDataKey, jsonEncode(user.toJson()));
  }

  // Clear user data from shared preferences
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userDataKey);
  }
}
