import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/api_response.dart';
import 'http_client.dart';

class AuthService {
  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // User data storage key
  static const String _userDataKey = 'user_data';

  // HTTP client
  final HttpClient _httpClient = HttpClient();

  // Send OTP for customer login
  Future<ApiResponse<String>> sendOtp(String mobile) async {
    return await _httpClient.post(
      '/api/auth/customer/otp/send/',
      body: {
        'mobile': mobile,
        'user_type': 'CUSTOMER',
      },
      requiresAuth: false,
    );
  }

  // Resend OTP for customer login
  Future<ApiResponse<String>> resendOtp(String mobile) async {
    return await _httpClient.post(
      '/api/auth/customer/otp/resend/',
      body: {
        'mobile': mobile,
        'user_type': 'CUSTOMER',
      },
      requiresAuth: false,
    );
  }

  // Verify OTP and login
  Future<ApiResponse<User>> verifyOtp(String mobile, String otp) async {
    final response = await _httpClient.post(
      '/api/auth/customer/otp/verify/',
      body: {
        'mobile': mobile,
        'otp': otp,
        'user_type': 'CUSTOMER',
      },
      requiresAuth: false,
    );

    if (response.isSuccess && response.data != null) {
      // Extract user data and tokens
      final data = response.data as Map<String, dynamic>;
      final user = User.fromJson(data['user']);

      // Store tokens
      if (data['access'] != null && data['refresh'] != null) {
        await _httpClient.saveTokens(
          data['access'],
          data['refresh'],
        );
      }

      // Store user data
      await _saveUserData(user);

      return ApiResponse(
        status: 'success',
        message: 'Registration successful',
        data: user,
      );
    }

    return ApiResponse(
      status: response.status,
      message: response.message,
      data: null,
    );
  }

  // Get user info
  Future<ApiResponse<User>> getUserInfo() async {
    final token = await _httpClient.getAccessToken();

    if (token == null) {
      return ApiResponse(
        status: 'error',
        message: 'Not authenticated',
        data: null,
      );
    }

    final response = await _httpClient.get(
      '/api/auth/user/info/',
      fromJson: (data) => User.fromJson(data),
    );

    if (response.isSuccess && response.data != null) {
      await _saveUserData(response.data!);
    }

    return response;
  }

  // Logout
  Future<void> logout() async {
    print("AuthService: Starting logout process");
    try {
      // Call logout API endpoint to clear cookies
      final response = await _httpClient.post(
        '/api/auth/token/cookie/logout/',
        body: null,
      );
      print("AuthService: Logout API response: ${response.status}");
    } catch (e) {
      // Ignore errors, we'll clear local storage anyway
      print("AuthService: Error during logout API call: $e");
    } finally {
      // Clear local storage
      await _httpClient.clearTokens();
      await _clearUserData();
      print("AuthService: Local storage cleared");
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await _httpClient.getAccessToken();
    return token != null;
  }

  // Get current user
  Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(_userDataKey);

    if (userData != null) {
      return User.fromJson(jsonDecode(userData));
    }

    return null;
  }

  // Save user data to shared preferences
  Future<void> _saveUserData(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDataKey, jsonEncode(user.toJson()));
  }

  // Clear user data from shared preferences
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userDataKey);
  }
}
