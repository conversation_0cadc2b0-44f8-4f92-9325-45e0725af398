import 'package:flutter/foundation.dart';
import '../models/cart_item.dart';
import '../models/service.dart';
import '../models/service_category.dart';
import '../models/service_addon.dart';
import '../models/coupon_model.dart';
import '../services/supabase_service.dart';

class CartProvider with ChangeNotifier {
  final List<CartItem> _items = [];
  CouponModel? _appliedCoupon;
  final SupabaseService _supabaseService = SupabaseService();
  String? _currentUserId;

  List<CartItem> get items => [..._items];
  CouponModel? get appliedCoupon => _appliedCoupon;

  int get itemCount => _items.length;

  // Set current user for Supabase operations
  void setCurrentUser(String? userId) {
    _currentUserId = userId;
  }

  double get subtotal {
    return _items.fold(0.0, (sum, item) => sum + item.subtotal);
  }

  double get tax {
    return _items.fold(0.0, (sum, item) => sum + item.tax);
  }

  double get smallOrderFee {
    // Add small order fee of ₹150 if subtotal is less than ₹300
    return subtotal < 300 ? 150.0 : 0.0;
  }

  double get couponDiscount {
    if (_appliedCoupon == null) return 0.0;
    final calculatedDiscount =
        subtotal * (_appliedCoupon!.discountPercentage / 100);
    return calculatedDiscount > _appliedCoupon!.maxDiscount
        ? _appliedCoupon!.maxDiscount
        : calculatedDiscount;
  }

  double get total {
    return _items.fold(0.0, (sum, item) => sum + item.total) +
        smallOrderFee -
        couponDiscount;
  }

  Future<void> addItem({
    required Service service,
    required ServiceCategory category,
    required int quantity,
    required DateTime date,
    required String time,
    Map<String, dynamic>? address,
    List<ServiceAddon>? addons,
  }) async {
    // Check if the service is already in the cart
    final existingIndex =
        _items.indexWhere((item) => item.service.id == service.id);

    if (existingIndex >= 0) {
      // Update existing item
      _items[existingIndex] = CartItem(
        id: _items[existingIndex].id,
        service: service,
        category: category,
        quantity: quantity,
        date: date,
        time: time,
        address: address ?? _items[existingIndex].address,
        addons: addons ?? _items[existingIndex].addons,
      );
    } else {
      // Add new item
      _items.add(
        CartItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          service: service,
          category: category,
          quantity: quantity,
          date: date,
          time: time,
          address: address,
          addons: addons ?? [],
        ),
      );
    }

    // Sync to Supabase if user is logged in
    if (_currentUserId != null) {
      try {
        await _supabaseService.addToCart(_currentUserId!, service.id, quantity);
        print('Cart item synced to Supabase: ${service.name}');
      } catch (e) {
        print('Failed to sync cart item to Supabase: $e');
        // Continue with local storage even if Supabase sync fails
      }
    }

    notifyListeners();
  }

  void addAddonToItem(String itemId, ServiceAddon addon) {
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final item = _items[index];
      final updatedAddons = [...item.addons];

      // Check if addon already exists
      final existingAddonIndex =
          updatedAddons.indexWhere((a) => a.id == addon.id);
      if (existingAddonIndex >= 0) {
        // Update quantity if addon already exists
        final existingAddon = updatedAddons[existingAddonIndex];
        updatedAddons[existingAddonIndex] = existingAddon.copyWith(
          quantity: existingAddon.quantity + 1,
        );
      } else {
        // Add new addon with quantity 1
        updatedAddons.add(addon);
      }

      _items[index] = CartItem(
        id: item.id,
        service: item.service,
        category: item.category,
        quantity: item.quantity,
        date: item.date,
        time: item.time,
        address: item.address,
        addons: updatedAddons,
      );

      notifyListeners();
    }
  }

  void removeAddonFromItem(String itemId, String addonId) {
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final item = _items[index];
      final updatedAddons = [...item.addons];

      updatedAddons.removeWhere((addon) => addon.id == addonId);

      _items[index] = CartItem(
        id: item.id,
        service: item.service,
        category: item.category,
        quantity: item.quantity,
        date: item.date,
        time: item.time,
        address: item.address,
        addons: updatedAddons,
      );

      notifyListeners();
    }
  }

  void updateAddonQuantity(String itemId, String addonId, int quantity) {
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final item = _items[index];
      final updatedAddons = [...item.addons];

      final addonIndex =
          updatedAddons.indexWhere((addon) => addon.id == addonId);
      if (addonIndex >= 0) {
        if (quantity <= 0) {
          // Remove addon if quantity is 0 or less
          updatedAddons.removeAt(addonIndex);
        } else {
          // Update addon quantity
          updatedAddons[addonIndex] = updatedAddons[addonIndex].copyWith(
            quantity: quantity,
          );
        }

        _items[index] = CartItem(
          id: item.id,
          service: item.service,
          category: item.category,
          quantity: item.quantity,
          date: item.date,
          time: item.time,
          address: item.address,
          addons: updatedAddons,
        );

        notifyListeners();
      }
    }
  }

  void removeItem(String id) {
    _items.removeWhere((item) => item.id == id);
    notifyListeners();
  }

  void updateQuantity(String id, int quantity) {
    final index = _items.indexWhere((item) => item.id == id);
    if (index >= 0) {
      _items[index] = CartItem(
        id: _items[index].id,
        service: _items[index].service,
        category: _items[index].category,
        quantity: quantity,
        date: _items[index].date,
        time: _items[index].time,
        address: _items[index].address,
        addons: _items[index].addons,
      );
      notifyListeners();
    }
  }

  Future<void> clear() async {
    _items.clear();
    _appliedCoupon = null;

    // Clear from Supabase if user is logged in
    if (_currentUserId != null) {
      try {
        await _supabaseService.clearCart(_currentUserId!);
        print('Cart cleared from Supabase');
      } catch (e) {
        print('Failed to clear cart from Supabase: $e');
        // Continue with local clear even if Supabase clear fails
      }
    }

    notifyListeners();
  }

  void applyCoupon(CouponModel coupon) {
    if (subtotal >= coupon.minOrderValue && coupon.isValid()) {
      _appliedCoupon = coupon;
      notifyListeners();
    }
  }

  void removeCoupon() {
    _appliedCoupon = null;
    notifyListeners();
  }

  bool isCouponApplicable(CouponModel coupon) {
    return subtotal >= coupon.minOrderValue && coupon.isValid();
  }

  // Check if a service is from the same category as existing items in cart
  bool isFromSameCategory(String categoryId) {
    if (_items.isEmpty) return true;

    // Get the category ID of the first item in cart
    final existingCategoryId = _items.first.category.id;

    // Check if the new service's category matches the existing one
    return categoryId == existingCategoryId;
  }
}
