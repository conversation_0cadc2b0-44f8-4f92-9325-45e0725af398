import 'package:flutter/foundation.dart';
import '../models/coupon_model.dart';

class CouponProvider with ChangeNotifier {
  List<CouponModel> _availableCoupons = [];
  CouponModel? _appliedCoupon;
  double _cartTotal = 0;

  List<CouponModel> get availableCoupons => _availableCoupons;
  CouponModel? get appliedCoupon => _appliedCoupon;
  double get discountAmount => _appliedCoupon != null 
      ? _appliedCoupon!.calculateDiscount(_cartTotal) 
      : 0;

  void setCartTotal(double total) {
    _cartTotal = total;
    notifyListeners();
  }

  void loadCoupons() {
    // In a real app, this would fetch from an API or database
    _availableCoupons = [
      CouponModel(
        id: '1',
        code: 'DIWALI25',
        title: 'Diwali Special',
        description: 'Get 25% off on all services during Diwali festival',
        discountPercentage: 25,
        maxDiscount: 500,
        minOrderValue: 1000,
        validUntil: DateTime.now().add(const Duration(days: 30)),
      ),
      CouponModel(
        id: '2',
        code: 'WELCOME15',
        title: 'Welcome Offer',
        description: 'Get 15% off on your first booking',
        discountPercentage: 15,
        maxDiscount: 300,
        minOrderValue: 500,
        validUntil: DateTime.now().add(const Duration(days: 60)),
      ),
      CouponModel(
        id: '3',
        code: 'SUMMER10',
        title: 'Summer Special',
        description: 'Get 10% off on all cleaning services',
        discountPercentage: 10,
        maxDiscount: 200,
        minOrderValue: 800,
        validUntil: DateTime.now().add(const Duration(days: 45)),
      ),
      CouponModel(
        id: '4',
        code: 'FLAT500',
        title: 'Flat ₹500 Off',
        description: 'Get flat ₹500 off on orders above ₹5000',
        discountPercentage: 100,
        maxDiscount: 500,
        minOrderValue: 5000,
        validUntil: DateTime.now().add(const Duration(days: 15)),
      ),
    ];
    notifyListeners();
  }

  bool applyCoupon(String code) {
    final coupon = _availableCoupons.firstWhere(
      (coupon) => coupon.code == code && coupon.isValid(),
      orElse: () => CouponModel(
        id: '',
        code: '',
        title: '',
        description: '',
        discountPercentage: 0,
        maxDiscount: 0,
        minOrderValue: 0,
        validUntil: DateTime.now(),
        isActive: false,
      ),
    );

    if (coupon.id.isEmpty) {
      return false;
    }

    if (_cartTotal < coupon.minOrderValue) {
      return false;
    }

    _appliedCoupon = coupon;
    notifyListeners();
    return true;
  }

  void removeCoupon() {
    _appliedCoupon = null;
    notifyListeners();
  }

  bool isCouponValid(CouponModel coupon) {
    return coupon.isValid() && _cartTotal >= coupon.minOrderValue;
  }

  double getDiscountForCoupon(CouponModel coupon) {
    return coupon.calculateDiscount(_cartTotal);
  }
}
