import 'package:flutter/material.dart';
import '../services/real_time_mock_api_service.dart';

/// Provider for real-time API data
class RealTimeApiProvider extends ChangeNotifier {
  final RealTimeMockApiService _apiService = RealTimeMockApiService();
  String? _error;

  // Getters
  String? get error => _error;

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set current user directly (for testing/mock purposes)
  void setCurrentUser(String userId, {String? mobile}) {
    _apiService.setCurrentUser(userId, mobile: mobile);
  }

  // Authentication
  bool _isAuthenticated = false;
  Map<String, dynamic>? _currentUser;

  bool get isAuthenticated => _isAuthenticated;
  Map<String, dynamic>? get currentUser => _currentUser;

  // Stream getters
  Stream<List<Map<String, dynamic>>> get categoriesStream =>
      _apiService.categoriesStream;

  Stream<List<Map<String, dynamic>>> get subcategoriesStream =>
      _apiService.subcategoriesStream;

  Stream<List<Map<String, dynamic>>> get productsStream =>
      _apiService.productsStream;

  Stream<Map<String, dynamic>> get cartStream => _apiService.cartStream;

  Stream<List<Map<String, dynamic>>> get bookingsStream =>
      _apiService.bookingsStream;

  Stream<Map<String, dynamic>> get profileStream => _apiService.profileStream;

  Stream<List<Map<String, dynamic>>> get chatMessagesStream =>
      _apiService.chatMessagesStream;

  Stream<List<Map<String, dynamic>>> get bannersStream =>
      _apiService.bannersStream;

  Stream<List<Map<String, dynamic>>> get notificationsStream =>
      _apiService.notificationsStream;

  Stream<List<Map<String, dynamic>>> get addressesStream =>
      _apiService.addressesStream;

  Stream<List<Map<String, dynamic>>> get couponsStream =>
      _apiService.couponsStream;

  Stream<List<Map<String, dynamic>>> get featuredServicesStream =>
      _apiService.featuredServicesStream;

  Stream<List<Map<String, dynamic>>> get recentServicesStream =>
      _apiService.recentServicesStream;

  // Constructor
  RealTimeApiProvider() {
    // Listen to auth stream
    _apiService.authStream.listen((authData) {
      _isAuthenticated = authData['isAuthenticated'] ?? false;
      _currentUser = authData['user'];
      notifyListeners();
    });
  }

  // Authentication methods
  Future<bool> sendOtp(String mobile) async {
    try {
      final response = await _apiService.sendOtp(mobile);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to send OTP';
        return false;
      }
    } catch (e) {
      _error = 'Error sending OTP: $e';
      return false;
    }
  }

  Future<bool> verifyOtp(String mobile, String otp) async {
    try {
      final response = await _apiService.verifyOtp(mobile, otp);
      if (response['status'] == 'success') {
        final userId = response['data']['userId'];
        _apiService.setCurrentUser(userId, mobile: mobile);
        return true;
      } else {
        _error = response['message'] ?? 'Failed to verify OTP';
        return false;
      }
    } catch (e) {
      _error = 'Error verifying OTP: $e';
      return false;
    }
  }

  Future<bool> logout() async {
    try {
      final response = await _apiService.logout();
      if (response['status'] == 'success') {
        // Clear local state
        _isAuthenticated = false;
        _currentUser = null;
        _error = null;

        // Notify listeners of the state change
        notifyListeners();

        return true;
      } else {
        _error = response['message'] ?? 'Failed to logout';
        return false;
      }
    } catch (e) {
      _error = 'Error logging out: $e';
      return false;
    }
  }

  // Category methods
  Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      final response = await _apiService.getCategories();
      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'] ?? 'Failed to get categories';
        return [];
      }
    } catch (e) {
      _error = 'Error getting categories: $e';
      return [];
    }
  }

  // Product methods
  Future<List<Map<String, dynamic>>> getProductsByCategory(
      String categoryId) async {
    try {
      final response = await _apiService.getProductsByCategory(categoryId);
      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'] ?? 'Failed to get products';
        return [];
      }
    } catch (e) {
      _error = 'Error getting products: $e';
      return [];
    }
  }

  Future<Map<String, dynamic>?> getProductDetails(String productId) async {
    try {
      final response = await _apiService.getProductDetails(productId);
      if (response['status'] == 'success') {
        return response['data'];
      } else {
        _error = response['message'] ?? 'Failed to get product details';
        return null;
      }
    } catch (e) {
      _error = 'Error getting product details: $e';
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    try {
      final response = await _apiService.searchProducts(query);
      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'] ?? 'Failed to search products';
        return [];
      }
    } catch (e) {
      _error = 'Error searching products: $e';
      return [];
    }
  }

  // Cart methods
  Future<bool> addToCart(Map<String, dynamic> product, int quantity) async {
    try {
      final response = await _apiService.addToCart(product['id'], quantity);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to add to cart';
        return false;
      }
    } catch (e) {
      _error = 'Error adding to cart: $e';
      return false;
    }
  }

  Future<bool> removeFromCart(String productId) async {
    try {
      final response = await _apiService.removeFromCart(productId);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to remove from cart';
        return false;
      }
    } catch (e) {
      _error = 'Error removing from cart: $e';
      return false;
    }
  }

  Future<bool> updateCartItemQuantity(String productId, int quantity) async {
    try {
      final response =
          await _apiService.updateCartItemQuantity(productId, quantity);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to update cart';
        return false;
      }
    } catch (e) {
      _error = 'Error updating cart: $e';
      return false;
    }
  }

  Future<bool> clearCart() async {
    try {
      final response = await _apiService.clearCart();
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to clear cart';
        return false;
      }
    } catch (e) {
      _error = 'Error clearing cart: $e';
      return false;
    }
  }

  Future<bool> applyCoupon(String couponCode) async {
    try {
      final response = await _apiService.applyCoupon(couponCode);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to apply coupon';
        return false;
      }
    } catch (e) {
      _error = 'Error applying coupon: $e';
      return false;
    }
  }

  Future<bool> removeCoupon() async {
    try {
      final response = await _apiService.removeCoupon();
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to remove coupon';
        return false;
      }
    } catch (e) {
      _error = 'Error removing coupon: $e';
      return false;
    }
  }

  // Booking methods
  Future<bool> createBooking(Map<String, dynamic> bookingData) async {
    try {
      final response = await _apiService.createBooking(bookingData);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to create booking';
        return false;
      }
    } catch (e) {
      _error = 'Error creating booking: $e';
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> getBookings() async {
    try {
      final response = await _apiService.getBookings();
      print('RealTimeApiProvider: getBookings response: $response');

      if (response['status'] == 'success') {
        final bookings = List<Map<String, dynamic>>.from(response['data']);
        print('RealTimeApiProvider: Processed ${bookings.length} bookings');
        return bookings;
      } else {
        _error = response['message'] ?? 'Failed to get bookings';
        print('RealTimeApiProvider: Error - ${_error}');
        return [];
      }
    } catch (e) {
      _error = 'Error getting bookings: $e';
      print('RealTimeApiProvider: Exception - $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> getBookingDetails(String bookingId) async {
    try {
      final response = await _apiService.getBookingDetails(bookingId);
      if (response['status'] == 'success') {
        return response['data'];
      } else {
        _error = response['message'] ?? 'Failed to get booking details';
        return null;
      }
    } catch (e) {
      _error = 'Error getting booking details: $e';
      return null;
    }
  }

  Future<bool> cancelBooking(String bookingId) async {
    try {
      final response = await _apiService.cancelBooking(bookingId);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to cancel booking';
        return false;
      }
    } catch (e) {
      _error = 'Error cancelling booking: $e';
      return false;
    }
  }

  Future<bool> rateBooking(
      String bookingId, double rating, String? review) async {
    try {
      final response = await _apiService.rateBooking(bookingId, rating, review);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to rate booking';
        return false;
      }
    } catch (e) {
      _error = 'Error rating booking: $e';
      return false;
    }
  }

  // Profile methods
  Future<Map<String, dynamic>?> getProfile() async {
    try {
      final response = await _apiService.getProfile();
      if (response['status'] == 'success') {
        return response['data'];
      } else {
        _error = response['message'] ?? 'Failed to get profile';
        return null;
      }
    } catch (e) {
      _error = 'Error getting profile: $e';
      return null;
    }
  }

  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    try {
      print(
          'RealTimeApiProvider: updateProfile called with data: $profileData');
      final response = await _apiService.updateProfile(profileData);
      print('RealTimeApiProvider: updateProfile response: $response');

      if (response['status'] == 'success') {
        print('RealTimeApiProvider: updateProfile success');
        return true;
      } else {
        _error = response['message'] ?? 'Failed to update profile';
        print('RealTimeApiProvider: updateProfile failed: $_error');
        return false;
      }
    } catch (e) {
      _error = 'Error updating profile: $e';
      print('RealTimeApiProvider: updateProfile exception: $e');
      return false;
    }
  }

  // Chat methods
  Future<List<Map<String, dynamic>>> getChatMessages() async {
    try {
      final response = await _apiService.getChatMessages();
      if (response['status'] == 'success') {
        return List<Map<String, dynamic>>.from(response['data']);
      } else {
        _error = response['message'] ?? 'Failed to get chat messages';
        return [];
      }
    } catch (e) {
      _error = 'Error getting chat messages: $e';
      return [];
    }
  }

  Future<bool> sendChatMessage(String message) async {
    try {
      final response = await _apiService.sendChatMessage(message);
      if (response['status'] == 'success') {
        return true;
      } else {
        _error = response['message'] ?? 'Failed to send message';
        return false;
      }
    } catch (e) {
      _error = 'Error sending message: $e';
      return false;
    }
  }
}
