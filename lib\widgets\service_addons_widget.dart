import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cart_item.dart';
import '../models/service_addon.dart';
import '../providers/cart_provider.dart';
import 'neumorphic_container.dart';

class ServiceAddonsWidget extends StatefulWidget {
  final CartItem item;

  const ServiceAddonsWidget({
    super.key,
    required this.item,
  });

  @override
  State<ServiceAddonsWidget> createState() => _ServiceAddonsWidgetState();
}

class _ServiceAddonsWidgetState extends State<ServiceAddonsWidget> {
  bool _isExpanded = false;

  // Mock data for service add-ons
  List<ServiceAddon> _getRecommendedAddons() {
    final serviceId = widget.item.service.id;

    // Electrical service add-ons
    if (widget.item.category.name == 'Electrical' ||
        widget.item.service.name.contains('Electrical')) {
      return [
        ServiceAddon(
          id: '${serviceId}_addon1',
          serviceId: serviceId,
          name: 'Switch Installation',
          description: 'Install new electrical switches (parts extra)',
          price: 149.0,
          isPopular: true,
        ),
        ServiceAddon(
          id: '${serviceId}_addon2',
          serviceId: serviceId,
          name: 'Switch Replacement',
          description: 'Replace old or damaged switches (parts extra)',
          price: 129.0,
          isPopular: true,
        ),
        ServiceAddon(
          id: '${serviceId}_addon3',
          serviceId: serviceId,
          name: 'Wiring Inspection',
          description: 'Thorough inspection of electrical wiring',
          price: 199.0,
        ),
        ServiceAddon(
          id: '${serviceId}_addon4',
          serviceId: serviceId,
          name: 'Circuit Testing',
          description: 'Test electrical circuits for safety and functionality',
          price: 179.0,
        ),
      ];
    }

    // Bathroom cleaning add-ons
    if (widget.item.service.name.contains('Bathroom') &&
        widget.item.category.name == 'Cleaning') {
      return [
        ServiceAddon(
          id: '${serviceId}_addon1',
          serviceId: serviceId,
          name: 'Fan Cleaning',
          description: 'Deep cleaning of bathroom exhaust fans',
          price: 99.0,
          isPopular: true,
        ),
        ServiceAddon(
          id: '${serviceId}_addon2',
          serviceId: serviceId,
          name: 'Sink Cleaning',
          description: 'Thorough cleaning of sink and faucets',
          price: 79.0,
        ),
        ServiceAddon(
          id: '${serviceId}_addon3',
          serviceId: serviceId,
          name: 'Tile Scrubbing',
          description: 'Deep scrubbing of bathroom tiles',
          price: 149.0,
          isPopular: true,
        ),
      ];
    }

    // Kitchen cleaning add-ons
    if (widget.item.service.name.contains('Kitchen') &&
        widget.item.category.name == 'Cleaning') {
      return [
        ServiceAddon(
          id: '${serviceId}_addon1',
          serviceId: serviceId,
          name: 'Chimney Cleaning',
          description: 'Deep cleaning of kitchen chimney',
          price: 199.0,
          isPopular: true,
        ),
        ServiceAddon(
          id: '${serviceId}_addon2',
          serviceId: serviceId,
          name: 'Cabinet Cleaning',
          description: 'Thorough cleaning of kitchen cabinets',
          price: 149.0,
        ),
        ServiceAddon(
          id: '${serviceId}_addon3',
          serviceId: serviceId,
          name: 'Appliance Cleaning',
          description: 'Cleaning of kitchen appliances',
          price: 129.0,
          isPopular: true,
        ),
      ];
    }

    // Plumbing service add-ons
    if (widget.item.category.name == 'Plumbing' ||
        widget.item.service.name.contains('Plumbing') ||
        widget.item.service.name.contains('Pipe')) {
      return [
        ServiceAddon(
          id: '${serviceId}_addon1',
          serviceId: serviceId,
          name: 'Water Heater Inspection',
          description: 'Thorough inspection of water heater system',
          price: 199.0,
          isPopular: true,
        ),
        ServiceAddon(
          id: '${serviceId}_addon2',
          serviceId: serviceId,
          name: 'Tap Replacement',
          description: 'Replace old taps with new ones (parts extra)',
          price: 149.0,
        ),
        ServiceAddon(
          id: '${serviceId}_addon3',
          serviceId: serviceId,
          name: 'Drain Cleaning',
          description: 'Deep cleaning of drains to prevent clogging',
          price: 179.0,
          isPopular: true,
        ),
      ];
    }

    // Cleaning service default add-ons
    if (widget.item.category.name == 'Cleaning') {
      return [
        ServiceAddon(
          id: '${serviceId}_addon1',
          serviceId: serviceId,
          name: 'Premium Cleaning',
          description: 'Using premium cleaning products',
          price: 149.0,
          isPopular: true,
        ),
        ServiceAddon(
          id: '${serviceId}_addon2',
          serviceId: serviceId,
          name: 'Sanitization',
          description: 'Additional sanitization service',
          price: 99.0,
        ),
      ];
    }

    // Default add-ons for other services
    return [
      ServiceAddon(
        id: '${serviceId}_addon1',
        serviceId: serviceId,
        name: 'Express Service',
        description: 'Priority scheduling for faster service',
        price: 199.0,
        isPopular: true,
      ),
      ServiceAddon(
        id: '${serviceId}_addon2',
        serviceId: serviceId,
        name: 'Extended Warranty',
        description: 'Additional warranty coverage for the service',
        price: 149.0,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    final recommendedAddons = _getRecommendedAddons();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add-ons header
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                const Text(
                  'Recommended Add-ons',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D4059),
                  ),
                ),
                const Spacer(),
                Icon(
                  _isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: const Color(0xFF7D8CA3),
                ),
              ],
            ),
          ),
        ),

        // Add-ons list
        if (_isExpanded)
          Column(
            children: recommendedAddons.map((addon) {
              final isAdded = widget.item.addons.any((a) => a.id == addon.id);

              // Find the addon in the cart if it exists
              final existingAddon = isAdded
                  ? widget.item.addons.firstWhere((a) => a.id == addon.id)
                  : null;
              final addonQuantity = existingAddon?.quantity ?? 0;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Checkbox(
                          value: isAdded,
                          activeColor: widget.item.category.color,
                          onChanged: (value) {
                            if (value == true) {
                              cartProvider.addAddonToItem(
                                  widget.item.id, addon);
                            } else {
                              cartProvider.removeAddonFromItem(
                                  widget.item.id, addon.id);
                            }
                          },
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    addon.name,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF2D4059),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  if (addon.isPopular)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.orange.shade100,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Text(
                                        'Popular',
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: Colors.orange,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              Text(
                                addon.description,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF7D8CA3),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '₹${addon.price.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                      ],
                    ),

                    // Quantity controls - only show if addon is added
                    if (isAdded)
                      Padding(
                        padding: const EdgeInsets.only(left: 40.0, top: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                // Decrease button
                                InkWell(
                                  onTap: addonQuantity > 1
                                      ? () {
                                          cartProvider.updateAddonQuantity(
                                              widget.item.id,
                                              addon.id,
                                              addonQuantity - 1);
                                        }
                                      : null,
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: addonQuantity > 1
                                          ? Colors.grey.shade200
                                          : Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Icon(
                                      Icons.remove,
                                      size: 16,
                                      color: addonQuantity > 1
                                          ? const Color(0xFF7D8CA3)
                                          : Colors.grey.shade400,
                                    ),
                                  ),
                                ),

                                // Quantity display
                                Container(
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    addonQuantity.toString(),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF2D4059),
                                    ),
                                  ),
                                ),

                                // Increase button
                                InkWell(
                                  onTap: () {
                                    cartProvider.updateAddonQuantity(
                                        widget.item.id,
                                        addon.id,
                                        addonQuantity + 1);
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade200,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      size: 16,
                                      color: widget.item.category.color,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            // Total price for this addon
                            Text(
                              '₹${(addon.price * addonQuantity).toStringAsFixed(0)}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: widget.item.category.color,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }
}
