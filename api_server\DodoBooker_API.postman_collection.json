{"info": {"name": "DodoBooker API", "description": "API collection for testing DodoBooker app endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3000"}, {"key": "user_id", "value": "user_**********"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Authentication", "item": [{"name": "Send OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mobile\": \"**********\"\n}"}, "url": {"raw": "{{base_url}}/auth/send-otp", "host": ["{{base_url}}"], "path": ["auth", "send-otp"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mobile\": \"**********\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/auth/verify-otp", "host": ["{{base_url}}"], "path": ["auth", "verify-otp"]}}}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/1", "host": ["{{base_url}}"], "path": ["categories", "1"]}}}]}, {"name": "Subcategories", "item": [{"name": "Get All Subcategories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/subcategories", "host": ["{{base_url}}"], "path": ["subcategories"]}}}, {"name": "Get Subcategories by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/subcategories?categoryId=1", "host": ["{{base_url}}"], "path": ["subcategories"], "query": [{"key": "categoryId", "value": "1"}]}}}]}, {"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products", "host": ["{{base_url}}"], "path": ["products"]}}}, {"name": "Get Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products?categoryId=1-1", "host": ["{{base_url}}"], "path": ["products"], "query": [{"key": "categoryId", "value": "1-1"}]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/1-1-1", "host": ["{{base_url}}"], "path": ["products", "1-1-1"]}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/cart/{{user_id}}", "host": ["{{base_url}}"], "path": ["cart", "{{user_id}}"]}}}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"1-1-1\",\n  \"quantity\": 1\n}"}, "url": {"raw": "{{base_url}}/cart/{{user_id}}/add", "host": ["{{base_url}}"], "path": ["cart", "{{user_id}}", "add"]}}}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/cart/{{user_id}}", "host": ["{{base_url}}"], "path": ["cart", "{{user_id}}"]}}}, {"name": "Remove Item from Cart", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/cart/{{user_id}}/item/1-1-1", "host": ["{{base_url}}"], "path": ["cart", "{{user_id}}", "item", "1-1-1"]}}}]}, {"name": "Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/profile/{{user_id}}", "host": ["{{base_url}}"], "path": ["profile", "{{user_id}}"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated User Name\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/profile/{{user_id}}", "host": ["{{base_url}}"], "path": ["profile", "{{user_id}}"]}}}]}, {"name": "Testing", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}]}]}