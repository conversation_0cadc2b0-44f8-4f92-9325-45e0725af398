const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Mock Database
let database = {
  users: {},
  categories: [],
  subcategories: [],
  products: [],
  cart: {},
  bookings: [],
  addresses: [],
  coupons: [],
  notifications: [],
  chatMessages: []
};

// Initialize mock data
function initializeData() {
  // Categories
  database.categories = [
    {
      id: "1",
      name: "Cleaning",
      icon: 61648,
      color: 4280391411,
      imageUrl: "https://images.unsplash.com/photo-1581578731548-c64695cc6952",
      description: "Professional cleaning services for your home"
    },
    {
      id: "2",
      name: "Plumbing",
      icon: 58704,
      color: 4285887861,
      imageUrl: "https://images.unsplash.com/photo-1607472586893-edb57bdc0e39",
      description: "Expert plumbing services for all your needs"
    },
    {
      id: "3",
      name: "Electrical",
      icon: 57744,
      color: 4294951175,
      imageUrl: "https://images.unsplash.com/photo-1621905251918-48416bd8575a",
      description: "Safe and reliable electrical services"
    }
  ];

  // Subcategories
  database.subcategories = [
    {
      id: "1-1",
      parentId: "1",
      name: "Home Cleaning",
      icon: 61584,
      color: 4280391411,
      imageUrl: "https://images.unsplash.com/photo-1581578731548-c64695cc6952"
    },
    {
      id: "1-2",
      parentId: "1",
      name: "Deep Cleaning",
      icon: 61648,
      color: 4280391411,
      imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64"
    },
    {
      id: "2-1",
      parentId: "2",
      name: "Pipe Repair",
      icon: 58704,
      color: 4285887861,
      imageUrl: "https://images.unsplash.com/photo-1607472586893-edb57bdc0e39"
    },
    {
      id: "3-1",
      parentId: "3",
      name: "Switch Installation",
      icon: 57744,
      color: 4294951175,
      imageUrl: "https://images.unsplash.com/photo-1621905251918-48416bd8575a"
    }
  ];

  // Products
  database.products = [
    {
      id: "1-1-1",
      categoryId: "1-1",
      name: "Basic Home Cleaning",
      description: "Complete cleaning of your home including dusting, mopping, and bathroom cleaning.",
      price: 499.0,
      durationMinutes: 120,
      imageUrl: "https://images.unsplash.com/photo-1581578731548-c64695cc6952",
      rating: 4.5,
      reviewCount: 120,
      isPopular: true,
      isFeatured: true
    },
    {
      id: "1-1-2",
      categoryId: "1-1",
      name: "Premium Home Cleaning",
      description: "Comprehensive cleaning service with additional services like window cleaning and appliance cleaning.",
      price: 799.0,
      durationMinutes: 180,
      imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64",
      rating: 4.8,
      reviewCount: 89,
      isPopular: false,
      isFeatured: true
    }
  ];

  // Default user
  database.users['user_default'] = {
    id: 'user_default',
    mobile: '9876543210',
    name: 'Demo User',
    email: '<EMAIL>',
    profilePic: 'https://randomuser.me/api/portraits/men/1.jpg',
    createdAt: new Date().toISOString(),
    lastLogin: new Date().toISOString()
  };

  // Default cart
  database.cart['user_default'] = {
    userId: 'user_default',
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0,
    couponCode: null,
    couponDiscount: 0,
    smallOrderFee: 0,
    lastUpdated: new Date().toISOString()
  };
}

// Helper functions
function createSuccessResponse(data, message = 'Success') {
  return {
    status: 'success',
    message: message,
    data: data,
    timestamp: new Date().toISOString()
  };
}

function createErrorResponse(message, code = 400) {
  return {
    status: 'error',
    message: message,
    code: code,
    timestamp: new Date().toISOString()
  };
}

// Initialize data on server start
initializeData();

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json(createSuccessResponse({ status: 'Server is running' }));
});

// Authentication
app.post('/auth/send-otp', (req, res) => {
  const { mobile } = req.body;

  if (!mobile) {
    return res.status(400).json(createErrorResponse('Mobile number is required'));
  }

  // Simulate OTP sending
  setTimeout(() => {
    res.json(createSuccessResponse({
      message: 'OTP sent successfully',
      mobile: mobile,
      otp: '123456' // For testing purposes
    }));
  }, 1000);
});

app.post('/auth/verify-otp', (req, res) => {
  const { mobile, otp } = req.body;

  if (!mobile || !otp) {
    return res.status(400).json(createErrorResponse('Mobile and OTP are required'));
  }

  if (otp !== '123456') {
    return res.status(400).json(createErrorResponse('Invalid OTP'));
  }

  // Create or get user
  const userId = `user_${mobile}`;
  if (!database.users[userId]) {
    database.users[userId] = {
      id: userId,
      mobile: mobile,
      name: 'User',
      email: `${mobile}@example.com`,
      profilePic: 'https://randomuser.me/api/portraits/men/1.jpg',
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    };

    // Initialize cart for new user
    database.cart[userId] = {
      userId: userId,
      items: [],
      subtotal: 0,
      tax: 0,
      discount: 0,
      total: 0,
      couponCode: null,
      couponDiscount: 0,
      smallOrderFee: 0,
      lastUpdated: new Date().toISOString()
    };
  }

  res.json(createSuccessResponse({
    user: database.users[userId],
    token: `token_${userId}_${Date.now()}`
  }));
});

// Categories
app.get('/categories', (req, res) => {
  res.json(createSuccessResponse(database.categories));
});

app.get('/categories/:categoryId', (req, res) => {
  const { categoryId } = req.params;
  const category = database.categories.find(c => c.id === categoryId);

  if (!category) {
    return res.status(404).json(createErrorResponse('Category not found'));
  }

  res.json(createSuccessResponse(category));
});

// Subcategories
app.get('/subcategories', (req, res) => {
  const { categoryId } = req.query;

  let subcategories = database.subcategories;
  if (categoryId) {
    subcategories = subcategories.filter(s => s.parentId === categoryId);
  }

  res.json(createSuccessResponse(subcategories));
});

// Products
app.get('/products', (req, res) => {
  const { categoryId, subcategoryId } = req.query;

  let products = database.products;
  if (categoryId) {
    products = products.filter(p => p.categoryId === categoryId);
  }
  if (subcategoryId) {
    products = products.filter(p => p.categoryId === subcategoryId);
  }

  res.json(createSuccessResponse(products));
});

app.get('/products/:productId', (req, res) => {
  const { productId } = req.params;
  const product = database.products.find(p => p.id === productId);

  if (!product) {
    return res.status(404).json(createErrorResponse('Product not found'));
  }

  res.json(createSuccessResponse(product));
});

// Cart
app.get('/cart/:userId', (req, res) => {
  const { userId } = req.params;
  const cart = database.cart[userId];

  if (!cart) {
    return res.status(404).json(createErrorResponse('Cart not found'));
  }

  res.json(createSuccessResponse(cart));
});

app.post('/cart/:userId/add', (req, res) => {
  const { userId } = req.params;
  const { productId, quantity = 1 } = req.body;

  if (!database.cart[userId]) {
    database.cart[userId] = {
      userId: userId,
      items: [],
      subtotal: 0,
      tax: 0,
      discount: 0,
      total: 0,
      couponCode: null,
      couponDiscount: 0,
      smallOrderFee: 0,
      lastUpdated: new Date().toISOString()
    };
  }

  const product = database.products.find(p => p.id === productId);
  if (!product) {
    return res.status(404).json(createErrorResponse('Product not found'));
  }

  const cart = database.cart[userId];
  const existingItem = cart.items.find(item => item.productId === productId);

  if (existingItem) {
    existingItem.quantity += quantity;
  } else {
    cart.items.push({
      productId: productId,
      name: product.name,
      price: product.price,
      quantity: quantity,
      imageUrl: product.imageUrl,
      addedAt: new Date().toISOString()
    });
  }

  // Recalculate totals
  cart.subtotal = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  cart.tax = cart.subtotal * 0.05; // 5% tax
  cart.total = cart.subtotal + cart.tax;
  cart.lastUpdated = new Date().toISOString();

  res.json(createSuccessResponse(cart));
});

// Profile
app.get('/profile/:userId', (req, res) => {
  const { userId } = req.params;
  const user = database.users[userId];

  if (!user) {
    return res.status(404).json(createErrorResponse('User not found'));
  }

  res.json(createSuccessResponse(user));
});

// Clear cart
app.delete('/cart/:userId', (req, res) => {
  const { userId } = req.params;

  if (!database.cart[userId]) {
    return res.status(404).json(createErrorResponse('Cart not found'));
  }

  database.cart[userId] = {
    userId: userId,
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0,
    couponCode: null,
    couponDiscount: 0,
    smallOrderFee: 0,
    lastUpdated: new Date().toISOString()
  };

  res.json(createSuccessResponse(database.cart[userId]));
});

// Remove item from cart
app.delete('/cart/:userId/item/:productId', (req, res) => {
  const { userId, productId } = req.params;

  if (!database.cart[userId]) {
    return res.status(404).json(createErrorResponse('Cart not found'));
  }

  const cart = database.cart[userId];
  cart.items = cart.items.filter(item => item.productId !== productId);

  // Recalculate totals
  cart.subtotal = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  cart.tax = cart.subtotal * 0.05;
  cart.total = cart.subtotal + cart.tax;
  cart.lastUpdated = new Date().toISOString();

  res.json(createSuccessResponse(cart));
});

// Update profile
app.put('/profile/:userId', (req, res) => {
  const { userId } = req.params;
  const { name, email } = req.body;

  if (!database.users[userId]) {
    return res.status(404).json(createErrorResponse('User not found'));
  }

  if (name) database.users[userId].name = name;
  if (email) database.users[userId].email = email;
  database.users[userId].lastLogin = new Date().toISOString();

  res.json(createSuccessResponse(database.users[userId]));
});

// Get all users (for testing)
app.get('/users', (req, res) => {
  res.json(createSuccessResponse(Object.values(database.users)));
});

// Start server
app.listen(PORT, () => {
  console.log(`DodoBooker API Server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('- GET /health');
  console.log('- POST /auth/send-otp');
  console.log('- POST /auth/verify-otp');
  console.log('- GET /categories');
  console.log('- GET /subcategories');
  console.log('- GET /products');
  console.log('- GET /cart/:userId');
  console.log('- POST /cart/:userId/add');
  console.log('- DELETE /cart/:userId');
  console.log('- DELETE /cart/:userId/item/:productId');
  console.log('- GET /profile/:userId');
  console.log('- PUT /profile/:userId');
  console.log('- GET /users');
});
