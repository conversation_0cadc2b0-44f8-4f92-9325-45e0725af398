import 'package:flutter/material.dart';
import '../models/hierarchical_category.dart';
import '../services/supabase_service.dart';

class CategoryProvider with ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();

  // Categories data
  List<HierarchicalCategory> _categories = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<HierarchicalCategory> get categories => [..._categories];
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasCategories => _categories.isNotEmpty;

  // Initialize categories
  Future<void> loadCategories() async {
    if (_categories.isNotEmpty) {
      return; // Already loaded
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Load categories from Supabase
      final categoriesData = await _supabaseService.getCategories();

      // Convert Supabase data to HierarchicalCategory objects
      _categories = categoriesData.map((categoryData) {
        return _supabaseToCategory(categoryData);
      }).toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load categories: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Convert Supabase category data to HierarchicalCategory
  HierarchicalCategory _supabaseToCategory(Map<String, dynamic> categoryData) {
    return HierarchicalCategory(
      id: categoryData['id'] as String,
      name: categoryData['name'] as String,
      icon: IconData(
        categoryData['icon_code_point'] as int? ?? 61584, // Default icon
        fontFamily: 'MaterialIcons',
      ),
      color: Color(int.parse(
          categoryData['color_hex']?.replaceFirst('#', '0xFF') ??
              '0xFF4CAF50')),
      imageUrl: categoryData['image_url'] as String?,
      hasProducts: true, // Assume categories have products
      subcategories: [], // Will be loaded separately if needed
    );
  }

  // Get category by ID
  HierarchicalCategory? getCategoryById(String id) {
    // First check in main categories
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      // Not found in main categories, search in subcategories
      for (final category in _categories) {
        final result = _findCategoryInSubcategories(category, id);
        if (result != null) {
          return result;
        }
      }
    }
    return null;
  }

  // Helper method to find a category in subcategories
  HierarchicalCategory? _findCategoryInSubcategories(
      HierarchicalCategory parent, String id) {
    for (final subcategory in parent.subcategories) {
      if (subcategory.id == id) {
        return subcategory;
      }

      // Recursively search in subcategories
      final result = _findCategoryInSubcategories(subcategory, id);
      if (result != null) {
        return result;
      }
    }
    return null;
  }

  // Clear categories
  void clearCategories() {
    _categories = [];
    notifyListeners();
  }
}
