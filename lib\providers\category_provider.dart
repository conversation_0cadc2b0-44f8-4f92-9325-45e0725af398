import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/hierarchical_category.dart';
import '../services/supabase_service.dart';

class CategoryProvider with ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();

  // Categories data
  List<HierarchicalCategory> _categories = [];
  bool _isLoading = false;
  String? _error;

  // Real-time subscription
  StreamSubscription<List<Map<String, dynamic>>>? _categoriesSubscription;

  // Cache timestamp to track when data was last loaded
  DateTime? _lastLoadTime;

  // Getters
  List<HierarchicalCategory> get categories => [..._categories];
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime? get lastLoadTime => _lastLoadTime;

  // Get formatted last update time
  String get lastUpdateText {
    if (_lastLoadTime == null) return 'Never updated';
    final now = DateTime.now();
    final difference = now.difference(_lastLoadTime!);

    if (difference.inMinutes < 1) {
      return 'Updated just now';
    } else if (difference.inMinutes < 60) {
      return 'Updated ${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return 'Updated ${difference.inHours}h ago';
    } else {
      return 'Updated ${difference.inDays}d ago';
    }
  }

  bool get hasCategories => _categories.isNotEmpty;

  // Initialize categories
  Future<void> loadCategories({bool forceRefresh = false}) async {
    if (_categories.isNotEmpty && !forceRefresh) {
      return; // Already loaded
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      print('CategoryProvider: Loading categories from Supabase...');

      // Load categories from Supabase
      final categoriesData = await _supabaseService.getCategories();

      if (categoriesData.isNotEmpty) {
        // Load subcategories for all categories
        final subcategoriesData = await _supabaseService.getAllSubcategories();

        // Convert Supabase data to HierarchicalCategory objects with subcategories
        _categories = categoriesData.map((categoryData) {
          return _supabaseToCategory(categoryData, subcategoriesData);
        }).toList();

        print(
            'CategoryProvider: Loaded ${_categories.length} categories with subcategories from Supabase');
      } else {
        // Fallback to mock data if Supabase is empty
        print(
            'CategoryProvider: No categories from Supabase, using fallback data...');
        _categories = _getFallbackCategories();
        print(
            'CategoryProvider: Loaded ${_categories.length} fallback categories');
      }

      _lastLoadTime = DateTime.now();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('CategoryProvider: Error loading categories: $e');

      // Use fallback categories on error
      _categories = _getFallbackCategories();
      _error = null; // Clear error since we have fallback data
      _lastLoadTime = DateTime.now();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Check if cache is stale (older than 5 minutes)
  bool get _isCacheStale {
    if (_lastLoadTime == null) return true;
    return DateTime.now().difference(_lastLoadTime!).inMinutes > 5;
  }

  // Auto-refresh categories if cache is stale
  Future<void> autoRefreshIfNeeded() async {
    if (_isCacheStale || _categories.isEmpty) {
      print('CategoryProvider: Cache is stale, auto-refreshing...');
      await refreshCategories();
    }
  }

  // Get fallback categories if Supabase fails
  List<HierarchicalCategory> _getFallbackCategories() {
    return [
      HierarchicalCategory(
        id: 'cleaning',
        name: 'Cleaning',
        icon: Icons.cleaning_services,
        color: const Color(0xFF4ECDC4),
        imageUrl:
            'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-**********.jpg',
        hasProducts: true,
      ),
      HierarchicalCategory(
        id: 'plumbing',
        name: 'Plumbing',
        icon: Icons.plumbing,
        color: const Color(0xFFFF6B6B),
        imageUrl:
            'https://img.freepik.com/free-photo/plumber-fixing-pipe_23-**********.jpg',
        hasProducts: true,
      ),
      HierarchicalCategory(
        id: 'electrical',
        name: 'Electrical',
        icon: Icons.electrical_services,
        color: const Color(0xFFFFD93D),
        imageUrl:
            'https://img.freepik.com/free-photo/electrician-working_23-**********.jpg',
        hasProducts: true,
      ),
    ];
  }

  // Force refresh categories from Supabase
  Future<void> refreshCategories() async {
    print('CategoryProvider: Force refreshing categories...');
    _categories.clear(); // Clear existing categories
    await loadCategories(forceRefresh: true);
  }

  // Convert Supabase category data to HierarchicalCategory
  HierarchicalCategory _supabaseToCategory(Map<String, dynamic> categoryData,
      [List<Map<String, dynamic>>? allSubcategories]) {
    // Find subcategories for this category
    List<HierarchicalCategory> subcategories = [];
    if (allSubcategories != null) {
      final categorySubcategories = allSubcategories
          .where((sub) => sub['category_id'] == categoryData['id'])
          .toList();

      subcategories = categorySubcategories.map((subData) {
        return HierarchicalCategory(
          id: subData['id'] as String,
          name: subData['name'] as String,
          icon: IconData(
            subData['icon_code_point'] as int? ?? 61584,
            fontFamily: 'MaterialIcons',
          ),
          color: Color(int.parse(
              subData['color_hex']?.replaceFirst('#', '0xFF') ?? '0xFF4CAF50')),
          imageUrl: subData['image_url'] as String?,
          hasProducts: true,
          subcategories: [],
        );
      }).toList();
    }

    return HierarchicalCategory(
      id: categoryData['id'] as String,
      name: categoryData['name'] as String,
      icon: IconData(
        categoryData['icon_code_point'] as int? ?? 61584, // Default icon
        fontFamily: 'MaterialIcons',
      ),
      color: Color(int.parse(
          categoryData['color_hex']?.replaceFirst('#', '0xFF') ??
              '0xFF4CAF50')),
      imageUrl: categoryData['image_url'] as String?,
      hasProducts: subcategories.isEmpty, // Has products if no subcategories
      subcategories: subcategories,
    );
  }

  // Get category by ID
  HierarchicalCategory? getCategoryById(String id) {
    // First check in main categories
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      // Not found in main categories, search in subcategories
      for (final category in _categories) {
        final result = _findCategoryInSubcategories(category, id);
        if (result != null) {
          return result;
        }
      }
    }
    return null;
  }

  // Helper method to find a category in subcategories
  HierarchicalCategory? _findCategoryInSubcategories(
      HierarchicalCategory parent, String id) {
    for (final subcategory in parent.subcategories) {
      if (subcategory.id == id) {
        return subcategory;
      }

      // Recursively search in subcategories
      final result = _findCategoryInSubcategories(subcategory, id);
      if (result != null) {
        return result;
      }
    }
    return null;
  }

  // Clear categories
  void clearCategories() {
    _categories = [];
    notifyListeners();
  }
}
