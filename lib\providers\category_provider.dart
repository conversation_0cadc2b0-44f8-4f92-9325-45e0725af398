import 'dart:async';
import 'package:flutter/material.dart';
import '../models/hierarchical_category.dart';
import '../services/mock_api_service.dart';

class CategoryProvider with ChangeNotifier {
  final MockApiService _apiService = MockApiService();

  // Categories data
  List<HierarchicalCategory> _categories = [];
  bool _isLoading = false;
  String? _error;

  // Stream subscription for realtime updates
  StreamSubscription<Map<String, dynamic>>? _categoriesSubscription;

  // Getters
  List<HierarchicalCategory> get categories => [..._categories];
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasCategories => _categories.isNotEmpty;

  // Initialize categories
  Future<void> loadCategories() async {
    if (_categories.isNotEmpty) {
      return; // Already loaded
    }

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // First load categories from the regular API
      final response = await _apiService.getCategories();

      if (response['status'] == 'success') {
        final List<dynamic> categoriesData = response['data'];

        // Convert JSON data back to HierarchicalCategory objects
        _categories = categoriesData.map((categoryJson) {
          return _jsonToCategory(categoryJson);
        }).toList();

        _isLoading = false;
        notifyListeners();

        // Then subscribe to realtime updates
        _subscribeToRealtimeUpdates();
      } else {
        _error = response['message'] ?? 'Failed to load categories';
        _isLoading = false;
        notifyListeners();
      }
    } catch (e) {
      _error = 'An error occurred: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Subscribe to realtime category updates
  void _subscribeToRealtimeUpdates() {
    // Cancel any existing subscription
    _categoriesSubscription?.cancel();

    // Subscribe to the categories stream
    _categoriesSubscription =
        _apiService.getCategoriesStream().listen((response) {
      if (response['status'] == 'success') {
        final List<dynamic> categoriesData = response['data'];

        // Convert JSON data back to HierarchicalCategory objects
        _categories = categoriesData.map((categoryJson) {
          return _jsonToCategory(categoryJson);
        }).toList();

        // Notify listeners about the update
        notifyListeners();
      }
    }, onError: (error) {
      _error = 'Stream error: $error';
      notifyListeners();
    });
  }

  // Get category by ID
  HierarchicalCategory? getCategoryById(String id) {
    // First check in main categories
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      // Not found in main categories, search in subcategories
      for (final category in _categories) {
        final result = _findCategoryInSubcategories(category, id);
        if (result != null) {
          return result;
        }
      }
    }
    return null;
  }

  // Helper method to find a category in subcategories
  HierarchicalCategory? _findCategoryInSubcategories(
      HierarchicalCategory parent, String id) {
    for (final subcategory in parent.subcategories) {
      if (subcategory.id == id) {
        return subcategory;
      }

      // Recursively search in subcategories
      final result = _findCategoryInSubcategories(subcategory, id);
      if (result != null) {
        return result;
      }
    }
    return null;
  }

  // Helper method to convert JSON to HierarchicalCategory
  HierarchicalCategory _jsonToCategory(Map<String, dynamic> json) {
    // Convert subcategories recursively
    List<HierarchicalCategory> subcategories = [];
    if (json['subcategories'] != null) {
      subcategories = (json['subcategories'] as List)
          .map((subcat) => _jsonToCategory(subcat as Map<String, dynamic>))
          .toList();
    }

    return HierarchicalCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: IconData(json['icon'] as int, fontFamily: 'MaterialIcons'),
      color: Color(json['color'] as int),
      imageUrl: json['imageUrl'] as String?,
      hasProducts: json['hasProducts'] as bool? ?? false,
      subcategories: subcategories,
    );
  }

  // Clear categories
  void clearCategories() {
    _categories = [];
    notifyListeners();
  }

  // Clean up resources
  @override
  void dispose() {
    // Cancel the subscription when the provider is disposed
    _categoriesSubscription?.cancel();
    super.dispose();
  }
}
