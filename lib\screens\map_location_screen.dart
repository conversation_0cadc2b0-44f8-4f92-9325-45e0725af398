import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';

class MapLocationScreen extends StatefulWidget {
  final String? initialAddress;

  const MapLocationScreen({
    super.key,
    this.initialAddress,
  });

  @override
  State<MapLocationScreen> createState() => _MapLocationScreenState();
}

class _MapLocationScreenState extends State<MapLocationScreen> {
  final Completer<GoogleMapController> _controller = Completer();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(40.7128, -74.0060), // New York
    zoom: 14.0,
  );

  CameraPosition _currentPosition = _defaultPosition;
  String _currentAddress = '';
  bool _isLoading = false;
  bool _isSearching = false;
  List<Map<String, dynamic>> _searchResults = [];
  Set<Marker> _markers = {};

  // Helper function to create colors with opacity
  Color withOpacity(Color color, double opacity) {
    return Color.fromRGBO(
      color.red & 0xFF,
      color.green & 0xFF,
      color.blue & 0xFF,
      opacity,
    );
  }

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();

    if (widget.initialAddress != null && widget.initialAddress!.isNotEmpty) {
      _searchController.text = widget.initialAddress!;
      _searchAddress();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Permissions are denied, show a message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Location permissions are denied'),
                backgroundColor: Colors.red,
              ),
            );
          }
          setState(() {
            _isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Permissions are denied forever, handle appropriately
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Location permissions are permanently denied, please enable them in settings'),
              backgroundColor: Colors.red,
            ),
          );
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Update camera position
      final CameraPosition newPosition = CameraPosition(
        target: LatLng(position.latitude, position.longitude),
        zoom: 16.0,
      );

      // Update map
      final GoogleMapController controller = await _controller.future;
      controller.animateCamera(CameraUpdate.newCameraPosition(newPosition));

      // Update marker
      _updateMarker(LatLng(position.latitude, position.longitude));

      // Get address from coordinates
      _getAddressFromLatLng(position.latitude, position.longitude);
    } catch (e) {
      debugPrint('Error getting location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getAddressFromLatLng(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        setState(() {
          _currentAddress = _formatAddress(place);
        });
      }
    } catch (e) {
      debugPrint('Error getting address: $e');
    }
  }

  String _formatAddress(Placemark place) {
    List<String> addressParts = [
      place.street ?? '',
      place.subLocality ?? '',
      place.locality ?? '',
      place.administrativeArea ?? '',
      place.postalCode ?? '',
      place.country ?? '',
    ];

    // Filter out empty parts
    addressParts = addressParts.where((part) => part.isNotEmpty).toList();

    return addressParts.join(', ');
  }

  void _updateMarker(LatLng position) {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
          draggable: true,
          onDragEnd: (newPosition) {
            _getAddressFromLatLng(newPosition.latitude, newPosition.longitude);
          },
        ),
      };
      _currentPosition = CameraPosition(
        target: position,
        zoom: 16.0,
      );
    });
  }

  Future<void> _searchAddress() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    setState(() {
      _isSearching = true;
      _searchResults = [];
    });

    try {
      // Using a more reliable geocoding approach
      List<Location> locations = await locationFromAddress(query);

      if (locations.isNotEmpty) {
        // Get the first location
        Location location = locations.first;

        // Get address details for better display
        List<Placemark> placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );

        List<Map<String, dynamic>> results = [];

        // Add the main result
        if (placemarks.isNotEmpty) {
          Placemark place = placemarks.first;
          String formattedAddress = _formatAddress(place);

          results.add({
            'display_name': formattedAddress,
            'lat': location.latitude,
            'lon': location.longitude,
          });

          // Move the map to this location
          _updateMarker(LatLng(location.latitude, location.longitude));
          _controller.future.then((controller) {
            controller.animateCamera(CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(location.latitude, location.longitude),
                zoom: 16.0,
              ),
            ));
          });
        }

        // Try to get additional nearby results for better suggestions
        try {
          final response = await http.get(
            Uri.parse(
              'https://nominatim.openstreetmap.org/search?q=$query&format=json&limit=5',
            ),
            headers: {'Accept': 'application/json'},
          );

          if (response.statusCode == 200) {
            final List<dynamic> data = json.decode(response.body);

            for (var item in data) {
              // Skip if it's too similar to our main result
              double lat = double.parse(item['lat']);
              double lon = double.parse(item['lon']);

              if ((lat - location.latitude).abs() < 0.01 &&
                  (lon - location.longitude).abs() < 0.01) {
                continue;
              }

              results.add({
                'display_name': item['display_name'],
                'lat': lat,
                'lon': lon,
              });
            }
          }
        } catch (e) {
          debugPrint('Error getting additional results: $e');
        }

        setState(() {
          _searchResults = results;
        });
      } else {
        // Fallback to OpenStreetMap if no results from geocoding
        final response = await http.get(
          Uri.parse(
            'https://nominatim.openstreetmap.org/search?q=$query&format=json&limit=5',
          ),
          headers: {'Accept': 'application/json'},
        );

        if (response.statusCode == 200) {
          final List<dynamic> data = json.decode(response.body);

          if (data.isNotEmpty) {
            setState(() {
              _searchResults = data.map((item) {
                double lat = double.parse(item['lat']);
                double lon = double.parse(item['lon']);

                return {
                  'display_name': item['display_name'],
                  'lat': lat,
                  'lon': lon,
                };
              }).toList();

              // Move the map to the first result
              if (_searchResults.isNotEmpty) {
                final firstResult = _searchResults.first;
                _updateMarker(LatLng(firstResult['lat'], firstResult['lon']));
                _controller.future.then((controller) {
                  controller.animateCamera(CameraUpdate.newCameraPosition(
                    CameraPosition(
                      target: LatLng(firstResult['lat'], firstResult['lon']),
                      zoom: 16.0,
                    ),
                  ));
                });
              }
            });
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('No locations found for this search'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error searching address: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error searching for location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  void _selectSearchResult(Map<String, dynamic> result) {
    final LatLng position = LatLng(result['lat'], result['lon']);

    _updateMarker(position);
    _searchController.text = result['display_name'];
    _currentAddress = result['display_name'];

    // Move camera to selected position
    _controller.future.then((controller) {
      controller.animateCamera(CameraUpdate.newCameraPosition(
        CameraPosition(target: position, zoom: 16.0),
      ));
    });

    // Clear search results and hide keyboard
    setState(() {
      _searchResults = [];
    });
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Select Location',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF2D4059)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(
        children: [
          // Google Map
          Positioned.fill(
            child: GoogleMap(
              mapType: MapType.normal,
              initialCameraPosition: _currentPosition,
              onMapCreated: (GoogleMapController controller) {
                _controller.complete(controller);
              },
              markers: _markers,
              myLocationEnabled: true,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              onTap: (LatLng position) {
                _updateMarker(position);
                _getAddressFromLatLng(position.latitude, position.longitude);
              },
            ),
          ),

          // Search bar
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Column(
              children: [
                NeumorphicContainer(
                  child: Row(
                    children: [
                      const Icon(
                        Icons.search,
                        color: Color(0xFF7D8CA3),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _searchController,
                          focusNode: _searchFocusNode,
                          decoration: const InputDecoration(
                            hintText: 'Search for a location',
                            border: InputBorder.none,
                          ),
                          onSubmitted: (_) => _searchAddress(),
                        ),
                      ),
                      if (_searchController.text.isNotEmpty)
                        IconButton(
                          icon:
                              const Icon(Icons.clear, color: Color(0xFF7D8CA3)),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchResults = [];
                            });
                          },
                        ),
                    ],
                  ),
                ),

                // Search results
                if (_searchResults.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: withOpacity(Colors.black, 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _searchResults.length,
                      separatorBuilder: (context, index) =>
                          const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final result = _searchResults[index];
                        return ListTile(
                          title: Text(
                            result['display_name'],
                            style: const TextStyle(fontSize: 14),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          onTap: () => _selectSearchResult(result),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),

          // Current location button
          Positioned(
            bottom: 100,
            right: 16,
            child: FloatingActionButton(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              onPressed: _getCurrentLocation,
              child: const Icon(Icons.my_location),
            ),
          ),

          // Address and confirm button
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFFCFD8DC),
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Selected Location',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _currentAddress.isEmpty
                        ? 'No address selected'
                        : _currentAddress,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  NeumorphicButton(
                    onPressed: _currentAddress.isEmpty
                        ? null
                        : () {
                            Navigator.of(context).pop({
                              'address': _currentAddress,
                              'latitude': _currentPosition.target.latitude,
                              'longitude': _currentPosition.target.longitude,
                            });
                          },
                    child: const Text('Confirm Location'),
                  ),
                ],
              ),
            ),
          ),

          // Loading indicator
          if (_isLoading)
            Container(
              color: withOpacity(Colors.black, 0.3),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
