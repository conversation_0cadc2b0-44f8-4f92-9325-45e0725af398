import 'package:flutter/material.dart';

class NeumorphicContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final double borderRadius;
  final Color? color;
  final EdgeInsetsGeometry padding;
  final bool isPressed;
  final VoidCallback? onTap;

  const NeumorphicContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.borderRadius = 16.0,
    this.color,
    this.padding = const EdgeInsets.all(16.0),
    this.isPressed = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = color ?? const Color(0xFFF0F5F9);

    // Helper function to create colors with opacity
    Color withOpacity(Color color, double opacity) {
      return Color.fromRGBO(
        color.red,
        color.green,
        color.blue,
        opacity,
      );
    }

    final whiteWithOpacity = withOpacity(Colors.white, 0.8);
    final shadowWithOpacity = withOpacity(const Color(0xFFA3B1C6), 0.4);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        width: width,
        height: height,
        padding: padding,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: isPressed
              ? [
                  BoxShadow(
                    color: whiteWithOpacity,
                    offset: const Offset(-2, -2),
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: shadowWithOpacity,
                    offset: const Offset(2, 2),
                    blurRadius: 5,
                    spreadRadius: 1,
                  ),
                ]
              : [
                  BoxShadow(
                    color: whiteWithOpacity,
                    offset: const Offset(-5, -5),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: shadowWithOpacity,
                    offset: const Offset(5, 5),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
        ),
        child: child,
      ),
    );
  }
}
