import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';

/// Base class for mock API services with common functionality
abstract class BaseMockApiService {
  // Random generator for simulating network conditions
  final Random _random = Random();
  
  // Simulate network delay
  Future<void> simulateNetworkDelay() async {
    // Simulate different network conditions
    final latency = _random.nextInt(3);

    switch (latency) {
      case 0: // Fast connection
        await Future.delayed(
            Duration(milliseconds: 100 + _random.nextInt(200)));
        break;
      case 1: // Medium connection
        await Future.delayed(
            Duration(milliseconds: 500 + _random.nextInt(500)));
        break;
      case 2: // Slow connection
        await Future.delayed(
            Duration(milliseconds: 1000 + _random.nextInt(1000)));
        break;
    }
  }
  
  // Debug logging helper
  void logApiCall(String endpoint, {Map<String, dynamic>? params, bool isEnhanced = false}) {
    final prefix = isEnhanced ? 'EnhancedMockAPI' : 'MockAPI';
    debugPrint(
        '$prefix: Calling $endpoint ${params != null ? 'with params: $params' : ''}');
  }
  
  // Helper method to create standard API response
  Map<String, dynamic> createSuccessResponse(dynamic data) {
    return {
      'status': 'success',
      'data': data,
    };
  }
  
  // Helper method to create error response
  Map<String, dynamic> createErrorResponse(String message) {
    return {
      'status': 'error',
      'message': message,
    };
  }
  
  // Helper method to transform a stream to include status
  Stream<Map<String, dynamic>> transformStreamToApiResponse(Stream<dynamic> stream) {
    return stream.map((data) => createSuccessResponse(data));
  }
  
  // Helper method to simulate price updates
  void simulatePriceUpdates(List<Map<String, dynamic>> products, int numProductsToUpdate) {
    for (var i = 0; i < numProductsToUpdate; i++) {
      final randomIndex = _random.nextInt(products.length);
      final product = Map<String, dynamic>.from(products[randomIndex]);

      // Update price by ±5-15%
      final priceChange = (product['price'] *
              (_random.nextDouble() * 0.1 + 0.05) *
              (_random.nextBool() ? 1 : -1))
          .round();
      product['price'] = (product['price'] + priceChange)
          .clamp(product['price'] * 0.7, product['price'] * 1.3)
          .round();

      // Update availability status randomly
      product['inStock'] = _random.nextBool();

      // Update last modified timestamp
      product['lastUpdated'] = DateTime.now().toIso8601String();

      // Replace the product in the database
      products[randomIndex] = product;
    }
  }
}
