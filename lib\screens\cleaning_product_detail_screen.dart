import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cleaning_product.dart';
import '../models/service_adapter.dart';
import '../providers/cart_provider.dart';
import '../providers/product_provider.dart';
import '../widgets/cart_icon_with_badge.dart';
import '../widgets/quantity_counter.dart';
import '../widgets/app_navigation_bar.dart';

class CleaningProductDetailScreen extends StatefulWidget {
  final CleaningProduct product;

  const CleaningProductDetailScreen({
    super.key,
    required this.product,
  });

  @override
  State<CleaningProductDetailScreen> createState() =>
      _CleaningProductDetailScreenState();
}

class _CleaningProductDetailScreenState
    extends State<CleaningProductDetailScreen> {
  int _quantity = 1;
  bool _isInCart = false;

  // Product data
  CleaningProduct? _product;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _product = widget.product;

    // Check if product is already in cart
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cartProvider = Provider.of<CartProvider>(context, listen: false);
      setState(() {
        _isInCart = cartProvider.items
            .any((item) => item.service.id == widget.product.id);
        if (_isInCart) {
          final cartItem = cartProvider.items
              .firstWhere((item) => item.service.id == widget.product.id);
          _quantity = cartItem.quantity;
        }
      });

      // Refresh product details from API
      _refreshProductDetails();
    });
  }

  Future<void> _refreshProductDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final productProvider =
          Provider.of<ProductProvider>(context, listen: false);
      final updatedProduct =
          await productProvider.fetchProductById(widget.product.id);

      if (updatedProduct != null) {
        setState(() {
          _product = updatedProduct;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Failed to load product details';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'An error occurred: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the refreshed product data if available
    final product = _product ?? widget.product;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          product.name,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: const [
          CartIconWithBadge(),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _refreshProductDetails,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _refreshProductDetails,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: _buildProductDetails(product),
                  ),
                ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildBottomBar(),
          AppNavigationBar(
            currentSection: NavigationSection.home,
            mainContext: context,
          ),
        ],
      ),
    );
  }

  Widget _buildProductDetails(CleaningProduct product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product image
        SizedBox(
          width: double.infinity,
          height: 200,
          child: Image.network(
            product.imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey.shade200,
                child: const Center(
                  child: Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                    size: 50,
                  ),
                ),
              );
            },
          ),
        ),

        // Product details
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and rating
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.green,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          product.rating.toString(),
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '(${_formatReviewCount(product.reviews)} reviews)',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Price
              Text(
                'Starts at ₹${product.price.toInt()}',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),

              const SizedBox(height: 24),

              // Description
              const Text(
                'Description',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                product.description,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                  height: 1.5,
                ),
              ),

              const SizedBox(height: 24),

              // What's included
              const Text(
                'What\'s included',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              ...product.includes.map((item) => _buildListItem(
                    item,
                    Icons.check_circle,
                    Colors.green,
                  )),

              const SizedBox(height: 24),

              // What's excluded
              const Text(
                'What\'s excluded',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              ...product.excludes.map((item) => _buildListItem(
                    item,
                    Icons.info,
                    Colors.orange,
                  )),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildListItem(String text, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          // Quantity counter
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Quantity',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                QuantityCounter(
                  initialValue: _quantity,
                  onChanged: (value) {
                    setState(() {
                      _quantity = value;
                    });
                  },
                  minValue: 1,
                  maxValue: 10,
                  width: 120,
                  height: 36,
                ),
              ],
            ),
          ),

          // Add to cart button
          ElevatedButton(
            onPressed: _addToCart,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              _isInCart ? 'Update Cart' : 'Add to Cart',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _addToCart() async {
    final product = _product ?? widget.product;
    final cartProvider = Provider.of<CartProvider>(context, listen: false);

    // Check if we need to clear the cart first
    // Allow multiple services from the same category
    if (cartProvider.items.isNotEmpty &&
        !cartProvider.items.any((item) => item.service.id == product.id) &&
        !cartProvider.isFromSameCategory(product.categoryId)) {
      _showClearCartConfirmation();
      return;
    }

    // Convert product to service
    final service = convertToService(product);
    final category = createServiceCategory(product);

    // Add or update cart
    await cartProvider.addItem(
      service: service,
      category: category,
      quantity: _quantity,
      date: DateTime.now(),
      time: '10:00 AM', // Default time
    );

    if (mounted) {
      setState(() {
        _isInCart = true;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product.name} added to cart'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showClearCartConfirmation() {
    final product = _product ?? widget.product;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Replace Cart Items?'),
        content: const Text(
            'You can only book services from the same category at a time. Your cart contains services from a different category. Do you want to replace the items in your cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Clear cart and add new item
              final cartProvider =
                  Provider.of<CartProvider>(context, listen: false);
              await cartProvider.clear();

              // Convert product to service
              final service = convertToService(product);
              final category = createServiceCategory(product);

              // Add to cart
              await cartProvider.addItem(
                service: service,
                category: category,
                quantity: _quantity,
                date: DateTime.now(),
                time: '10:00 AM', // Default time
              );

              if (mounted) {
                setState(() {
                  _isInCart = true;
                });

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${product.name} added to cart'),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
            child: const Text('Replace'),
          ),
        ],
      ),
    );
  }

  String _formatReviewCount(int count) {
    if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(count >= 10000 ? 0 : 1)}K';
    }
    return count.toString();
  }
}
