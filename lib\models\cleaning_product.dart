class CleaningProduct {
  final String id;
  final String name;
  final String description;
  final double price;
  final double rating;
  final int reviews;
  final String imageUrl;
  final List<String> includes;
  final List<String> excludes;
  final int options;
  final String categoryId;

  CleaningProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.rating,
    required this.reviews,
    required this.imageUrl,
    required this.includes,
    required this.excludes,
    required this.options,
    required this.categoryId,
  });

  factory CleaningProduct.fromMap(Map<String, dynamic> map, String categoryId) {
    return CleaningProduct(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      price: map['price'].toDouble(),
      rating: map['rating'].toDouble(),
      reviews: map['reviews'],
      imageUrl: map['imageUrl'],
      includes: List<String>.from(map['includes']),
      excludes: List<String>.from(map['excludes']),
      options: map['options'],
      categoryId: categoryId,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'rating': rating,
      'reviews': reviews,
      'imageUrl': imageUrl,
      'includes': includes,
      'excludes': excludes,
      'options': options,
      'categoryId': categoryId,
    };
  }
}
