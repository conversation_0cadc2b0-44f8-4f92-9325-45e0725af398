import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/booking_card.dart';
import '../models/service.dart';
import '../models/service_category.dart';
import '../models/order.dart';
import '../models/cart_item.dart';
import '../providers/real_time_api_provider.dart';
import 'order_details_screen.dart';

class BookingsScreen extends StatefulWidget {
  const BookingsScreen({super.key});

  @override
  State<BookingsScreen> createState() => _BookingsScreenState();
}

class _BookingsScreenState extends State<BookingsScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late TabController _tabController;

  // Booking data
  List<Map<String, dynamic>> _upcomingBookings = [];
  List<Map<String, dynamic>> _pastBookings = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addObserver(this);

    // Ensure authentication is set up before loading bookings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureAuthenticationAndLoadBookings();
    });
  }

  // Ensure user is authenticated before loading bookings
  Future<void> _ensureAuthenticationAndLoadBookings() async {
    final apiProvider =
        Provider.of<RealTimeApiProvider>(context, listen: false);

    print('BookingsScreen: Starting authentication check...');
    print('BookingsScreen: Current user: ${apiProvider.currentUser}');
    print('BookingsScreen: Is authenticated: ${apiProvider.isAuthenticated}');

    // Check if user is authenticated, if not, authenticate with default user
    if (!apiProvider.isAuthenticated) {
      print('BookingsScreen: User not authenticated, setting default user...');
      // Directly set the default user as authenticated
      apiProvider.setCurrentUser('user_default');

      // Wait a moment for the authentication to be processed
      await Future.delayed(const Duration(milliseconds: 500));

      print(
          'BookingsScreen: After setting default user: ${apiProvider.currentUser}');
    }

    // Now load bookings and set up listener
    _loadBookings();
    _setupBookingsListener();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh bookings when app comes back to foreground
    if (state == AppLifecycleState.resumed) {
      _loadBookings();
    }
  }

  // Set up real-time bookings listener
  void _setupBookingsListener() {
    final apiProvider =
        Provider.of<RealTimeApiProvider>(context, listen: false);

    // Listen to bookings stream for real-time updates
    apiProvider.bookingsStream.listen((bookings) {
      if (mounted) {
        _processBookingsUpdate(bookings);
      }
    });
  }

  // Process bookings update from stream
  void _processBookingsUpdate(List<Map<String, dynamic>> bookings) {
    final now = DateTime.now();
    final upcoming = <Map<String, dynamic>>[];
    final past = <Map<String, dynamic>>[];

    for (final booking in bookings) {
      // Convert API booking to our format
      final processedBooking = _processBooking(booking);

      // Check if booking is upcoming or past
      final bookingDate = DateTime.parse(booking['date']);
      if (bookingDate.isAfter(now) ||
          (booking['status'] == 'Pending' ||
              booking['status'] == 'Confirmed')) {
        upcoming.add(processedBooking);
      } else {
        past.add(processedBooking);
      }
    }

    // Sort bookings by date
    upcoming.sort(
        (a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));
    past.sort(
        (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));

    if (mounted) {
      setState(() {
        _upcomingBookings = upcoming;
        _pastBookings = past;
        _isLoading = false;
        _error = null;
      });
    }
  }

  // Load bookings from API
  Future<void> _loadBookings() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final apiProvider =
          Provider.of<RealTimeApiProvider>(context, listen: false);

      // Debug: Check authentication status
      print('BookingsScreen: Loading bookings...');
      print('BookingsScreen: Is authenticated: ${apiProvider.isAuthenticated}');
      print('BookingsScreen: Current user: ${apiProvider.currentUser}');

      final response = await apiProvider.getBookings();
      print('BookingsScreen: Received ${response.length} bookings');

      // Process bookings
      final now = DateTime.now();
      final upcoming = <Map<String, dynamic>>[];
      final past = <Map<String, dynamic>>[];

      for (final booking in response) {
        print(
            'BookingsScreen: Processing booking: ${booking['id']} - ${booking['service']}');

        // Convert API booking to our format
        final processedBooking = _processBooking(booking);

        // Check if booking is upcoming or past
        final bookingDate = DateTime.parse(booking['date']);
        if (bookingDate.isAfter(now) ||
            (booking['status'] == 'Pending' ||
                booking['status'] == 'Confirmed')) {
          upcoming.add(processedBooking);
          print('BookingsScreen: Added to upcoming: ${booking['service']}');
        } else {
          past.add(processedBooking);
          print('BookingsScreen: Added to past: ${booking['service']}');
        }
      }

      // Sort bookings by date
      upcoming.sort(
          (a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));
      past.sort(
          (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));

      if (mounted) {
        setState(() {
          _upcomingBookings = upcoming;
          _pastBookings = past;
          _isLoading = false;
        });

        print(
            'BookingsScreen: Updated UI - Upcoming: ${upcoming.length}, Past: ${past.length}');
      }
    } catch (e) {
      print('BookingsScreen: Error loading bookings: $e');
      if (mounted) {
        setState(() {
          _error = 'Failed to load bookings: $e';
          _isLoading = false;
        });
      }
    }
  }

  // Process booking from API to our format
  Map<String, dynamic> _processBooking(Map<String, dynamic> apiBooking) {
    // Get service info directly from the booking
    final serviceName = apiBooking['service'] ?? 'Unknown Service';
    final categoryName = apiBooking['category'] ?? 'Service';

    // Get address - handle both string and object formats
    String address;
    if (apiBooking['address'] is String) {
      address = apiBooking['address'];
    } else if (apiBooking['address'] is Map) {
      address =
          apiBooking['address']['formattedAddress'] ?? 'No address provided';
    } else {
      address = 'No address provided';
    }

    // Get date and time
    final date = DateTime.parse(apiBooking['date']);
    final time = apiBooking['time'] ?? '12:00 PM';

    // Get price
    final price = apiBooking['price'] ?? 0.0;

    // Get status
    final status = _getStatusText(apiBooking['status'] ?? 'pending');

    // Get icon and color from the booking or use defaults
    final iconCodePoint =
        apiBooking['icon'] ?? Icons.home_repair_service.codePoint;
    final colorValue = apiBooking['color'] ?? Colors.blue.toARGB32();

    return {
      'id': apiBooking['id'],
      'service': serviceName,
      'category': categoryName,
      'date': date,
      'time': time,
      'address': address,
      'price': price,
      'status': status,
      'icon': IconData(iconCodePoint, fontFamily: 'MaterialIcons'),
      'color': Color(colorValue),
    };
  }

  // Helper methods to get category info
  String _getCategoryName(String categoryId) {
    switch (categoryId) {
      case '1':
        return 'Cleaning';
      case '2':
        return 'Plumbing';
      case '3':
        return 'Electrical';
      case '4':
        return 'Carpenter';
      case '5':
        return 'Appliances';
      case '6':
        return 'Painting';
      case '7':
        return 'AC Services';
      case '8':
        return 'Pest Control';
      case '9':
        return 'Car Wash';
      case '10':
        return 'Packers & Movers';
      default:
        return 'Service';
    }
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case '1':
        return Icons.cleaning_services;
      case '2':
        return Icons.plumbing;
      case '3':
        return Icons.electrical_services;
      case '4':
        return Icons.handyman;
      case '5':
        return Icons.kitchen;
      case '6':
        return Icons.format_paint;
      case '7':
        return Icons.ac_unit;
      case '8':
        return Icons.bug_report;
      case '9':
        return Icons.local_car_wash;
      case '10':
        return Icons.local_shipping;
      default:
        return Icons.home_repair_service;
    }
  }

  Color _getCategoryColor(String categoryId) {
    switch (categoryId) {
      case '1':
        return const Color(0xFF4ECDC4);
      case '2':
        return const Color(0xFF6C63FF);
      case '3':
        return const Color(0xFFFF6B6B);
      case '4':
        return const Color(0xFFFFB347);
      case '5':
        return const Color(0xFF5D5FEF);
      case '6':
        return const Color(0xFF00C2A8);
      case '7':
        return const Color(0xFF4A90E2);
      case '8':
        return const Color(0xFFE57373);
      case '9':
        return const Color(0xFF42A5F5);
      case '10':
        return const Color(0xFF9575CD);
      default:
        return const Color(0xFF7D8CA3);
    }
  }

  String _getStatusText(String apiStatus) {
    switch (apiStatus.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'in_progress':
        return 'In Progress';
      default:
        return 'Pending';
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _tabController.dispose();
    super.dispose();
  }

  // Helper function to create colors with opacity
  Color withOpacity(Color color, double opacity) {
    final a = (opacity * 255).round();
    final rgb = color.toARGB32();
    return Color(rgb & 0x00FFFFFF | (a << 24));
  }

  // Convert booking data to Order object
  Order _convertBookingToOrder(Map<String, dynamic> booking) {
    final service = Service(
      id: 'service-${booking['id']}',
      categoryId: 'cat-${booking['category']}',
      name: booking['service'],
      description:
          'Professional ${booking['category'].toLowerCase()} service for your home.',
      price: booking['price'],
      imageUrl: 'assets/images/service1.jpg',
      rating: 4.5,
      reviewCount: 120,
      durationMinutes: 120,
    );

    final category = ServiceCategory(
      id: 'cat-${booking['category']}',
      name: booking['category'],
      icon: booking['icon'],
      color: booking['color'],
    );

    final cartItem = CartItem(
      id: 'item-${booking['id']}',
      service: service,
      category: category,
      quantity: 1,
      date: booking['date'],
      time: booking['time'],
      addons: [],
    );

    final status = booking['status'];
    OrderStatus orderStatus;
    switch (status) {
      case 'Confirmed':
        orderStatus = OrderStatus.confirmed;
        break;
      case 'Pending':
        orderStatus = OrderStatus.pending;
        break;
      case 'Completed':
        orderStatus = OrderStatus.completed;
        break;
      case 'Cancelled':
        orderStatus = OrderStatus.cancelled;
        break;
      default:
        orderStatus = OrderStatus.pending;
    }

    return Order(
      id: booking['id'],
      items: [cartItem],
      address: {
        'formattedAddress': booking['address'],
      },
      date: booking['date'],
      time: booking['time'],
      subtotal: booking['price'],
      tax: booking['price'] * 0.05,
      discount: 0,
      smallOrderFee: booking['price'] < 300 ? 150 : 0,
      total: booking['price'] +
          (booking['price'] * 0.05) +
          (booking['price'] < 300 ? 150 : 0),
      paymentMethod: 'Credit/Debit Card',
      status: orderStatus,
      createdAt: booking['date'].subtract(const Duration(days: 1)),
      technician: orderStatus == OrderStatus.confirmed ||
              orderStatus == OrderStatus.inProgress ||
              orderStatus == OrderStatus.completed
          ? 'John Doe'
          : null,
      technicianPhone: orderStatus == OrderStatus.confirmed ||
              orderStatus == OrderStatus.inProgress ||
              orderStatus == OrderStatus.completed
          ? '+919876543210'
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'My Bookings',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBookings,
            color: const Color(0xFF2D4059),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.black,
          labelColor: Colors.black,
          unselectedLabelColor: const Color(0xFF7D8CA3),
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 60,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.red,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadBookings,
                        child: const Text('Try Again'),
                      ),
                    ],
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    // Upcoming bookings tab
                    RefreshIndicator(
                      onRefresh: _loadBookings,
                      child: _upcomingBookings.isEmpty
                          ? _buildEmptyState('No upcoming bookings')
                          : _buildBookingsList(_upcomingBookings),
                    ),

                    // Past bookings tab
                    RefreshIndicator(
                      onRefresh: _loadBookings,
                      child: _pastBookings.isEmpty
                          ? _buildEmptyState('No past bookings')
                          : _buildBookingsList(_pastBookings),
                    ),
                  ],
                ),
    );
  }

  Widget _buildEmptyState(String message) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.calendar_today,
                size: 80,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadBookings,
                child: const Text('Refresh'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingsList(List<Map<String, dynamic>> bookings) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];
        return _buildBookingCard(booking);
      },
    );
  }

  // Build a booking card
  Widget _buildBookingCard(Map<String, dynamic> booking) {
    return BookingCard(
      booking: booking,
      onTap: () => _navigateToOrderDetails(booking),
      onCancel: () => _showCancelConfirmation(booking),
      showCancelButton: booking['status'] == 'Pending',
    );
  }

  // Navigate to order details screen
  void _navigateToOrderDetails(Map<String, dynamic> booking) {
    // Convert booking to Order and navigate to OrderDetailsScreen
    final order = _convertBookingToOrder(booking);
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            OrderDetailsScreen(order: order),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;
          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );
          var offsetAnimation = animation.drive(tween);
          return SlideTransition(position: offsetAnimation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  // Show cancel confirmation dialog
  void _showCancelConfirmation(Map<String, dynamic> booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: const Text('Are you sure you want to cancel this booking?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showCancellationReasonDialog(booking);
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }

  // Show cancellation reason dialog
  void _showCancellationReasonDialog(Map<String, dynamic> booking) {
    final TextEditingController reasonController = TextEditingController();
    String? selectedReason;

    final List<String> cancellationReasons = [
      'Change of plans',
      'Found a better service provider',
      'Emergency came up',
      'Service no longer needed',
      'Pricing concerns',
      'Scheduling conflict',
      'Other',
    ];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Cancellation Reason'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Please select a reason for cancellation:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 16),
                // Reason dropdown
                DropdownButtonFormField<String>(
                  value: selectedReason,
                  decoration: const InputDecoration(
                    labelText: 'Select Reason',
                    border: OutlineInputBorder(),
                  ),
                  items: cancellationReasons.map((reason) {
                    return DropdownMenuItem(
                      value: reason,
                      child: Text(reason),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedReason = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                // Additional comments field
                TextField(
                  controller: reasonController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Additional Comments (Optional)',
                    hintText: 'Please provide any additional details...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Back'),
            ),
            ElevatedButton(
              onPressed: selectedReason != null
                  ? () {
                      Navigator.of(context).pop();
                      _processCancellation(
                        booking,
                        selectedReason!,
                        reasonController.text.trim(),
                      );
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Submit & Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  // Process the cancellation with reason
  void _processCancellation(
    Map<String, dynamic> booking,
    String reason,
    String additionalComments,
  ) async {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      // Call API to cancel booking with reason
      final apiProvider =
          Provider.of<RealTimeApiProvider>(context, listen: false);

      // Create cancellation data
      final cancellationData = {
        'reason': reason,
        'comments': additionalComments,
        'cancelledAt': DateTime.now().toIso8601String(),
      };

      // For now, use the existing cancelBooking method
      // TODO: Update API to support cancellation reasons
      final success = await apiProvider.cancelBooking(booking['id']);

      if (success) {
        // Reload bookings
        _loadBookings();

        // Show success message with reason
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Booking cancelled successfully.\nReason: $reason',
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          });
        }

        // Log the cancellation reason for future API integration
        print('Booking ${booking['id']} cancelled with reason: $reason');
        if (additionalComments.isNotEmpty) {
          print('Additional comments: $additionalComments');
        }
      } else {
        // Show error message
        setState(() {
          _isLoading = false;
          _error = apiProvider.error ?? 'Failed to cancel booking';
        });

        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(_error ?? 'Failed to cancel booking'),
                backgroundColor: Colors.red,
              ),
            );
          });
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error cancelling booking: $e';
      });

      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error cancelling booking: $e'),
              backgroundColor: Colors.red,
            ),
          );
        });
      }
    }
  }
}
