import 'package:flutter/material.dart';
import 'service.dart';
import 'service_category.dart';
import 'service_addon.dart';

class CartItem {
  final String id;
  final Service service;
  final ServiceCategory category;
  final int quantity;
  final DateTime date;
  final String time;
  final Map<String, dynamic>? address;
  final List<ServiceAddon> addons;

  CartItem({
    required this.id,
    required this.service,
    required this.category,
    required this.quantity,
    required this.date,
    required this.time,
    this.address,
    this.addons = const [],
  });

  double get subtotal => (service.price * quantity) + addonSubtotal;
  double get addonSubtotal =>
      addons.fold(0.0, (sum, addon) => sum + (addon.price * addon.quantity));
  double get tax => subtotal * 0.05; // 5% tax
  double get total => subtotal + tax;
}
