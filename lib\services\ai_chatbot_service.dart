import 'dart:async';
import 'dart:math';

class AIChatbotService {
  static final AIChatbotService _instance = AIChatbotService._internal();
  factory AIChatbotService() => _instance;
  AIChatbotService._internal();

  // Conversation memory
  final List<Map<String, dynamic>> _conversationHistory = [];
  final Map<String, dynamic> _userContext = {};

  // AI personality and knowledge base
  final String _botName = "DodoBot";
  final String _botPersonality = "friendly, helpful, and knowledgeable";

  // Service categories and knowledge
  final Map<String, List<String>> _serviceKnowledge = {
    'cleaning': [
      'house cleaning',
      'deep cleaning',
      'carpet cleaning',
      'window cleaning',
      'bathroom cleaning',
      'kitchen cleaning',
      'office cleaning'
    ],
    'plumbing': [
      'pipe repair',
      'leak fixing',
      'drain cleaning',
      'toilet repair',
      'faucet installation',
      'water heater repair'
    ],
    'electrical': [
      'wiring',
      'switch installation',
      'outlet repair',
      'ceiling fan installation',
      'light fixture',
      'electrical troubleshooting'
    ],
    'carpenter': [
      'furniture repair',
      'door installation',
      'cabinet making',
      'wood work',
      'custom furniture',
      'shelving'
    ],
    'appliances': [
      'washing machine repair',
      'refrigerator repair',
      'microwave repair',
      'dishwasher repair',
      'AC repair'
    ],
    'painting': [
      'wall painting',
      'exterior painting',
      'interior painting',
      'texture painting',
      'color consultation'
    ],
    'pest_control': [
      'termite control',
      'cockroach control',
      'ant control',
      'rodent control',
      'mosquito control'
    ],
    'car_wash': [
      'car cleaning',
      'car detailing',
      'interior cleaning',
      'exterior wash'
    ]
  };

  // Common responses and patterns
  final Map<String, List<String>> _responseTemplates = {
    'greeting': [
      "Hello! I'm DodoBot, your AI assistant. How can I help you today?",
      "Hi there! Welcome to DodoBooker. What service are you looking for?",
      "Greetings! I'm here to help you with all your home service needs.",
    ],
    'booking_intent': [
      "I'd be happy to help you book a service! What type of service do you need?",
      "Great! Let me help you find the perfect service. What are you looking for?",
      "I can definitely help you with booking. Which service interests you?",
    ],
    'service_explanation': [
      "Let me explain our services for you.",
      "Here's what we offer in that category:",
      "Our services include:",
    ],
    'pricing_inquiry': [
      "Our pricing varies based on the specific service and requirements.",
      "Prices depend on the scope of work. Let me help you get an estimate.",
      "I can help you understand our pricing structure.",
    ],
    'availability': [
      "We're available 7 days a week for most services.",
      "Our service providers are typically available from 8 AM to 8 PM.",
      "We can usually schedule services within 24-48 hours.",
    ],
    'confusion': [
      "I'm not sure I understand. Could you please rephrase that?",
      "Could you provide more details about what you're looking for?",
      "I want to make sure I help you correctly. Can you clarify?",
    ],
    'thanks': [
      "You're welcome! Is there anything else I can help you with?",
      "Happy to help! Feel free to ask if you need anything else.",
      "Glad I could assist! Any other questions?",
    ]
  };

  // Intent detection patterns
  final Map<String, List<String>> _intentPatterns = {
    'greeting': [
      'hello',
      'hi',
      'hey',
      'good morning',
      'good afternoon',
      'good evening'
    ],
    'booking': [
      'book',
      'schedule',
      'appointment',
      'service',
      'need',
      'want',
      'hire'
    ],
    'pricing': [
      'price',
      'cost',
      'how much',
      'rate',
      'charge',
      'fee',
      'expensive'
    ],
    'availability': [
      'available',
      'when',
      'time',
      'schedule',
      'today',
      'tomorrow'
    ],
    'cancel': ['cancel', 'cancellation', 'refund', 'change booking'],
    'status': ['status', 'booking status', 'order status', 'track'],
    'help': ['help', 'support', 'assistance', 'problem', 'issue'],
    'thanks': ['thank', 'thanks', 'appreciate', 'grateful'],
    'goodbye': ['bye', 'goodbye', 'see you', 'later', 'exit']
  };

  // Generate AI response
  Future<String> generateResponse(String userMessage, {String? userId}) async {
    // Simulate AI thinking time
    await Future.delayed(Duration(milliseconds: 500 + Random().nextInt(1500)));

    // Add to conversation history
    _addToHistory(userMessage, isUser: true, userId: userId);

    // Detect intent
    final intent = _detectIntent(userMessage.toLowerCase());

    // Generate contextual response
    String response = await _generateContextualResponse(userMessage, intent);

    // Add bot response to history
    _addToHistory(response, isUser: false, userId: userId);

    return response;
  }

  // Detect user intent from message
  String _detectIntent(String message) {
    for (final intent in _intentPatterns.keys) {
      for (final pattern in _intentPatterns[intent]!) {
        if (message.contains(pattern)) {
          return intent;
        }
      }
    }

    // Check for service-specific intents
    for (final service in _serviceKnowledge.keys) {
      for (final keyword in _serviceKnowledge[service]!) {
        if (message.contains(keyword)) {
          return 'service_inquiry';
        }
      }
    }

    return 'general';
  }

  // Generate contextual response based on intent and history
  Future<String> _generateContextualResponse(
      String userMessage, String intent) async {
    switch (intent) {
      case 'greeting':
        return _getRandomResponse('greeting');

      case 'booking':
        return _handleBookingIntent(userMessage);

      case 'service_inquiry':
        return _handleServiceInquiry(userMessage);

      case 'pricing':
        return _handlePricingInquiry(userMessage);

      case 'availability':
        return _getRandomResponse('availability');

      case 'cancel':
        return _handleCancellationRequest(userMessage);

      case 'status':
        return _handleStatusInquiry(userMessage);

      case 'help':
        return _handleHelpRequest(userMessage);

      case 'thanks':
        return _getRandomResponse('thanks');

      case 'goodbye':
        return "Thank you for using DodoBooker! Have a great day! 👋";

      default:
        return _handleGeneralInquiry(userMessage);
    }
  }

  // Handle booking-related requests
  String _handleBookingIntent(String message) {
    final detectedService = _detectServiceFromMessage(message);

    if (detectedService != null) {
      return "Perfect! I can help you book $detectedService services. "
          "Here's what I can do:\n\n"
          "🔹 Show you available services\n"
          "🔹 Check pricing and availability\n"
          "🔹 Schedule an appointment\n"
          "🔹 Connect you with our best professionals\n\n"
          "Would you like me to show you our $detectedService services?";
    }

    return _getRandomResponse('booking_intent') +
        "\n\n"
            "We offer these services:\n"
            "🏠 Cleaning Services\n"
            "🔧 Plumbing\n"
            "⚡ Electrical Work\n"
            "🪚 Carpentry\n"
            "🔨 Appliance Repair\n"
            "🎨 Painting\n"
            "🐛 Pest Control\n"
            "🚗 Car Wash";
  }

  // Handle service-specific inquiries
  String _handleServiceInquiry(String message) {
    final service = _detectServiceFromMessage(message);

    if (service != null) {
      final serviceList = _serviceKnowledge[service]!;
      return "Great choice! Our $service services include:\n\n" +
          serviceList.map((s) => "• ${s.toUpperCase()}").join('\n') +
          "\n\nWould you like me to:\n"
              "📋 Show detailed pricing\n"
              "📅 Check availability\n"
              "📞 Book a service\n"
              "💬 Connect with a specialist";
    }

    return _getRandomResponse('service_explanation');
  }

  // Handle pricing inquiries
  String _handlePricingInquiry(String message) {
    final service = _detectServiceFromMessage(message);

    if (service != null) {
      return "Here's our pricing structure for $service:\n\n"
          "💰 Basic Service: ₹299 - ₹599\n"
          "⭐ Premium Service: ₹599 - ₹999\n"
          "🌟 Deluxe Package: ₹999 - ₹1,499\n\n"
          "Factors affecting price:\n"
          "• Service complexity\n"
          "• Time required\n"
          "• Materials needed\n"
          "• Location\n\n"
          "Would you like a personalized quote?";
    }

    return _getRandomResponse('pricing_inquiry') +
        "\n\n"
            "💡 Tip: Prices start from ₹299 and vary based on service type and requirements.";
  }

  // Handle cancellation requests
  String _handleCancellationRequest(String message) {
    return "I understand you need to cancel a booking. I can help with that!\n\n"
        "To cancel your booking:\n"
        "1️⃣ Go to 'My Bookings' in the app\n"
        "2️⃣ Find your booking\n"
        "3️⃣ Tap 'Cancel' and select a reason\n\n"
        "📋 Cancellation Policy:\n"
        "• Free cancellation up to 2 hours before service\n"
        "• Refunds processed within 3-5 business days\n\n"
        "Need help finding your booking? I can guide you!";
  }

  // Handle status inquiries
  String _handleStatusInquiry(String message) {
    return "I can help you check your booking status! 📊\n\n"
        "To check status:\n"
        "1️⃣ Open 'My Bookings' section\n"
        "2️⃣ View real-time updates\n\n"
        "📱 Status Types:\n"
        "🟡 Pending - Waiting for confirmation\n"
        "🔵 Confirmed - Service provider assigned\n"
        "🟢 In Progress - Service being performed\n"
        "✅ Completed - Service finished\n"
        "❌ Cancelled - Booking cancelled\n\n"
        "Would you like me to help you navigate to your bookings?";
  }

  // Handle help requests
  String _handleHelpRequest(String message) {
    return "I'm here to help! 🤖 Here's what I can assist you with:\n\n"
        "🔹 **Booking Services** - Find and book any home service\n"
        "🔹 **Pricing Info** - Get quotes and pricing details\n"
        "🔹 **Scheduling** - Check availability and book appointments\n"
        "🔹 **Order Status** - Track your bookings\n"
        "🔹 **Cancellations** - Cancel or modify bookings\n"
        "🔹 **Payment Help** - Payment methods and billing\n"
        "🔹 **Service Info** - Learn about our services\n\n"
        "Just tell me what you need help with, and I'll guide you step by step!";
  }

  // Handle general inquiries
  String _handleGeneralInquiry(String message) {
    // Try to provide helpful response based on keywords
    if (message.contains('payment') || message.contains('pay')) {
      return "We accept multiple payment methods:\n\n"
          "💳 Credit/Debit Cards\n"
          "📱 UPI (Google Pay, PhonePe, Paytm)\n"
          "💵 Cash on Delivery\n\n"
          "All payments are secure and encrypted. Need help with a specific payment issue?";
    }

    if (message.contains('location') || message.contains('area')) {
      return "We provide services across multiple locations! 📍\n\n"
          "Currently serving:\n"
          "🏙️ Major cities and suburbs\n"
          "🏘️ Residential areas\n"
          "🏢 Commercial zones\n\n"
          "Enter your address during booking to check if we serve your area.";
    }

    return _getRandomResponse('confusion') +
        "\n\n"
            "I can help you with:\n"
            "• Booking services 📅\n"
            "• Checking prices 💰\n"
            "• Service information ℹ️\n"
            "• Order status 📊\n"
            "• General support 🆘";
  }

  // Utility methods
  String? _detectServiceFromMessage(String message) {
    for (final service in _serviceKnowledge.keys) {
      if (message.contains(service)) return service;
      for (final keyword in _serviceKnowledge[service]!) {
        if (message.contains(keyword)) return service;
      }
    }
    return null;
  }

  String _getRandomResponse(String category) {
    final responses = _responseTemplates[category] ?? ['I understand.'];
    return responses[Random().nextInt(responses.length)];
  }

  void _addToHistory(String message, {required bool isUser, String? userId}) {
    _conversationHistory.add({
      'message': message,
      'isUser': isUser,
      'timestamp': DateTime.now().toIso8601String(),
      'userId': userId,
    });

    // Keep only last 20 messages to manage memory
    if (_conversationHistory.length > 20) {
      _conversationHistory.removeAt(0);
    }
  }

  // Get conversation history
  List<Map<String, dynamic>> getConversationHistory() {
    return List.from(_conversationHistory);
  }

  // Clear conversation
  void clearConversation() {
    _conversationHistory.clear();
    _userContext.clear();
  }

  // Advanced features

  // Generate quick reply suggestions
  List<String> generateQuickReplies(String lastBotMessage) {
    if (lastBotMessage.contains('services')) {
      return [
        'Show me cleaning services',
        'I need plumbing help',
        'Electrical services',
        'Check pricing'
      ];
    }

    if (lastBotMessage.contains('pricing') ||
        lastBotMessage.contains('price')) {
      return ['Get a quote', 'Compare prices', 'Book now', 'More details'];
    }

    if (lastBotMessage.contains('booking') || lastBotMessage.contains('book')) {
      return [
        'Yes, book now',
        'Check availability',
        'See more options',
        'Not now'
      ];
    }

    return ['Tell me more', 'Book a service', 'Check prices', 'Help me choose'];
  }

  // Analyze user sentiment
  String analyzeSentiment(String message) {
    final positiveWords = [
      'good',
      'great',
      'excellent',
      'amazing',
      'perfect',
      'love',
      'like'
    ];
    final negativeWords = [
      'bad',
      'terrible',
      'awful',
      'hate',
      'problem',
      'issue',
      'wrong'
    ];

    final lowerMessage = message.toLowerCase();
    int positiveScore = 0;
    int negativeScore = 0;

    for (final word in positiveWords) {
      if (lowerMessage.contains(word)) positiveScore++;
    }

    for (final word in negativeWords) {
      if (lowerMessage.contains(word)) negativeScore++;
    }

    if (positiveScore > negativeScore) return 'positive';
    if (negativeScore > positiveScore) return 'negative';
    return 'neutral';
  }

  // Generate contextual follow-up questions
  String generateFollowUpQuestion(String userMessage, String intent) {
    switch (intent) {
      case 'booking':
        return "What's your preferred date and time for the service?";
      case 'service_inquiry':
        return "Would you like me to show you our top-rated professionals in your area?";
      case 'pricing':
        return "What's your budget range for this service?";
      default:
        return "Is there anything specific you'd like to know more about?";
    }
  }

  // Smart service recommendations
  List<String> getServiceRecommendations(String userMessage) {
    final message = userMessage.toLowerCase();

    if (message.contains('clean') ||
        message.contains('dirty') ||
        message.contains('mess')) {
      return ['Deep Cleaning', 'Regular Cleaning', 'Carpet Cleaning'];
    }

    if (message.contains('leak') ||
        message.contains('pipe') ||
        message.contains('water')) {
      return ['Plumbing Repair', 'Leak Detection', 'Pipe Installation'];
    }

    if (message.contains('electric') ||
        message.contains('power') ||
        message.contains('light')) {
      return ['Electrical Repair', 'Wiring', 'Switch Installation'];
    }

    return ['Popular Services', 'Recommended for You', 'Trending Now'];
  }

  // Get conversation summary
  String getConversationSummary() {
    if (_conversationHistory.isEmpty) return 'No conversation yet';

    final userMessages =
        _conversationHistory.where((msg) => msg['isUser'] == true).length;
    final botMessages =
        _conversationHistory.where((msg) => msg['isUser'] == false).length;

    return 'Conversation: $userMessages user messages, $botMessages bot responses';
  }

  // Get bot info
  Map<String, dynamic> getBotInfo() {
    return {
      'name': _botName,
      'personality': _botPersonality,
      'capabilities': [
        'Service booking assistance',
        'Pricing information',
        'Scheduling help',
        'Order tracking',
        'Payment support',
        'General customer support',
        'Smart recommendations',
        'Sentiment analysis',
        'Quick replies',
        'Follow-up questions'
      ],
      'version': '2.0.0',
      'features': [
        'Natural language understanding',
        'Context-aware responses',
        'Multi-turn conversations',
        'Service recommendations',
        'Intelligent suggestions'
      ]
    };
  }

  // Export conversation for analysis
  Map<String, dynamic> exportConversation() {
    return {
      'conversation_id': DateTime.now().millisecondsSinceEpoch.toString(),
      'messages': _conversationHistory,
      'user_context': _userContext,
      'summary': getConversationSummary(),
      'exported_at': DateTime.now().toIso8601String(),
    };
  }
}
