# Phone Authentication Setup for DodoBooker

## Current Status
Your app is currently using **mock phone authentication** to avoid the "phone_provider_disabled" error. This allows you to test the app while you set up proper phone authentication.

## Error Explanation
The error "Unsupported phone provider, statusCode: 400, code: phone_provider_disabled" occurs because:
1. Phone authentication is not enabled in your Supabase project
2. No SMS provider is configured
3. Supabase cannot send real OTP messages

## Quick Fix (Current Implementation)
I've temporarily set `_useSupabase = false` in `AuthService`, which means:
- ✅ **Phone login works** with mock OTP (123456)
- ✅ **Google sign-in works** with Supabase OAuth
- ✅ **App functions normally** for testing
- ✅ **No errors** during authentication

## Setting Up Real Phone Authentication

### Step 1: Enable Phone Provider in Supabase

1. **Go to Supabase Dashboard**:
   - Visit: https://supabase.com/dashboard
   - Select your DodoBooker project

2. **Enable Phone Authentication**:
   - Navigate to **Authentication** → **Providers**
   - Find **Phone** in the providers list
   - Toggle **Enable** to ON

### Step 2: Configure SMS Provider

You need to choose and configure an SMS provider. Here are the options:

#### Option A: Twilio (Recommended)
1. **Create Twilio Account**: https://www.twilio.com/
2. **Get Credentials**:
   - Account SID
   - Auth Token
   - Phone Number
3. **Configure in Supabase**:
   - In Phone provider settings
   - Enter Twilio credentials
   - Set webhook URL

#### Option B: MessageBird
1. **Create MessageBird Account**: https://www.messagebird.com/
2. **Get API Key**
3. **Configure in Supabase**

#### Option C: Textlocal
1. **Create Textlocal Account**: https://www.textlocal.com/
2. **Get API Key**
3. **Configure in Supabase**

### Step 3: Configure Phone Settings

In Supabase Phone provider settings:

```
SMS Provider: [Your chosen provider]
Account SID: [Your provider SID]
Auth Token: [Your provider token]
Phone Number: [Your provider phone number]
```

### Step 4: Test Phone Authentication

1. **Enable Supabase in Code**:
   ```dart
   // In lib/services/auth_service.dart
   bool _useSupabase = true; // Change from false to true
   ```

2. **Test with Real Phone**:
   - Use your actual phone number
   - Receive real OTP via SMS
   - Enter the received OTP

### Step 5: Development vs Production

#### For Development:
- Keep `_useSupabase = false` for mock authentication
- Use phone: `**********` and OTP: `123456`
- No SMS costs, instant testing

#### For Production:
- Set `_useSupabase = true` for real authentication
- Configure SMS provider properly
- Users receive real OTP messages

## Cost Considerations

### SMS Provider Costs:
- **Twilio**: ~$0.0075 per SMS
- **MessageBird**: ~$0.05 per SMS
- **Textlocal**: Varies by region

### Development Strategy:
1. **Use mock auth** during development (free)
2. **Enable real SMS** only for production
3. **Test with small user group** first

## Current App Behavior

### With Mock Authentication (`_useSupabase = false`):
- ✅ Phone: `**********` → OTP: `123456` → Success
- ✅ Google sign-in → Works with Supabase OAuth
- ✅ User profiles → Stored locally
- ✅ Cart/Bookings → Uses mock data

### With Real Authentication (`_useSupabase = true`):
- 📱 Phone: Any real number → Real OTP via SMS → Success
- ✅ Google sign-in → Works with Supabase OAuth
- ✅ User profiles → Stored in Supabase database
- ✅ Cart/Bookings → Uses Supabase database

## Switching Between Mock and Real Auth

### To Enable Real Phone Auth:
1. Configure SMS provider in Supabase
2. Change `_useSupabase = true` in `auth_service.dart`
3. Test with real phone numbers

### To Keep Mock Auth:
1. Keep `_useSupabase = false` in `auth_service.dart`
2. Use test credentials: Phone `**********`, OTP `123456`
3. No SMS provider needed

## Testing Strategy

### Phase 1: Development (Current)
- Mock phone authentication
- Real Google authentication
- Local data storage
- No SMS costs

### Phase 2: Pre-Production
- Real phone authentication
- Real Google authentication
- Supabase database
- Limited SMS testing

### Phase 3: Production
- All real authentication
- Full Supabase integration
- Production SMS provider
- Real user onboarding

## Troubleshooting

### Common Issues:

1. **"phone_provider_disabled" error**:
   - Solution: Set `_useSupabase = false` for mock auth
   - Or: Configure SMS provider in Supabase

2. **SMS not received**:
   - Check SMS provider configuration
   - Verify phone number format
   - Check SMS provider balance

3. **Invalid OTP error**:
   - With mock auth: Use `123456`
   - With real auth: Use the SMS OTP received

## Recommendation

**For immediate development**: Keep the current mock authentication setup
**For production launch**: Configure Twilio or MessageBird SMS provider

The current implementation allows you to:
- ✅ Test all app features
- ✅ Develop without SMS costs
- ✅ Use Google sign-in with real Supabase integration
- ✅ Switch to real phone auth when ready

Your app is fully functional with the current setup!
