import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'base_mock_api_service.dart';

/// Comprehensive real-time mock API service that provides endpoints for all app pages
/// with real-time data simulation and proper logging
class RealTimeMockApiService extends BaseMockApiService {
  // Singleton pattern
  static final RealTimeMockApiService _instance =
      RealTimeMockApiService._internal();
  factory RealTimeMockApiService() => _instance;

  // In-memory database for realtime data
  final Map<String, dynamic> _database = {};

  // Stream controllers for realtime updates
  final StreamController<List<Map<String, dynamic>>>
      _categoriesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _subcategoriesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _productsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<Map<String, dynamic>> _cartStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _bookingsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<Map<String, dynamic>> _profileStreamController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _chatMessagesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _bannersStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _notificationsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _addressesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>> _couponsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _featuredServicesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<List<Map<String, dynamic>>>
      _recentServicesStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final StreamController<Map<String, dynamic>> _authStreamController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Getters for streams
  Stream<List<Map<String, dynamic>>> get categoriesStream =>
      _categoriesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get subcategoriesStream =>
      _subcategoriesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get productsStream =>
      _productsStreamController.stream;
  Stream<Map<String, dynamic>> get cartStream => _cartStreamController.stream;
  Stream<List<Map<String, dynamic>>> get bookingsStream =>
      _bookingsStreamController.stream;
  Stream<Map<String, dynamic>> get profileStream =>
      _profileStreamController.stream;
  Stream<List<Map<String, dynamic>>> get chatMessagesStream =>
      _chatMessagesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get bannersStream =>
      _bannersStreamController.stream;
  Stream<List<Map<String, dynamic>>> get notificationsStream =>
      _notificationsStreamController.stream;
  Stream<List<Map<String, dynamic>>> get addressesStream =>
      _addressesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get couponsStream =>
      _couponsStreamController.stream;
  Stream<List<Map<String, dynamic>>> get featuredServicesStream =>
      _featuredServicesStreamController.stream;
  Stream<List<Map<String, dynamic>>> get recentServicesStream =>
      _recentServicesStreamController.stream;
  Stream<Map<String, dynamic>> get authStream => _authStreamController.stream;

  // Current user ID (set after authentication)
  String? _currentUserId;

  // Mock support agent
  late final Map<String, dynamic> _supportAgent;

  RealTimeMockApiService._internal() {
    // Initialize the in-memory database
    _initializeDatabase();

    // Set up periodic updates to simulate realtime data changes
    Timer.periodic(const Duration(minutes: 5), (_) {
      _simulateDataUpdates();
    });

    // Set up automated chat responses
    Timer.periodic(const Duration(seconds: 30), (_) {
      _simulateAgentResponses();
    });

    // Set up periodic notifications
    Timer.periodic(const Duration(minutes: 15), (_) {
      _simulateNewNotifications();
    });

    // Set up periodic booking status updates
    Timer.periodic(const Duration(minutes: 10), (_) {
      _simulateBookingStatusUpdates();
    });
  }

  // Initialize the in-memory database with data
  void _initializeDatabase() {
    _initializeCategories();
    _initializeProducts();
    _initializeUsers();
    _initializeBookings();
    _initializeCart();
    _initializeChatMessages();
    _initializeNotifications();
    _initializeBanners();
    _initializeAddresses();
    _initializeCoupons();

    // Notify listeners about initial data
    _categoriesStreamController.add(_database['categories'] ?? []);
    _subcategoriesStreamController.add(_database['subcategories'] ?? []);
    _productsStreamController.add(_database['products'] ?? []);
    _bannersStreamController.add(_database['banners'] ?? []);
    _featuredServicesStreamController.add(_database['featuredServices'] ?? []);
    _recentServicesStreamController.add(_database['recentServices'] ?? []);
    _couponsStreamController.add(_database['coupons'] ?? []);

    // Initialize auth state
    _authStreamController.add({
      'isAuthenticated': false,
      'user': null,
    });
  }

  // Method to refresh all data (clears cache and reinitializes)
  Future<Map<String, dynamic>> refreshAllData() async {
    await simulateNetworkDelay();

    print(
        'MockAPI: Refreshing all data - clearing cache and reinitializing...');

    // Clear the entire database
    _database.clear();

    // Reinitialize all data
    _initializeDatabase();

    // If user is logged in, reinitialize user-specific data
    if (_currentUserId != null) {
      _initializeUserCart();
      _initializeUserBookings();
      _initializeUserChatMessages();
      _initializeUserNotifications();
      _initializeUserAddresses();

      // Notify listeners about refreshed user data
      final user = _database['users']?.firstWhere(
        (u) => u['id'] == _currentUserId,
        orElse: () => null,
      );

      if (user != null) {
        _profileStreamController.add(user);
        _cartStreamController
            .add(_database['carts'][_currentUserId] ?? {'items': []});
        _bookingsStreamController
            .add(_database['userBookings'][_currentUserId] ?? []);
        _chatMessagesStreamController
            .add(_database['userChatMessages'][_currentUserId] ?? []);
        _notificationsStreamController
            .add(_database['userNotifications'][_currentUserId] ?? []);
        _addressesStreamController
            .add(_database['userAddresses'][_currentUserId] ?? []);
      }
    }

    print('MockAPI: Data refresh completed successfully');

    logApiCall('refreshAllData');

    return createSuccessResponse({
      'message': 'All data refreshed successfully',
      'timestamp': DateTime.now().toIso8601String(),
      'categoriesCount': (_database['categories'] as List?)?.length ?? 0,
      'productsCount': (_database['products'] as List?)?.length ?? 0,
    });
  }

  // Set current user after authentication
  void setCurrentUser(String userId, {String? mobile}) {
    _currentUserId = userId;

    print(
        'MockAPI: setCurrentUser called with userId: $userId, mobile: $mobile');

    // Find user in database
    final users = List<Map<String, dynamic>>.from(_database['users']);
    var user = users.firstWhere(
      (u) => u['id'] == userId,
      orElse: () => <String, dynamic>{},
    );

    if (user.isEmpty) {
      print('MockAPI: User not found, creating new user with ID: $userId');
      // Create a new user if not found
      user = {
        'id': userId,
        'mobile': mobile ?? '9876543210', // Use provided mobile or default
        'name': 'New User',
        'email': '',
        'profilePic': 'https://randomuser.me/api/portraits/men/32.jpg',
        'createdAt': DateTime.now().toIso8601String(),
        'lastLogin': DateTime.now().toIso8601String(),
        'isProfileComplete': false,
      };

      users.add(user);
      _database['users'] = users;
      print(
          'MockAPI: Created new user: ${user['name']} with ID: ${user['id']}');
    } else {
      print(
          'MockAPI: Found existing user: ${user['name']} with ID: ${user['id']}');
      // Update last login for existing user
      user['lastLogin'] = DateTime.now().toIso8601String();
      final userIndex = users.indexWhere((u) => u['id'] == userId);
      if (userIndex >= 0) {
        users[userIndex] = user;
        _database['users'] = users;
      }
    }

    // Initialize user-specific data
    _initializeUserCart();
    _initializeUserBookings();
    _initializeUserChatMessages();
    _initializeUserNotifications();
    _initializeUserAddresses();

    // Notify listeners about user data
    _profileStreamController.add(user);
    _cartStreamController.add(_database['carts'][userId] ?? {'items': []});
    _bookingsStreamController.add(_database['userBookings'][userId] ?? []);
    _chatMessagesStreamController
        .add(_database['userChatMessages'][userId] ?? []);
    _notificationsStreamController
        .add(_database['userNotifications'][userId] ?? []);
    _addressesStreamController.add(_database['userAddresses'][userId] ?? []);

    // Update auth state
    _authStreamController.add({
      'isAuthenticated': true,
      'user': user,
    });

    logApiCall('setCurrentUser', params: {'userId': userId});
  }

  // Authentication methods
  Future<Map<String, dynamic>> sendOtp(String mobile) async {
    await simulateNetworkDelay();

    logApiCall('sendOtp', params: {'mobile': mobile});

    // Generate a mock OTP
    const otp = '123456';

    // Store the mobile and OTP in the database for verification
    if (!_database.containsKey('otps')) {
      _database['otps'] = {};
    }
    _database['otps'][mobile] = otp;

    return createSuccessResponse({
      'message': 'OTP sent successfully. Use $otp for testing.',
      'otp': otp,
    });
  }

  Future<Map<String, dynamic>> verifyOtp(String mobile, String otp) async {
    await simulateNetworkDelay();

    logApiCall('verifyOtp', params: {'mobile': mobile, 'otp': otp});

    print('MockAPI: verifyOtp - Checking OTP for mobile: $mobile');

    // Check if the OTP is valid
    final storedOtp = _database['otps']?[mobile];

    if (storedOtp == null) {
      return createErrorResponse('No OTP found for this mobile number');
    }

    if (storedOtp != otp) {
      return createErrorResponse('Invalid OTP');
    }

    print('MockAPI: verifyOtp - OTP verified successfully');

    // Find existing user by mobile number first
    final users = List<Map<String, dynamic>>.from(_database['users'] ?? []);
    var user = users.firstWhere(
      (u) => u['mobile'] == mobile,
      orElse: () => <String, dynamic>{},
    );

    if (user.isEmpty) {
      print('MockAPI: verifyOtp - Creating new user for mobile: $mobile');
      // Create a new user
      user = {
        'id': 'user_${mobile}_${DateTime.now().millisecondsSinceEpoch}',
        'mobile': mobile,
        'name': 'User ${mobile.substring(mobile.length - 4)}',
        'email': '',
        'profilePic':
            'https://randomuser.me/api/portraits/men/${mobile.hashCode % 100}.jpg',
        'createdAt': DateTime.now().toIso8601String(),
        'lastLogin': DateTime.now().toIso8601String(),
        'isProfileComplete': false,
      };

      users.add(user);
      _database['users'] = users;
      print('MockAPI: verifyOtp - New user created with ID: ${user['id']}');
    } else {
      print(
          'MockAPI: verifyOtp - Found existing user: ${user['name']} (${user['id']})');
      // Update last login for existing user
      user['lastLogin'] = DateTime.now().toIso8601String();

      // Update the user in the database
      final index = users.indexWhere((u) => u['id'] == user['id']);
      if (index >= 0) {
        users[index] = user;
        _database['users'] = users;
      }
    }

    // Clear the OTP
    _database['otps'].remove(mobile);

    print(
        'MockAPI: verifyOtp - Returning user: ${user['id']}, isProfileComplete: ${user['isProfileComplete']}');

    return createSuccessResponse({
      'message': 'OTP verified successfully',
      'userId': user['id'],
      'user': user,
      'isNewUser': user['isProfileComplete'] != true,
    });
  }

  // Logout current user
  Future<Map<String, dynamic>> logout() async {
    await simulateNetworkDelay();

    print('MockAPI: Logging out user: $_currentUserId');

    _currentUserId = null;

    // Update auth state
    _authStreamController.add({
      'isAuthenticated': false,
      'user': null,
    });

    print('MockAPI: User logged out successfully, currentUserId set to null');

    return createSuccessResponse({'message': 'Logged out successfully'});
  }

  // Category methods
  Future<Map<String, dynamic>> getCategories(
      {bool forceRefresh = false}) async {
    await simulateNetworkDelay();

    logApiCall('getCategories');

    // Force refresh if requested
    if (forceRefresh) {
      print('MockAPI: Force refreshing categories data...');
      _initializeCategories();
      _categoriesStreamController.add(_database['categories'] ?? []);
    }

    final categories =
        List<Map<String, dynamic>>.from(_database['categories'] ?? []);

    print('MockAPI: getCategories returned ${categories.length} categories');
    return createSuccessResponse(categories);
  }

  // Method to add a new category dynamically (for testing purposes)
  Future<Map<String, dynamic>> addTestCategory() async {
    await simulateNetworkDelay();

    final categories =
        List<Map<String, dynamic>>.from(_database['categories'] ?? []);

    // Add a test category
    final newCategory = {
      'id': 'test-${DateTime.now().millisecondsSinceEpoch}',
      'name': 'Test Category ${categories.length + 1}',
      'icon': Icons.build.codePoint,
      'color': Colors.purple.value,
      'description': 'This is a dynamically added test category',
      'isActive': true,
      'subcategories': [],
    };

    categories.add(newCategory);
    _database['categories'] = categories;

    // Notify listeners
    _categoriesStreamController.add(categories);

    print('MockAPI: Added test category: ${newCategory['name']}');
    logApiCall('addTestCategory');

    return createSuccessResponse({
      'message': 'Test category added successfully',
      'category': newCategory,
      'totalCategories': categories.length,
    });
  }

  // Product methods
  Future<Map<String, dynamic>> getProductsByCategory(String categoryId) async {
    await simulateNetworkDelay();

    logApiCall('getProductsByCategory', params: {'categoryId': categoryId});

    final allProducts =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);

    // Filter products by category ID
    final products = allProducts.where((product) {
      // Check if the product has a categoryId field
      if (product.containsKey('categoryId')) {
        return product['categoryId'] == categoryId;
      }

      // If no categoryId field, check if the id starts with the category prefix
      final id = product['id'] as String;
      if (categoryId.contains('-')) {
        // For subcategories (e.g., "1-1")
        return id.startsWith(categoryId.split('-')[0]);
      } else {
        // For main categories (e.g., "1")
        return id.startsWith(categoryId);
      }
    }).toList();

    return createSuccessResponse(products);
  }

  Future<Map<String, dynamic>> getProductDetails(String productId) async {
    await simulateNetworkDelay();

    logApiCall('getProductDetails', params: {'productId': productId});

    final allProducts =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);

    // Find the product by ID
    final product = allProducts.firstWhere(
      (p) => p['id'] == productId,
      orElse: () => <String, dynamic>{},
    );

    if (product.isEmpty) {
      return createErrorResponse('Product not found');
    }

    return createSuccessResponse(product);
  }

  Future<Map<String, dynamic>> searchProducts(String query) async {
    await simulateNetworkDelay();

    logApiCall('searchProducts', params: {'query': query});

    if (query.isEmpty) {
      return createSuccessResponse([]);
    }

    final allProducts =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);

    // Normalize the search query
    final searchQuery = query.toLowerCase().trim();

    // Filter products by search query
    final products = allProducts.where((product) {
      final name = product['name'].toString().toLowerCase();
      final description = product['description'].toString().toLowerCase();

      return name.contains(searchQuery) || description.contains(searchQuery);
    }).toList();

    return createSuccessResponse(products);
  }

  // Cart methods
  Future<Map<String, dynamic>> addToCart(String productId, int quantity) async {
    await simulateNetworkDelay();

    logApiCall('addToCart',
        params: {'productId': productId, 'quantity': quantity});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Get the product
    final allProducts =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);
    final product = allProducts.firstWhere(
      (p) => p['id'] == productId,
      orElse: () => <String, dynamic>{},
    );

    if (product.isEmpty) {
      return createErrorResponse('Product not found');
    }

    // Get the user's cart
    if (!_database.containsKey('carts')) {
      _database['carts'] = {};
    }

    if (!_database['carts'].containsKey(_currentUserId)) {
      _database['carts'][_currentUserId!] = {
        'userId': _currentUserId,
        'items': <Map<String, dynamic>>[],
        'subtotal': 0,
        'tax': 0,
        'discount': 0,
        'total': 0,
        'couponCode': null,
        'couponDiscount': 0,
        'smallOrderFee': 0,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }

    final cart = Map<String, dynamic>.from(_database['carts'][_currentUserId!]);
    final items = List<Map<String, dynamic>>.from(cart['items']);

    // Check if the product is already in the cart
    final existingItemIndex =
        items.indexWhere((item) => item['productId'] == productId);

    if (existingItemIndex >= 0) {
      // Update the quantity
      items[existingItemIndex]['quantity'] = quantity;
    } else {
      // Add the product to the cart
      items.add({
        'productId': productId,
        'name': product['name'],
        'price': product['price'],
        'quantity': quantity,
        'imageUrl': product['imageUrl'],
        'addedAt': DateTime.now().toIso8601String(),
      });
    }

    // Update the cart
    cart['items'] = items;

    // Calculate totals
    _calculateCartTotals(cart);

    // Update the cart in the database
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return createSuccessResponse(cart);
  }

  Future<Map<String, dynamic>> removeFromCart(String productId) async {
    await simulateNetworkDelay();

    logApiCall('removeFromCart', params: {'productId': productId});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Get the user's cart
    if (!_database.containsKey('carts') ||
        !_database['carts'].containsKey(_currentUserId)) {
      return createErrorResponse('Cart not found');
    }

    final cart = Map<String, dynamic>.from(_database['carts'][_currentUserId!]);
    final items = List<Map<String, dynamic>>.from(cart['items']);

    // Remove the product from the cart
    final newItems =
        items.where((item) => item['productId'] != productId).toList();

    // Update the cart
    cart['items'] = newItems;

    // Calculate totals
    _calculateCartTotals(cart);

    // Update the cart in the database
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return createSuccessResponse(cart);
  }

  Future<Map<String, dynamic>> updateCartItemQuantity(
      String productId, int quantity) async {
    await simulateNetworkDelay();

    logApiCall('updateCartItemQuantity',
        params: {'productId': productId, 'quantity': quantity});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Get the user's cart
    if (!_database.containsKey('carts') ||
        !_database['carts'].containsKey(_currentUserId)) {
      return createErrorResponse('Cart not found');
    }

    final cart = Map<String, dynamic>.from(_database['carts'][_currentUserId!]);
    final items = List<Map<String, dynamic>>.from(cart['items']);

    // Find the product in the cart
    final itemIndex =
        items.indexWhere((item) => item['productId'] == productId);

    if (itemIndex < 0) {
      return createErrorResponse('Product not found in cart');
    }

    // Update the quantity
    items[itemIndex]['quantity'] = quantity;

    // Update the cart
    cart['items'] = items;

    // Calculate totals
    _calculateCartTotals(cart);

    // Update the cart in the database
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return createSuccessResponse(cart);
  }

  Future<Map<String, dynamic>> clearCart() async {
    await simulateNetworkDelay();

    logApiCall('clearCart');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Get the user's cart
    if (!_database.containsKey('carts') ||
        !_database['carts'].containsKey(_currentUserId)) {
      return createErrorResponse('Cart not found');
    }

    final cart = Map<String, dynamic>.from(_database['carts'][_currentUserId!]);

    // Clear the cart
    cart['items'] = <Map<String, dynamic>>[];
    cart['subtotal'] = 0;
    cart['tax'] = 0;
    cart['discount'] = 0;
    cart['total'] = 0;
    cart['couponCode'] = null;
    cart['couponDiscount'] = 0;
    cart['smallOrderFee'] = 0;
    cart['lastUpdated'] = DateTime.now().toIso8601String();

    // Update the cart in the database
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return createSuccessResponse(cart);
  }

  Future<Map<String, dynamic>> applyCoupon(String couponCode) async {
    await simulateNetworkDelay();

    logApiCall('applyCoupon', params: {'couponCode': couponCode});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Get the user's cart
    if (!_database.containsKey('carts') ||
        !_database['carts'].containsKey(_currentUserId)) {
      return createErrorResponse('Cart not found');
    }

    // Get the coupon
    final coupons = List<Map<String, dynamic>>.from(_database['coupons'] ?? []);
    final coupon = coupons.firstWhere(
      (c) => c['code'] == couponCode && c['isActive'] == true,
      orElse: () => <String, dynamic>{},
    );

    if (coupon.isEmpty) {
      return createErrorResponse('Invalid or expired coupon');
    }

    // Check if the coupon is valid
    final validFrom = DateTime.parse(coupon['validFrom']);
    final validUntil = DateTime.parse(coupon['validUntil']);
    final now = DateTime.now();

    if (now.isBefore(validFrom) || now.isAfter(validUntil)) {
      return createErrorResponse('Coupon is not valid at this time');
    }

    // Get the cart
    final cart = Map<String, dynamic>.from(_database['carts'][_currentUserId!]);

    // Check minimum order value
    final subtotal = cart['subtotal'] as num;
    final minOrderValue = coupon['minOrderValue'] as num;

    if (subtotal < minOrderValue) {
      return createErrorResponse(
          'Minimum order value of ₹$minOrderValue required for this coupon');
    }

    // Apply the coupon
    cart['couponCode'] = couponCode;

    // Calculate discount
    final discountType = coupon['discountType'];
    final discountValue = coupon['discountValue'] as num;
    num couponDiscount = 0;

    if (discountType == 'percentage') {
      couponDiscount = subtotal * (discountValue / 100);

      // Check max discount
      if (coupon.containsKey('maxDiscount')) {
        final maxDiscount = coupon['maxDiscount'] as num;
        couponDiscount =
            couponDiscount > maxDiscount ? maxDiscount : couponDiscount;
      }
    } else if (discountType == 'fixed') {
      couponDiscount = discountValue;
    }

    cart['couponDiscount'] = couponDiscount;

    // Calculate totals
    _calculateCartTotals(cart);

    // Update the cart in the database
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return createSuccessResponse(cart);
  }

  Future<Map<String, dynamic>> removeCoupon() async {
    await simulateNetworkDelay();

    logApiCall('removeCoupon');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Get the user's cart
    if (!_database.containsKey('carts') ||
        !_database['carts'].containsKey(_currentUserId)) {
      return createErrorResponse('Cart not found');
    }

    final cart = Map<String, dynamic>.from(_database['carts'][_currentUserId!]);

    // Remove the coupon
    cart['couponCode'] = null;
    cart['couponDiscount'] = 0;

    // Calculate totals
    _calculateCartTotals(cart);

    // Update the cart in the database
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);

    return createSuccessResponse(cart);
  }

  // Helper method to calculate cart totals
  void _calculateCartTotals(Map<String, dynamic> cart) {
    final items = List<Map<String, dynamic>>.from(cart['items']);

    // Calculate subtotal
    num subtotal = 0;
    for (final item in items) {
      subtotal += (item['price'] as num) * (item['quantity'] as num);
    }

    // Apply coupon discount
    final couponDiscount = cart['couponDiscount'] as num;

    // Calculate tax (5%)
    const taxRate = 0.05;
    final tax = (subtotal - couponDiscount) * taxRate;

    // Calculate small order fee
    num smallOrderFee = 0;
    if (subtotal < 300 && items.isNotEmpty) {
      smallOrderFee = 49;
    }

    // Calculate total
    final total = subtotal - couponDiscount + tax + smallOrderFee;

    // Update cart
    cart['subtotal'] = subtotal;
    cart['tax'] = tax;
    cart['smallOrderFee'] = smallOrderFee;
    cart['total'] = total;
    cart['lastUpdated'] = DateTime.now().toIso8601String();
  }

  // Initialize categories
  void _initializeCategories() {
    _database['categories'] = [
      {
        'id': '1',
        'name': 'Cleaning',
        'icon': Icons.cleaning_services.codePoint,
        'color': 0xFF2196F3, // Blue
        'imageUrl':
            'https://images.unsplash.com/photo-1581578731548-c64695cc6952',
        'description': 'Professional cleaning services for your home',
      },
      {
        'id': '2',
        'name': 'Plumbing',
        'icon': Icons.plumbing.codePoint,
        'color': 0xFFFF9800, // Orange
        'imageUrl':
            'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39',
        'description': 'Expert plumbing services for all your needs',
      },
      {
        'id': '3',
        'name': 'Electrical',
        'icon': Icons.electrical_services.codePoint,
        'color': 0xFFF44336, // Red
        'imageUrl':
            'https://images.unsplash.com/photo-1621905251189-08b45d6a269e',
        'description': 'Professional electrical services for your home',
      },
      {
        'id': '4',
        'name': 'Carpenter',
        'icon': Icons.handyman.codePoint,
        'color': 0xFF795548, // Brown
        'imageUrl':
            'https://images.unsplash.com/photo-1601564921647-b446262bbc6c',
        'description': 'Expert carpentry services for your home',
      },
      {
        'id': '5',
        'name': 'Appliances',
        'icon': Icons.kitchen.codePoint,
        'color': 0xFF9C27B0, // Purple
        'imageUrl':
            'https://images.unsplash.com/photo-1584269600464-37b1b58a9fe7',
        'description': 'Repair and maintenance for all your appliances',
      },
      {
        'id': '6',
        'name': 'AC Services',
        'icon': Icons.ac_unit.codePoint,
        'color': 0xFF00BCD4, // Cyan
        'imageUrl':
            'https://images.unsplash.com/photo-1621905252507-b35492cc74b4',
        'description': 'AC installation, repair, and maintenance services',
      },
      {
        'id': '7',
        'name': 'Pest Control',
        'icon': Icons.pest_control.codePoint,
        'color': 0xFF4CAF50, // Green
        'imageUrl':
            'https://images.unsplash.com/photo-1584269600464-37b1b58a9fe7',
        'description': 'Professional pest control services for your home',
      },
    ];

    _database['subcategories'] = [
      // Cleaning subcategories
      {
        'id': '1-1',
        'parentId': '1',
        'name': 'Home Cleaning',
        'icon': Icons.home.codePoint,
        'color': 0xFF2196F3, // Blue
        'imageUrl':
            'https://images.unsplash.com/photo-1581578731548-c64695cc6952',
      },
      {
        'id': '1-2',
        'parentId': '1',
        'name': 'Office Cleaning',
        'icon': Icons.business.codePoint,
        'color': 0xFF2196F3, // Blue
        'imageUrl':
            'https://images.unsplash.com/photo-1604328698692-f76ea9498e76',
      },
      {
        'id': '1-3',
        'parentId': '1',
        'name': 'Bathroom Cleaning',
        'icon': Icons.bathtub.codePoint,
        'color': 0xFF2196F3, // Blue
        'imageUrl':
            'https://images.unsplash.com/photo-1584622650111-993a426fbf0a',
      },

      // Plumbing subcategories
      {
        'id': '2-1',
        'parentId': '2',
        'name': 'Pipe Repair',
        'icon': Icons.plumbing.codePoint,
        'color': 0xFFFF9800, // Orange
        'imageUrl':
            'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39',
      },
      {
        'id': '2-2',
        'parentId': '2',
        'name': 'Tap Installation',
        'icon': Icons.water_drop.codePoint,
        'color': 0xFFFF9800, // Orange
        'imageUrl':
            'https://images.unsplash.com/photo-1585704032915-c3400ca199e7',
      },
      {
        'id': '2-3',
        'parentId': '2',
        'name': 'Toilet Repair',
        'icon': Icons.wc.codePoint,
        'color': 0xFFFF9800, // Orange
        'imageUrl':
            'https://images.unsplash.com/photo-1584622650111-993a426fbf0a',
      },

      // Electrical subcategories
      {
        'id': '3-1',
        'parentId': '3',
        'name': 'Switch Installation',
        'icon': Icons.toggle_on.codePoint,
        'color': 0xFFF44336, // Red
        'imageUrl': 'https://images.unsplash.com/photo-1558402529-d2638a7023e9',
      },
      {
        'id': '3-2',
        'parentId': '3',
        'name': 'Wiring',
        'icon': Icons.cable.codePoint,
        'color': 0xFFF44336, // Red
        'imageUrl':
            'https://images.unsplash.com/photo-1621905251189-08b45d6a269e',
      },
      {
        'id': '3-3',
        'parentId': '3',
        'name': 'Fan Installation',
        'icon': Icons.air.codePoint,
        'color': 0xFFF44336, // Red
        'imageUrl':
            'https://images.unsplash.com/photo-1614210492255-b4ae92f3e290',
      },
    ];
  }

  // Initialize products
  void _initializeProducts() {
    final products = <Map<String, dynamic>>[];

    // Add cleaning services
    products.addAll([
      {
        'id': '1-1-1',
        'categoryId': '1-1',
        'name': 'Basic Home Cleaning',
        'description':
            'Complete cleaning of your home including dusting, mopping, and bathroom cleaning.',
        'price': 499.0,
        'durationMinutes': 120,
        'imageUrl':
            'https://images.unsplash.com/photo-1581578731548-c64695cc6952',
        'rating': 4.5,
        'reviewCount': 120,
        'isPopular': true,
        'isFeatured': true,
      },
      {
        'id': '1-1-2',
        'categoryId': '1-1',
        'name': 'Deep Home Cleaning',
        'description':
            'Thorough cleaning of your entire home including hard-to-reach areas, appliances, and cabinets.',
        'price': 999.0,
        'durationMinutes': 240,
        'imageUrl':
            'https://images.unsplash.com/photo-1584622650111-993a426fbf0a',
        'rating': 4.8,
        'reviewCount': 85,
        'isPopular': true,
        'isFeatured': false,
      },
      {
        'id': '1-2-1',
        'categoryId': '1-2',
        'name': 'Office Cleaning (Small)',
        'description':
            'Professional cleaning for small offices up to 1000 sq ft.',
        'price': 1499.0,
        'durationMinutes': 180,
        'imageUrl':
            'https://images.unsplash.com/photo-1604328698692-f76ea9498e76',
        'rating': 4.3,
        'reviewCount': 42,
        'isPopular': false,
        'isFeatured': false,
      },
    ]);

    // Add plumbing services
    products.addAll([
      {
        'id': '2-1-1',
        'categoryId': '2-1',
        'name': 'Pipe Leak Repair',
        'description':
            'Fix leaking pipes and prevent water damage to your home.',
        'price': 399.0,
        'durationMinutes': 60,
        'imageUrl':
            'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39',
        'rating': 4.6,
        'reviewCount': 98,
        'isPopular': true,
        'isFeatured': true,
      },
      {
        'id': '2-2-1',
        'categoryId': '2-2',
        'name': 'Tap Installation',
        'description':
            'Professional installation of new taps in your kitchen or bathroom.',
        'price': 299.0,
        'durationMinutes': 45,
        'imageUrl':
            'https://images.unsplash.com/photo-1585704032915-c3400ca199e7',
        'rating': 4.4,
        'reviewCount': 56,
        'isPopular': false,
        'isFeatured': false,
      },
    ]);

    // Add electrical services
    products.addAll([
      {
        'id': '3-1-1',
        'categoryId': '3-1',
        'name': 'Switch Installation',
        'description':
            'Professional installation of electrical switches in your home.',
        'price': 199.0,
        'durationMinutes': 30,
        'imageUrl': 'https://images.unsplash.com/photo-1558402529-d2638a7023e9',
        'rating': 4.7,
        'reviewCount': 112,
        'isPopular': true,
        'isFeatured': false,
      },
      {
        'id': '3-2-1',
        'categoryId': '3-2',
        'name': 'Home Rewiring',
        'description':
            'Complete rewiring of your home with high-quality materials.',
        'price': 5999.0,
        'durationMinutes': 480,
        'imageUrl':
            'https://images.unsplash.com/photo-1621905251189-08b45d6a269e',
        'rating': 4.9,
        'reviewCount': 28,
        'isPopular': false,
        'isFeatured': true,
      },
    ]);

    _database['products'] = products;

    // Create featured and recent services lists
    _database['featuredServices'] =
        products.where((p) => p['isFeatured'] == true).toList();
    _database['recentServices'] = List<Map<String, dynamic>>.from(products)
      ..shuffle();
    _database['recentServices'] = _database['recentServices'].take(5).toList();
  }

  // Initialize users
  void _initializeUsers() {
    _database['users'] = [
      {
        'id': 'user_default',
        'mobile': '9876543210',
        'name': 'Demo User',
        'email': '<EMAIL>',
        'profilePic': 'https://randomuser.me/api/portraits/men/1.jpg',
        'createdAt':
            DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        'lastLogin': DateTime.now().toIso8601String(),
      }
    ];

    // Create support agent
    _supportAgent = {
      'id': 'agent_1',
      'name': 'Support Agent',
      'profilePic': 'https://randomuser.me/api/portraits/women/1.jpg',
      'isOnline': true,
    };
    _database['supportAgent'] = _supportAgent;
  }

  // Initialize banners
  void _initializeBanners() {
    _database['banners'] = [
      {
        'id': 'banner1',
        'title': 'Special Offer',
        'description': 'Get 20% off on all cleaning services',
        'imageUrl':
            'https://images.unsplash.com/photo-1581578731548-c64695cc6952',
        'actionUrl': '/categories/1',
        'validUntil':
            DateTime.now().add(const Duration(days: 30)).toIso8601String(),
      },
      {
        'id': 'banner2',
        'title': 'New Service',
        'description': 'Try our new AC maintenance service',
        'imageUrl':
            'https://images.unsplash.com/photo-1621905252507-b35492cc74b4',
        'actionUrl': '/categories/6',
        'validUntil':
            DateTime.now().add(const Duration(days: 60)).toIso8601String(),
      },
      {
        'id': 'banner3',
        'title': 'Monsoon Special',
        'description': 'Plumbing services at discounted rates',
        'imageUrl':
            'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39',
        'actionUrl': '/categories/2',
        'validUntil':
            DateTime.now().add(const Duration(days: 45)).toIso8601String(),
      },
    ];
  }

  // Simulate realtime data updates
  void _simulateDataUpdates() {
    // Simulate price changes for random products
    final products =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);

    if (products.isEmpty) return;

    final numProductsToUpdate = (products.length * 0.1).round();

    // Use the base class method to simulate price updates
    simulatePriceUpdates(products, numProductsToUpdate);

    // Update the database
    _database['products'] = products;

    // Notify listeners about the updates
    _productsStreamController.add(products);

    // Update featured and recent services
    _updateFeaturedServices();
    _updateRecentServices();

    logApiCall('simulateDataUpdates',
        params: {'numProductsUpdated': numProductsToUpdate});
  }

  // Update featured services
  void _updateFeaturedServices() {
    final products =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);
    final featuredServices =
        products.where((p) => p['isFeatured'] == true).toList();
    _database['featuredServices'] = featuredServices;
    _featuredServicesStreamController.add(featuredServices);
  }

  // Update recent services
  void _updateRecentServices() {
    final products =
        List<Map<String, dynamic>>.from(_database['products'] ?? []);
    final recentServices = List<Map<String, dynamic>>.from(products)..shuffle();
    _database['recentServices'] = recentServices.take(5).toList();
    _recentServicesStreamController.add(_database['recentServices']);
  }

  // Booking methods
  Future<Map<String, dynamic>> createBooking(
      Map<String, dynamic> bookingData) async {
    await simulateNetworkDelay();

    logApiCall('createBooking', params: {'bookingData': bookingData});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Generate a booking ID
    final bookingId = 'booking-${DateTime.now().millisecondsSinceEpoch}';

    // Debug logging
    print('MockAPI: Creating booking for user: $_currentUserId');
    print('MockAPI: Booking service: ${bookingData['service']}');
    print('MockAPI: Booking date: ${bookingData['date']}');

    // Create the booking
    final booking = {
      'id': bookingId,
      'userId': _currentUserId,
      'service': bookingData['service'],
      'category': bookingData['category'],
      'price': bookingData['price'],
      'date': bookingData['date'] is DateTime
          ? (bookingData['date'] as DateTime).toIso8601String()
          : bookingData['date'].toString(),
      'time': bookingData['time'],
      'address': bookingData['address'],
      'status': 'Pending',
      'paymentMethod': bookingData['paymentMethod'],
      'paymentStatus': bookingData['paymentMethod'] == 'Cash on Delivery'
          ? 'Pending'
          : 'Paid',
      'createdAt': DateTime.now().toIso8601String(),
      'icon': bookingData['icon'] ?? Icons.home_repair_service.codePoint,
      'color': bookingData['color'] ?? Colors.blue.toARGB32(),
    };

    // Add the booking to the database
    if (!_database.containsKey('bookings')) {
      _database['bookings'] = [];
    }

    if (!_database.containsKey('userBookings')) {
      _database['userBookings'] = {};
    }

    if (!_database['userBookings'].containsKey(_currentUserId)) {
      _database['userBookings'][_currentUserId!] = [];
    }

    final bookings = List<Map<String, dynamic>>.from(_database['bookings']);
    bookings.add(booking);
    _database['bookings'] = bookings;

    final userBookings = List<Map<String, dynamic>>.from(
        _database['userBookings'][_currentUserId!]);
    userBookings.add(booking);
    _database['userBookings'][_currentUserId!] = userBookings;

    // Debug logging after storing
    print('MockAPI: Booking stored successfully for user $_currentUserId');
    print('MockAPI: Total bookings for user: ${userBookings.length}');
    print('MockAPI: Latest booking ID: ${booking['id']}');

    // Notify listeners
    _bookingsStreamController.add(userBookings);

    // Clear the cart after successful booking
    clearCart();

    // Create a notification
    _createNotification(
        'Booking Confirmed',
        'Your booking for ${booking['service']} has been confirmed.',
        'booking',
        '/bookings');

    return createSuccessResponse(booking);
  }

  Future<Map<String, dynamic>> getBookings() async {
    await simulateNetworkDelay();

    logApiCall('getBookings');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Debug logging
    print('MockAPI: Getting bookings for user: $_currentUserId');
    print(
        'MockAPI: Database contains userBookings: ${_database.containsKey('userBookings')}');
    if (_database.containsKey('userBookings')) {
      print('MockAPI: User bookings keys: ${_database['userBookings'].keys}');
      print(
          'MockAPI: User has bookings: ${_database['userBookings'].containsKey(_currentUserId)}');
    }

    if (!_database.containsKey('userBookings') ||
        !_database['userBookings'].containsKey(_currentUserId)) {
      print(
          'MockAPI: No bookings found for user $_currentUserId, returning empty list');
      return createSuccessResponse([]);
    }

    final bookings = List<Map<String, dynamic>>.from(
        _database['userBookings'][_currentUserId!]);

    print(
        'MockAPI: Found ${bookings.length} bookings for user $_currentUserId');
    for (final booking in bookings) {
      print(
          'MockAPI: Booking: ${booking['id']} - ${booking['service']} - ${booking['status']}');
    }

    // Sort bookings by date (newest first)
    bookings.sort((a, b) {
      final aDate = DateTime.parse(a['createdAt']);
      final bDate = DateTime.parse(b['createdAt']);
      return bDate.compareTo(aDate);
    });

    return createSuccessResponse(bookings);
  }

  Future<Map<String, dynamic>> getBookingDetails(String bookingId) async {
    await simulateNetworkDelay();

    logApiCall('getBookingDetails', params: {'bookingId': bookingId});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userBookings') ||
        !_database['userBookings'].containsKey(_currentUserId)) {
      return createErrorResponse('Booking not found');
    }

    final bookings = List<Map<String, dynamic>>.from(
        _database['userBookings'][_currentUserId!]);

    final booking = bookings.firstWhere(
      (b) => b['id'] == bookingId,
      orElse: () => <String, dynamic>{},
    );

    if (booking.isEmpty) {
      return createErrorResponse('Booking not found');
    }

    return createSuccessResponse(booking);
  }

  Future<Map<String, dynamic>> cancelBooking(String bookingId) async {
    await simulateNetworkDelay();

    logApiCall('cancelBooking', params: {'bookingId': bookingId});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userBookings') ||
        !_database['userBookings'].containsKey(_currentUserId)) {
      return createErrorResponse('Booking not found');
    }

    final userBookings = List<Map<String, dynamic>>.from(
        _database['userBookings'][_currentUserId!]);

    final bookingIndex = userBookings.indexWhere((b) => b['id'] == bookingId);

    if (bookingIndex < 0) {
      return createErrorResponse('Booking not found');
    }

    final booking = userBookings[bookingIndex];

    // Check if the booking can be cancelled
    if (booking['status'] != 'Pending') {
      return createErrorResponse('Only pending bookings can be cancelled');
    }

    // Update the booking status
    booking['status'] = 'Cancelled';
    booking['paymentStatus'] = 'Refunded';

    // Update the booking in the database
    userBookings[bookingIndex] = booking;
    _database['userBookings'][_currentUserId!] = userBookings;

    // Update the global bookings
    final allBookings = List<Map<String, dynamic>>.from(_database['bookings']);
    final globalBookingIndex =
        allBookings.indexWhere((b) => b['id'] == bookingId);

    if (globalBookingIndex >= 0) {
      allBookings[globalBookingIndex] = booking;
      _database['bookings'] = allBookings;
    }

    // Notify listeners
    _bookingsStreamController.add(userBookings);

    // Create a notification
    _createNotification(
        'Booking Cancelled',
        'Your booking for ${booking['service']} has been cancelled.',
        'booking',
        '/bookings');

    return createSuccessResponse(booking);
  }

  Future<Map<String, dynamic>> rateBooking(
      String bookingId, double rating, String? review) async {
    await simulateNetworkDelay();

    logApiCall('rateBooking',
        params: {'bookingId': bookingId, 'rating': rating, 'review': review});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userBookings') ||
        !_database['userBookings'].containsKey(_currentUserId)) {
      return createErrorResponse('Booking not found');
    }

    final userBookings = List<Map<String, dynamic>>.from(
        _database['userBookings'][_currentUserId!]);

    final bookingIndex = userBookings.indexWhere((b) => b['id'] == bookingId);

    if (bookingIndex < 0) {
      return createErrorResponse('Booking not found');
    }

    final booking = userBookings[bookingIndex];

    // Check if the booking can be rated
    if (booking['status'] != 'Completed') {
      return createErrorResponse('Only completed bookings can be rated');
    }

    // Update the booking with rating and review
    booking['rating'] = rating;
    booking['review'] = review;

    // Update the booking in the database
    userBookings[bookingIndex] = booking;
    _database['userBookings'][_currentUserId!] = userBookings;

    // Update the global bookings
    final allBookings = List<Map<String, dynamic>>.from(_database['bookings']);
    final globalBookingIndex =
        allBookings.indexWhere((b) => b['id'] == bookingId);

    if (globalBookingIndex >= 0) {
      allBookings[globalBookingIndex] = booking;
      _database['bookings'] = allBookings;
    }

    // Notify listeners
    _bookingsStreamController.add(userBookings);

    return createSuccessResponse(booking);
  }

  // Profile methods
  Future<Map<String, dynamic>> getProfile() async {
    await simulateNetworkDelay();

    logApiCall('getProfile');

    if (_currentUserId == null) {
      print('MockAPI: getProfile - User not authenticated');
      return createErrorResponse('User not authenticated');
    }

    print('MockAPI: getProfile - Current user: $_currentUserId');
    final users = List<Map<String, dynamic>>.from(_database['users']);
    print('MockAPI: getProfile - Total users in database: ${users.length}');

    final user = users.firstWhere(
      (u) => u['id'] == _currentUserId,
      orElse: () => <String, dynamic>{},
    );

    if (user.isEmpty) {
      print('MockAPI: getProfile - User not found in database');
      return createErrorResponse('User not found');
    }

    print(
        'MockAPI: getProfile - Found user: ${user['name']}, isProfileComplete: ${user['isProfileComplete']}');
    return createSuccessResponse(user);
  }

  Future<Map<String, dynamic>> updateProfile(
      Map<String, dynamic> profileData) async {
    await simulateNetworkDelay();

    logApiCall('updateProfile', params: {'profileData': profileData});

    if (_currentUserId == null) {
      print('MockAPI: updateProfile - User not authenticated');
      return createErrorResponse('User not authenticated');
    }

    print('MockAPI: updateProfile - Current user: $_currentUserId');
    print('MockAPI: updateProfile - Profile data: $profileData');

    final users = List<Map<String, dynamic>>.from(_database['users']);

    final userIndex = users.indexWhere((u) => u['id'] == _currentUserId);

    if (userIndex < 0) {
      return createErrorResponse('User not found');
    }

    final user = users[userIndex];

    // Update user data
    if (profileData.containsKey('name')) {
      user['name'] = profileData['name'];
    }

    if (profileData.containsKey('email')) {
      user['email'] = profileData['email'];
    }

    if (profileData.containsKey('mobile')) {
      user['mobile'] = profileData['mobile'];
    }

    if (profileData.containsKey('profilePic')) {
      user['profilePic'] = profileData['profilePic'];
    }

    if (profileData.containsKey('isProfileComplete')) {
      user['isProfileComplete'] = profileData['isProfileComplete'];
    }

    // Update the user in the database
    users[userIndex] = user;
    _database['users'] = users;

    print(
        'MockAPI: updateProfile - User updated successfully: ${user['name']}');
    print(
        'MockAPI: updateProfile - Profile complete: ${user['isProfileComplete']}');

    // Notify listeners
    _profileStreamController.add(user);

    final response = createSuccessResponse(user);
    print('MockAPI: updateProfile - Response: $response');
    return response;
  }

  // Initialize bookings
  void _initializeBookings() {
    _database['bookings'] = [];
    _database['userBookings'] = {};
  }

  // Initialize user bookings
  void _initializeUserBookings() {
    if (_currentUserId == null) return;

    final random = Random();
    final allProducts = List<Map<String, dynamic>>.from(_database['products']);

    if (allProducts.isEmpty) return;

    // Create some random bookings for the user
    final bookings = <Map<String, dynamic>>[];

    // Past bookings (completed or cancelled)
    for (var i = 0; i < 3 + random.nextInt(3); i++) {
      final product = allProducts[random.nextInt(allProducts.length)];
      final isCompleted = random.nextBool();

      final booking = {
        'id': 'booking-${DateTime.now().millisecondsSinceEpoch}-$i',
        'userId': _currentUserId,
        'service': product['name'],
        'category': _getCategoryNameById(product['categoryId']),
        'price': product['price'],
        'date': DateTime.now()
            .subtract(Duration(days: random.nextInt(30) + 1))
            .toIso8601String(),
        'time': _getRandomTimeSlot(),
        'address': _getRandomAddress(),
        'status': isCompleted ? 'Completed' : 'Cancelled',
        'paymentMethod': _getRandomPaymentMethod(),
        'paymentStatus': isCompleted ? 'Paid' : 'Refunded',
        'technician': isCompleted ? 'John Doe' : null,
        'technicianPhone': isCompleted ? '+919876543210' : null,
        'rating': isCompleted ? (3 + random.nextInt(3)).toDouble() : null,
        'review': isCompleted && random.nextBool() ? 'Great service!' : null,
        'createdAt': DateTime.now()
            .subtract(Duration(days: random.nextInt(30) + 2))
            .toIso8601String(),
        'icon': Icons.home_repair_service.codePoint,
        'color': Colors.blue.toARGB32(),
      };

      bookings.add(booking);
    }

    // Upcoming bookings (pending or confirmed)
    for (var i = 0; i < 2 + random.nextInt(2); i++) {
      final product = allProducts[random.nextInt(allProducts.length)];
      final isConfirmed = random.nextBool();

      final booking = {
        'id': 'booking-${DateTime.now().millisecondsSinceEpoch}-${i + 10}',
        'userId': _currentUserId,
        'service': product['name'],
        'category': _getCategoryNameById(product['categoryId']),
        'price': product['price'],
        'date': DateTime.now()
            .add(Duration(days: random.nextInt(7) + 1))
            .toIso8601String(),
        'time': _getRandomTimeSlot(),
        'address': _getRandomAddress(),
        'status': isConfirmed ? 'Confirmed' : 'Pending',
        'paymentMethod': _getRandomPaymentMethod(),
        'paymentStatus': isConfirmed ? 'Paid' : 'Pending',
        'technician': isConfirmed ? 'Jane Smith' : null,
        'technicianPhone': isConfirmed ? '+919876543211' : null,
        'createdAt': DateTime.now()
            .subtract(Duration(hours: random.nextInt(24)))
            .toIso8601String(),
        'icon': Icons.home_repair_service.codePoint,
        'color': Colors.green.toARGB32(),
      };

      bookings.add(booking);
    }

    // Store bookings
    _database['userBookings'][_currentUserId!] = bookings;

    // Add to global bookings
    final allBookings = List<Map<String, dynamic>>.from(_database['bookings']);
    allBookings.addAll(bookings);
    _database['bookings'] = allBookings;

    // Notify listeners
    _bookingsStreamController.add(bookings);
  }

  // Initialize cart
  void _initializeCart() {
    _database['carts'] = {};
  }

  // Initialize user cart
  void _initializeUserCart() {
    if (_currentUserId == null) return;

    // Create an empty cart for the user
    final cart = {
      'userId': _currentUserId,
      'items': <Map<String, dynamic>>[],
      'subtotal': 0,
      'tax': 0,
      'discount': 0,
      'total': 0,
      'couponCode': null,
      'couponDiscount': 0,
      'smallOrderFee': 0,
      'lastUpdated': DateTime.now().toIso8601String(),
    };

    // Store cart
    _database['carts'][_currentUserId!] = cart;

    // Notify listeners
    _cartStreamController.add(cart);
  }

  // Initialize chat messages
  void _initializeChatMessages() {
    _database['chatMessages'] = [];
    _database['userChatMessages'] = {};
  }

  // Initialize user chat messages
  void _initializeUserChatMessages() {
    if (_currentUserId == null) return;

    final random = Random();
    final messages = <Map<String, dynamic>>[];

    // Add welcome message from support agent
    messages.add({
      'id': 'msg-${DateTime.now().millisecondsSinceEpoch}-1',
      'senderId': _supportAgent['id'],
      'senderName': 'Support Agent',
      'receiverId': _currentUserId,
      'message':
          'Hello! Welcome to DodoBooker support. How can I assist you today?\n\nI can help you with:\n• Service bookings and scheduling\n• Booking status and updates\n• Payment and billing questions\n• General support and inquiries\n\nPlease let me know what you need help with.',
      'timestamp': DateTime.now()
          .subtract(Duration(days: random.nextInt(7)))
          .toIso8601String(),
      'isRead': true,
      'type': 'text',
    });

    // Add some random messages
    if (random.nextBool()) {
      // User asked a question
      messages.add({
        'id': 'msg-${DateTime.now().millisecondsSinceEpoch}-2',
        'senderId': _currentUserId,
        'receiverId': _supportAgent['id'],
        'message': 'Hi, I need help with my booking.',
        'timestamp': DateTime.now()
            .subtract(
                Duration(days: random.nextInt(7), hours: random.nextInt(2)))
            .toIso8601String(),
        'isRead': true,
        'type': 'text',
      });

      // Support agent replied
      messages.add({
        'id': 'msg-${DateTime.now().millisecondsSinceEpoch}-3',
        'senderId': _supportAgent['id'],
        'senderName': 'Support Agent',
        'receiverId': _currentUserId,
        'message':
            'Sure, I\'d be happy to help. Could you please provide your booking ID?',
        'timestamp': DateTime.now()
            .subtract(
                Duration(days: random.nextInt(7), minutes: random.nextInt(30)))
            .toIso8601String(),
        'isRead': true,
        'type': 'text',
      });
    }

    // Store messages
    _database['userChatMessages'][_currentUserId!] = messages;

    // Add to global messages
    final allMessages =
        List<Map<String, dynamic>>.from(_database['chatMessages']);
    allMessages.addAll(messages);
    _database['chatMessages'] = allMessages;

    // Notify listeners
    _chatMessagesStreamController.add(messages);
  }

  // Chat methods
  Future<Map<String, dynamic>> getChatMessages() async {
    await simulateNetworkDelay();

    logApiCall('getChatMessages');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userChatMessages') ||
        !_database['userChatMessages'].containsKey(_currentUserId)) {
      return createSuccessResponse([]);
    }

    final messages = List<Map<String, dynamic>>.from(
        _database['userChatMessages'][_currentUserId!]);

    // Sort messages by timestamp (oldest first)
    messages.sort((a, b) {
      final aDate = DateTime.parse(a['timestamp']);
      final bDate = DateTime.parse(b['timestamp']);
      return aDate.compareTo(bDate);
    });

    return createSuccessResponse(messages);
  }

  Future<Map<String, dynamic>> sendChatMessage(String message) async {
    await simulateNetworkDelay();

    logApiCall('sendChatMessage', params: {'message': message});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Create the message
    final chatMessage = {
      'id': 'msg-${DateTime.now().millisecondsSinceEpoch}',
      'senderId': _currentUserId,
      'receiverId': _supportAgent['id'],
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
      'isRead': false,
      'type': 'text',
    };

    // Add the message to the database
    if (!_database.containsKey('chatMessages')) {
      _database['chatMessages'] = [];
    }

    if (!_database.containsKey('userChatMessages')) {
      _database['userChatMessages'] = {};
    }

    if (!_database['userChatMessages'].containsKey(_currentUserId)) {
      _database['userChatMessages'][_currentUserId!] = [];
    }

    final chatMessages =
        List<Map<String, dynamic>>.from(_database['chatMessages']);
    chatMessages.add(chatMessage);
    _database['chatMessages'] = chatMessages;

    final userChatMessages = List<Map<String, dynamic>>.from(
        _database['userChatMessages'][_currentUserId!]);
    userChatMessages.add(chatMessage);
    _database['userChatMessages'][_currentUserId!] = userChatMessages;

    // Notify listeners
    _chatMessagesStreamController.add(userChatMessages);

    // Simulate basic support agent response after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (_currentUserId != null) {
        final response = {
          'id': 'msg-${DateTime.now().millisecondsSinceEpoch}',
          'senderId': _supportAgent['id'],
          'senderName': 'Support Agent',
          'receiverId': _currentUserId,
          'message': _generateBasicResponse(message),
          'timestamp': DateTime.now().toIso8601String(),
          'isRead': false,
          'type': 'text',
        };

        final updatedUserChatMessages = List<Map<String, dynamic>>.from(
            _database['userChatMessages'][_currentUserId!]);
        updatedUserChatMessages.add(response);
        _database['userChatMessages'][_currentUserId!] =
            updatedUserChatMessages;

        final updatedChatMessages =
            List<Map<String, dynamic>>.from(_database['chatMessages']);
        updatedChatMessages.add(response);
        _database['chatMessages'] = updatedChatMessages;

        // Notify listeners
        _chatMessagesStreamController.add(updatedUserChatMessages);

        // Create a notification
        _createNotification('New message from Support',
            'You have a new message from our support team.', 'chat', '/chat');
      }
    });

    return createSuccessResponse(chatMessage);
  }

  // Notification methods
  Future<Map<String, dynamic>> getNotifications() async {
    await simulateNetworkDelay();

    logApiCall('getNotifications');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userNotifications') ||
        !_database['userNotifications'].containsKey(_currentUserId)) {
      return createSuccessResponse([]);
    }

    final notifications = List<Map<String, dynamic>>.from(
        _database['userNotifications'][_currentUserId!]);

    // Sort notifications by timestamp (newest first)
    notifications.sort((a, b) {
      final aDate = DateTime.parse(a['timestamp']);
      final bDate = DateTime.parse(b['timestamp']);
      return bDate.compareTo(aDate);
    });

    return createSuccessResponse(notifications);
  }

  Future<Map<String, dynamic>> markNotificationAsRead(
      String notificationId) async {
    await simulateNetworkDelay();

    logApiCall('markNotificationAsRead',
        params: {'notificationId': notificationId});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userNotifications') ||
        !_database['userNotifications'].containsKey(_currentUserId)) {
      return createErrorResponse('Notification not found');
    }

    final userNotifications = List<Map<String, dynamic>>.from(
        _database['userNotifications'][_currentUserId!]);

    final notificationIndex =
        userNotifications.indexWhere((n) => n['id'] == notificationId);

    if (notificationIndex < 0) {
      return createErrorResponse('Notification not found');
    }

    // Mark the notification as read
    userNotifications[notificationIndex]['isRead'] = true;

    // Update the database
    _database['userNotifications'][_currentUserId!] = userNotifications;

    // Update the global notifications
    final allNotifications =
        List<Map<String, dynamic>>.from(_database['notifications']);
    final globalNotificationIndex =
        allNotifications.indexWhere((n) => n['id'] == notificationId);

    if (globalNotificationIndex >= 0) {
      allNotifications[globalNotificationIndex]['isRead'] = true;
      _database['notifications'] = allNotifications;
    }

    // Notify listeners
    _notificationsStreamController.add(userNotifications);

    return createSuccessResponse(userNotifications[notificationIndex]);
  }

  Future<Map<String, dynamic>> clearAllNotifications() async {
    await simulateNetworkDelay();

    logApiCall('clearAllNotifications');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userNotifications') ||
        !_database['userNotifications'].containsKey(_currentUserId)) {
      return createSuccessResponse([]);
    }

    // Clear all notifications
    _database['userNotifications'][_currentUserId!] = [];

    // Update the global notifications
    final allNotifications =
        List<Map<String, dynamic>>.from(_database['notifications']);
    final updatedNotifications =
        allNotifications.where((n) => n['userId'] != _currentUserId).toList();
    _database['notifications'] = updatedNotifications;

    // Notify listeners
    _notificationsStreamController.add([]);

    return createSuccessResponse({'success': true});
  }

  // Initialize notifications
  void _initializeNotifications() {
    _database['notifications'] = [];
    _database['userNotifications'] = {};
  }

  // Initialize user notifications
  void _initializeUserNotifications() {
    if (_currentUserId == null) return;

    final random = Random();
    final notifications = <Map<String, dynamic>>[];

    // Welcome notification
    notifications.add({
      'id': 'notif-${DateTime.now().millisecondsSinceEpoch}-1',
      'userId': _currentUserId,
      'title': 'Welcome to DodoBooker',
      'message':
          'Thank you for choosing DodoBooker for your home services needs.',
      'timestamp': DateTime.now()
          .subtract(Duration(days: random.nextInt(7)))
          .toIso8601String(),
      'isRead': true,
      'type': 'welcome',
      'actionUrl': null,
    });

    // Offer notification
    notifications.add({
      'id': 'notif-${DateTime.now().millisecondsSinceEpoch}-2',
      'userId': _currentUserId,
      'title': 'Special Offer',
      'message': 'Get 20% off on your first booking. Use code WELCOME20.',
      'timestamp': DateTime.now()
          .subtract(Duration(days: random.nextInt(5)))
          .toIso8601String(),
      'isRead': random.nextBool(),
      'type': 'offer',
      'actionUrl': '/offers',
    });

    // Store notifications
    _database['userNotifications'][_currentUserId!] = notifications;

    // Add to global notifications
    final allNotifications =
        List<Map<String, dynamic>>.from(_database['notifications']);
    allNotifications.addAll(notifications);
    _database['notifications'] = allNotifications;

    // Notify listeners
    _notificationsStreamController.add(notifications);
  }

  // Address methods
  Future<Map<String, dynamic>> getAddresses() async {
    await simulateNetworkDelay();

    logApiCall('getAddresses');

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userAddresses') ||
        !_database['userAddresses'].containsKey(_currentUserId)) {
      return createSuccessResponse([]);
    }

    final addresses = List<Map<String, dynamic>>.from(
        _database['userAddresses'][_currentUserId!]);

    return createSuccessResponse(addresses);
  }

  Future<Map<String, dynamic>> addAddress(
      Map<String, dynamic> addressData) async {
    await simulateNetworkDelay();

    logApiCall('addAddress', params: {'addressData': addressData});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    // Generate an address ID
    final addressId = 'addr-${DateTime.now().millisecondsSinceEpoch}';

    // Create the address
    final address = {
      'id': addressId,
      'userId': _currentUserId,
      'type': addressData['type'] ?? 'Home',
      'name': addressData['name'] ?? addressData['type'] ?? 'Home',
      'address': addressData['address'],
      'landmark': addressData['landmark'],
      'city': addressData['city'],
      'state': addressData['state'],
      'pincode': addressData['pincode'],
      'latitude': addressData['latitude'],
      'longitude': addressData['longitude'],
      'isDefault': addressData['isDefault'] ?? false,
    };

    // Add the address to the database
    if (!_database.containsKey('addresses')) {
      _database['addresses'] = [];
    }

    if (!_database.containsKey('userAddresses')) {
      _database['userAddresses'] = {};
    }

    if (!_database['userAddresses'].containsKey(_currentUserId)) {
      _database['userAddresses'][_currentUserId!] = [];
    }

    final addresses = List<Map<String, dynamic>>.from(_database['addresses']);
    addresses.add(address);
    _database['addresses'] = addresses;

    final userAddresses = List<Map<String, dynamic>>.from(
        _database['userAddresses'][_currentUserId!]);

    // If this is the first address or it's marked as default, update other addresses
    if (userAddresses.isEmpty || address['isDefault'] == true) {
      for (final addr in userAddresses) {
        addr['isDefault'] = false;
      }
      address['isDefault'] = true;
    }

    userAddresses.add(address);
    _database['userAddresses'][_currentUserId!] = userAddresses;

    // Notify listeners
    _addressesStreamController.add(userAddresses);

    return createSuccessResponse(address);
  }

  Future<Map<String, dynamic>> updateAddress(
      String addressId, Map<String, dynamic> addressData) async {
    await simulateNetworkDelay();

    logApiCall('updateAddress',
        params: {'addressId': addressId, 'addressData': addressData});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userAddresses') ||
        !_database['userAddresses'].containsKey(_currentUserId)) {
      return createErrorResponse('Address not found');
    }

    final userAddresses = List<Map<String, dynamic>>.from(
        _database['userAddresses'][_currentUserId!]);

    final addressIndex = userAddresses.indexWhere((a) => a['id'] == addressId);

    if (addressIndex < 0) {
      return createErrorResponse('Address not found');
    }

    final address = userAddresses[addressIndex];

    // Update address data
    if (addressData.containsKey('type')) {
      address['type'] = addressData['type'];
    }

    if (addressData.containsKey('name')) {
      address['name'] = addressData['name'];
    }

    if (addressData.containsKey('address')) {
      address['address'] = addressData['address'];
    }

    if (addressData.containsKey('landmark')) {
      address['landmark'] = addressData['landmark'];
    }

    if (addressData.containsKey('city')) {
      address['city'] = addressData['city'];
    }

    if (addressData.containsKey('state')) {
      address['state'] = addressData['state'];
    }

    if (addressData.containsKey('pincode')) {
      address['pincode'] = addressData['pincode'];
    }

    if (addressData.containsKey('latitude')) {
      address['latitude'] = addressData['latitude'];
    }

    if (addressData.containsKey('longitude')) {
      address['longitude'] = addressData['longitude'];
    }

    if (addressData.containsKey('isDefault') &&
        addressData['isDefault'] == true) {
      // Update other addresses to not be default
      for (final addr in userAddresses) {
        addr['isDefault'] = false;
      }
      address['isDefault'] = true;
    }

    // Update the address in the database
    userAddresses[addressIndex] = address;
    _database['userAddresses'][_currentUserId!] = userAddresses;

    // Update the global addresses
    final allAddresses =
        List<Map<String, dynamic>>.from(_database['addresses']);
    final globalAddressIndex =
        allAddresses.indexWhere((a) => a['id'] == addressId);

    if (globalAddressIndex >= 0) {
      allAddresses[globalAddressIndex] = address;
      _database['addresses'] = allAddresses;
    }

    // Notify listeners
    _addressesStreamController.add(userAddresses);

    return createSuccessResponse(address);
  }

  Future<Map<String, dynamic>> deleteAddress(String addressId) async {
    await simulateNetworkDelay();

    logApiCall('deleteAddress', params: {'addressId': addressId});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userAddresses') ||
        !_database['userAddresses'].containsKey(_currentUserId)) {
      return createErrorResponse('Address not found');
    }

    final userAddresses = List<Map<String, dynamic>>.from(
        _database['userAddresses'][_currentUserId!]);

    final addressIndex = userAddresses.indexWhere((a) => a['id'] == addressId);

    if (addressIndex < 0) {
      return createErrorResponse('Address not found');
    }

    final address = userAddresses[addressIndex];
    final wasDefault = address['isDefault'] == true;

    // Remove the address
    userAddresses.removeAt(addressIndex);

    // If the deleted address was the default, set a new default
    if (wasDefault && userAddresses.isNotEmpty) {
      userAddresses.first['isDefault'] = true;
    }

    // Update the database
    _database['userAddresses'][_currentUserId!] = userAddresses;

    // Update the global addresses
    final allAddresses =
        List<Map<String, dynamic>>.from(_database['addresses']);
    final globalAddressIndex =
        allAddresses.indexWhere((a) => a['id'] == addressId);

    if (globalAddressIndex >= 0) {
      allAddresses.removeAt(globalAddressIndex);
      _database['addresses'] = allAddresses;
    }

    // Notify listeners
    _addressesStreamController.add(userAddresses);

    return createSuccessResponse({'success': true});
  }

  Future<Map<String, dynamic>> setDefaultAddress(String addressId) async {
    await simulateNetworkDelay();

    logApiCall('setDefaultAddress', params: {'addressId': addressId});

    if (_currentUserId == null) {
      return createErrorResponse('User not authenticated');
    }

    if (!_database.containsKey('userAddresses') ||
        !_database['userAddresses'].containsKey(_currentUserId)) {
      return createErrorResponse('Address not found');
    }

    final userAddresses = List<Map<String, dynamic>>.from(
        _database['userAddresses'][_currentUserId!]);

    final addressIndex = userAddresses.indexWhere((a) => a['id'] == addressId);

    if (addressIndex < 0) {
      return createErrorResponse('Address not found');
    }

    // Update all addresses to not be default
    for (final addr in userAddresses) {
      addr['isDefault'] = false;
    }

    // Set the selected address as default
    userAddresses[addressIndex]['isDefault'] = true;

    // Update the database
    _database['userAddresses'][_currentUserId!] = userAddresses;

    // Update the global addresses
    final allAddresses =
        List<Map<String, dynamic>>.from(_database['addresses']);

    for (final addr in allAddresses) {
      if (addr['userId'] == _currentUserId) {
        addr['isDefault'] = addr['id'] == addressId;
      }
    }

    _database['addresses'] = allAddresses;

    // Notify listeners
    _addressesStreamController.add(userAddresses);

    return createSuccessResponse(userAddresses[addressIndex]);
  }

  // Initialize addresses
  void _initializeAddresses() {
    _database['addresses'] = [];
    _database['userAddresses'] = {};
  }

  // Initialize user addresses
  void _initializeUserAddresses() {
    if (_currentUserId == null) return;

    final random = Random();
    final addresses = <Map<String, dynamic>>[];

    // Home address
    addresses.add({
      'id': 'addr-${DateTime.now().millisecondsSinceEpoch}-1',
      'userId': _currentUserId,
      'type': 'Home',
      'name': 'Home',
      'address': '123 Main Street, Apartment 4B',
      'landmark': 'Near City Park',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400001',
      'latitude': 19.0760 + (random.nextDouble() * 0.01),
      'longitude': 72.8777 + (random.nextDouble() * 0.01),
      'isDefault': true,
    });

    // Work address
    if (random.nextBool()) {
      addresses.add({
        'id': 'addr-${DateTime.now().millisecondsSinceEpoch}-2',
        'userId': _currentUserId,
        'type': 'Work',
        'name': 'Office',
        'address': '456 Business Park, Tower B, Floor 5',
        'landmark': 'Opposite Metro Station',
        'city': 'Mumbai',
        'state': 'Maharashtra',
        'pincode': '400051',
        'latitude': 19.1136 + (random.nextDouble() * 0.01),
        'longitude': 72.9023 + (random.nextDouble() * 0.01),
        'isDefault': false,
      });
    }

    // Store addresses
    _database['userAddresses'][_currentUserId!] = addresses;

    // Add to global addresses
    final allAddresses =
        List<Map<String, dynamic>>.from(_database['addresses']);
    allAddresses.addAll(addresses);
    _database['addresses'] = allAddresses;

    // Notify listeners
    _addressesStreamController.add(addresses);
  }

  // Initialize coupons
  void _initializeCoupons() {
    final coupons = <Map<String, dynamic>>[
      {
        'id': 'coupon-1',
        'code': 'WELCOME20',
        'description': '20% off on your first booking',
        'discountType': 'percentage',
        'discountValue': 20,
        'maxDiscount': 500,
        'minOrderValue': 0,
        'validFrom':
            DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
        'validUntil':
            DateTime.now().add(const Duration(days: 60)).toIso8601String(),
        'isActive': true,
        'isFirstOrderOnly': true,
        'usageLimit': 1,
      },
      {
        'id': 'coupon-2',
        'code': 'CLEAN10',
        'description': '10% off on cleaning services',
        'discountType': 'percentage',
        'discountValue': 10,
        'maxDiscount': 300,
        'minOrderValue': 500,
        'validFrom':
            DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
        'validUntil':
            DateTime.now().add(const Duration(days: 15)).toIso8601String(),
        'isActive': true,
        'isFirstOrderOnly': false,
        'usageLimit': 3,
        'applicableCategories': ['1'],
      },
      {
        'id': 'coupon-3',
        'code': 'FLAT100',
        'description': 'Flat ₹100 off on bookings above ₹1000',
        'discountType': 'fixed',
        'discountValue': 100,
        'minOrderValue': 1000,
        'validFrom':
            DateTime.now().subtract(const Duration(days: 7)).toIso8601String(),
        'validUntil':
            DateTime.now().add(const Duration(days: 23)).toIso8601String(),
        'isActive': true,
        'isFirstOrderOnly': false,
        'usageLimit': 2,
      },
    ];

    _database['coupons'] = coupons;
    _couponsStreamController.add(coupons);
  }

  // Simulate agent responses
  void _simulateAgentResponses() {
    if (_currentUserId == null) return;

    final userMessages = List<Map<String, dynamic>>.from(
        _database['userChatMessages'][_currentUserId!] ?? []);

    if (userMessages.isEmpty) return;

    // Sort messages by timestamp
    userMessages.sort((a, b) => DateTime.parse(a['timestamp'])
        .compareTo(DateTime.parse(b['timestamp'])));

    // Get the last message
    final lastMessage = userMessages.last;

    // If the last message is from the user and doesn't have a response yet
    if (lastMessage['senderId'] == _currentUserId &&
        !userMessages.any((msg) =>
            msg['senderId'] == _supportAgent['id'] &&
            DateTime.parse(msg['timestamp'])
                .isAfter(DateTime.parse(lastMessage['timestamp'])))) {
      // Generate a response
      final response = {
        'id': 'msg-${DateTime.now().millisecondsSinceEpoch}',
        'senderId': _supportAgent['id'],
        'receiverId': _currentUserId,
        'message': _generateAgentResponse(lastMessage['message']),
        'timestamp': DateTime.now().toIso8601String(),
        'isRead': false,
        'type': 'text',
      };

      // Add to user messages
      userMessages.add(response);
      _database['userChatMessages'][_currentUserId!] = userMessages;

      // Add to global messages
      final allMessages =
          List<Map<String, dynamic>>.from(_database['chatMessages']);
      allMessages.add(response);
      _database['chatMessages'] = allMessages;

      // Notify listeners
      _chatMessagesStreamController.add(userMessages);

      // Create a notification
      _createNotification('New message from Support',
          'You have a new message from our support team.', 'chat', '/chat');
    }
  }

  // Generate a basic response from support agent
  String _generateBasicResponse(String userMessage) {
    final lowerMessage = userMessage.toLowerCase();

    // Basic greeting responses
    if (lowerMessage.contains('hello') ||
        lowerMessage.contains('hi') ||
        lowerMessage.contains('hey')) {
      return 'Hello! Thank you for contacting DodoBooker support. How can I assist you today?';
    }

    // Enhanced booking responses with specific service detection
    if (lowerMessage.contains('book') ||
        lowerMessage.contains('appointment') ||
        lowerMessage.contains('schedule')) {
      // Check for specific services
      if (lowerMessage.contains('cleaning') || lowerMessage.contains('clean')) {
        return 'Great! I can help you book a cleaning service. To get started, I need to know:\n\n'
            '🏠 What type of cleaning do you need?\n'
            '• Basic bathroom cleaning\n'
            '• Deep bathroom cleaning\n'
            '• Full house cleaning\n'
            '• Kitchen cleaning\n'
            '• Carpet cleaning\n\n'
            'Please let me know which one you prefer.';
      }

      if (lowerMessage.contains('plumbing') ||
          lowerMessage.contains('plumber')) {
        return 'I can help you book a plumbing service. What type of plumbing work do you need?\n\n'
            '🔧 Available services:\n'
            '• Pipe repair/replacement\n'
            '• Leak fixing\n'
            '• Drain cleaning\n'
            '• Toilet repair\n'
            '• Faucet installation\n\n'
            'Please specify your requirement.';
      }

      if (lowerMessage.contains('electrical') ||
          lowerMessage.contains('electrician')) {
        return 'I can help you book an electrical service. What electrical work do you need?\n\n'
            '⚡ Available services:\n'
            '• Wiring and rewiring\n'
            '• Switch/outlet installation\n'
            '• Fan installation\n'
            '• Light fixture setup\n'
            '• Electrical troubleshooting\n\n'
            'Please let me know your specific need.';
      }

      return 'I can help you with booking services. Please let me know which service you\'re interested in, and I\'ll guide you through the process.';
    }

    // Handle specific service type responses (follow-up questions)
    if (lowerMessage.contains('basic bathroom') ||
        lowerMessage.contains('basic cleaning')) {
      return 'Perfect! You\'ve selected Basic Bathroom Cleaning. ✅\n\n'
          '🔢 How many bathrooms would you like cleaned?\n'
          'Please specify the quantity:\n'
          '• 1 bathroom\n'
          '• 2 bathrooms\n'
          '• 3 bathrooms\n'
          '• More than 3 (please specify)\n\n'
          'Type the number or select from the options above.';
    }

    if (lowerMessage.contains('deep bathroom') ||
        lowerMessage.contains('deep cleaning')) {
      return 'Excellent choice! Deep Bathroom Cleaning includes thorough sanitization. ✅\n\n'
          '🔢 How many bathrooms would you like deep cleaned?\n'
          'Please specify the quantity:\n'
          '• 1 bathroom\n'
          '• 2 bathrooms\n'
          '• 3 bathrooms\n'
          '• More than 3 (please specify)\n\n'
          'Type the number or select from the options above.';
    }

    // Handle quantity responses
    if (_containsQuantityPattern(lowerMessage)) {
      final quantity = _extractQuantity(lowerMessage);
      return 'Great! I\'ve noted $quantity bathroom(s) for cleaning. 📝\n\n'
          '🎉 SPECIAL OFFERS RUNNING:\n'
          '• 🏷️ **Festival Special**: 15% OFF on all cleaning services\n'
          '• 🎁 **Multi-Room Discount**: Book 2+ rooms, get 10% extra off\n'
          '• 💰 **First Time Customer**: Additional 20% OFF\n'
          '• 🔄 **Weekly Package**: Book weekly cleaning, save 25%\n\n'
          '✨ These offers can be applied during checkout!\n\n'
          '📍 Now, I need your address details:\n'
          'Please provide:\n'
          '• Full address\n'
          '• Landmark (if any)\n'
          '• Preferred contact number\n\n'
          'You can type your address or say "use saved address" if you have one.';
    }

    // Handle address confirmation and ask for schedule
    if (lowerMessage.contains('address') ||
        lowerMessage.contains('location') ||
        lowerMessage.contains('use saved address') ||
        _containsAddressPattern(lowerMessage)) {
      return 'Thank you for providing the address! ✅\n\n'
          '📅 Now, let\'s schedule your service:\n\n'
          'When would you like the service?\n'
          '• Today (if available)\n'
          '• Tomorrow\n'
          '• This weekend\n'
          '• Next week\n\n'
          'Please specify your preferred date and time.';
    }

    // Handle scheduling responses
    if (lowerMessage.contains('today') ||
        lowerMessage.contains('tomorrow') ||
        lowerMessage.contains('weekend') ||
        lowerMessage.contains('next week') ||
        _containsTimePattern(lowerMessage)) {
      return 'Great! I\'ve noted your preferred schedule. 📅\n\n'
          '💰 Service Summary:\n'
          '• Service: Bathroom Cleaning\n'
          '• Estimated Cost: ₹299 - ₹599 per bathroom\n'
          '• Duration: 1-2 hours per bathroom\n'
          '• Total Estimated Cost: ₹598 - ₹1198 (for 2 bathrooms)\n\n'
          '🎉 **Applicable Offers:**\n'
          '• Festival Special: 15% OFF (Save ₹90-₹180)\n'
          '• Multi-Room Discount: 10% Extra OFF\n'
          '• Final Cost: ₹456 - ₹914 (after discounts)\n\n'
          '✅ Your booking is almost complete!\n'
          'Would you like to:\n'
          '1. Confirm this booking\n'
          '2. Modify any details\n'
          '3. Add more services\n\n'
          'Reply with your choice (1, 2, or 3).';
    }

    // Handle booking confirmation
    if (lowerMessage.contains('confirm') ||
        lowerMessage.contains('1') ||
        lowerMessage.contains('yes') ||
        lowerMessage.contains('proceed')) {
      return '🎉 Booking Confirmed Successfully!\n\n'
          '📋 Booking Details:\n'
          '• Service: Bathroom Cleaning\n'
          '• Date & Time: As requested\n'
          '• Booking ID: #DB${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}\n\n'
          '📱 You will receive:\n'
          '• SMS confirmation\n'
          '• Professional details 1 hour before service\n'
          '• Real-time tracking\n\n'
          'Is there anything else I can help you with?';
    }

    // Handle offer inquiries
    if (lowerMessage.contains('offer') ||
        lowerMessage.contains('discount') ||
        lowerMessage.contains('deal') ||
        lowerMessage.contains('promotion') ||
        lowerMessage.contains('coupon')) {
      return '🎉 CURRENT OFFERS & DISCOUNTS:\n\n'
          '🏷️ **Festival Special** - 15% OFF\n'
          '   • Valid on all cleaning services\n'
          '   • Code: FESTIVAL15\n'
          '   • Valid till month end\n\n'
          '🎁 **Multi-Room Discount** - 10% Extra OFF\n'
          '   • Book 2+ rooms for cleaning\n'
          '   • Automatic discount applied\n\n'
          '💰 **First Time Customer** - 20% OFF\n'
          '   • For new customers only\n'
          '   • Code: WELCOME20\n\n'
          '🔄 **Weekly Package** - 25% OFF\n'
          '   • Book weekly cleaning service\n'
          '   • Long-term savings\n\n'
          '✨ All offers can be combined with service bookings!\n'
          'Would you like to book a service with these offers?';
    }

    // Basic status responses
    if (lowerMessage.contains('status') ||
        lowerMessage.contains('track') ||
        lowerMessage.contains('progress')) {
      return 'To check your booking status, please provide your booking ID or check the "My Bookings" section in the app.';
    }

    // Basic contact responses
    if (lowerMessage.contains('contact') ||
        lowerMessage.contains('phone') ||
        lowerMessage.contains('call') ||
        lowerMessage.contains('email')) {
      return 'You can reach our customer support <NAME_EMAIL> or call us at +91-9876543210 between 9 AM and 9 PM.';
    }

    // Basic thank you responses
    if (lowerMessage.contains('thank') || lowerMessage.contains('thanks')) {
      return 'You\'re welcome! Is there anything else I can help you with?';
    }

    // Default response
    return 'Thank you for your message. Our support team will assist you shortly. If you need immediate help, please call us at +91-9876543210.';
  }

  // Generate a response based on the user's message (legacy method for compatibility)
  String _generateAgentResponse(String userMessage) {
    return _generateBasicResponse(userMessage);
  }

  // Helper method to detect address patterns
  bool _containsAddressPattern(String message) {
    final addressKeywords = [
      'street',
      'road',
      'avenue',
      'block',
      'house',
      'flat',
      'apartment',
      'building',
      'sector',
      'area',
      'city',
      'pincode',
      'pin'
    ];
    return addressKeywords.any((keyword) => message.contains(keyword));
  }

  // Helper method to detect time patterns
  bool _containsTimePattern(String message) {
    final timeKeywords = [
      'morning',
      'afternoon',
      'evening',
      'night',
      'am',
      'pm',
      'o\'clock',
      'hour',
      'time'
    ];
    return timeKeywords.any((keyword) => message.contains(keyword)) ||
        RegExp(r'\d{1,2}:\d{2}').hasMatch(message) ||
        RegExp(r'\d{1,2}\s*(am|pm)').hasMatch(message);
  }

  // Helper method to detect quantity patterns
  bool _containsQuantityPattern(String message) {
    final quantityKeywords = ['bathroom', 'room', 'qty', 'quantity'];
    final hasQuantityKeyword =
        quantityKeywords.any((keyword) => message.contains(keyword));
    final hasNumber = RegExp(r'\b\d+\b').hasMatch(message);
    final hasNumberWords = [
      'one',
      'two',
      'three',
      'four',
      'five',
      'six',
      'seven',
      'eight',
      'nine',
      'ten'
    ].any((word) => message.contains(word));

    return hasQuantityKeyword && (hasNumber || hasNumberWords);
  }

  // Helper method to extract quantity from message
  String _extractQuantity(String message) {
    // Check for numeric patterns first
    final numericMatch = RegExp(r'\b(\d+)\b').firstMatch(message);
    if (numericMatch != null) {
      return numericMatch.group(1)!;
    }

    // Check for word numbers
    final numberWords = {
      'one': '1',
      'two': '2',
      'three': '3',
      'four': '4',
      'five': '5',
      'six': '6',
      'seven': '7',
      'eight': '8',
      'nine': '9',
      'ten': '10'
    };

    for (final entry in numberWords.entries) {
      if (message.contains(entry.key)) {
        return entry.value;
      }
    }

    return '1'; // Default to 1 if no quantity found
  }

  // Simulate new notifications
  void _simulateNewNotifications() {
    if (_currentUserId == null) return;

    final random = Random();

    // Only create a notification sometimes
    if (random.nextDouble() > 0.3) return;

    final notificationTypes = [
      {
        'title': 'Weekend Special',
        'message': 'Get 15% off on all services this weekend. Book now!',
        'type': 'offer',
        'actionUrl': '/offers',
      },
      {
        'title': 'New Service Available',
        'message': 'We now offer premium car washing services. Try it today!',
        'type': 'new_service',
        'actionUrl': '/categories/9',
      },
      {
        'title': 'Rate Your Experience',
        'message': 'How was your recent service experience? Tap to rate.',
        'type': 'rating',
        'actionUrl': '/bookings',
      },
      {
        'title': 'Monsoon Ready',
        'message':
            'Prepare your home for monsoon with our special plumbing services.',
        'type': 'seasonal',
        'actionUrl': '/categories/2',
      },
    ];

    final notificationType =
        notificationTypes[random.nextInt(notificationTypes.length)];

    _createNotification(
        notificationType['title']!,
        notificationType['message']!,
        notificationType['type']!,
        notificationType['actionUrl']);
  }

  // Create a notification
  void _createNotification(
      String title, String message, String type, String? actionUrl) {
    if (_currentUserId == null) return;

    final notification = {
      'id': 'notif-${DateTime.now().millisecondsSinceEpoch}',
      'userId': _currentUserId,
      'title': title,
      'message': message,
      'timestamp': DateTime.now().toIso8601String(),
      'isRead': false,
      'type': type,
      'actionUrl': actionUrl,
    };

    // Add to user notifications
    final userNotifications = List<Map<String, dynamic>>.from(
        _database['userNotifications'][_currentUserId!] ?? []);
    userNotifications.add(notification);
    _database['userNotifications'][_currentUserId!] = userNotifications;

    // Add to global notifications
    final allNotifications =
        List<Map<String, dynamic>>.from(_database['notifications']);
    allNotifications.add(notification);
    _database['notifications'] = allNotifications;

    // Notify listeners
    _notificationsStreamController.add(userNotifications);
  }

  // Simulate booking status updates
  void _simulateBookingStatusUpdates() {
    if (_currentUserId == null) return;

    final userBookings = List<Map<String, dynamic>>.from(
        _database['userBookings'][_currentUserId!] ?? []);

    if (userBookings.isEmpty) return;

    final random = Random();
    var updated = false;

    for (var i = 0; i < userBookings.length; i++) {
      final booking = userBookings[i];

      // Only update pending or confirmed bookings
      if (booking['status'] == 'Pending') {
        // 50% chance to confirm a pending booking
        if (random.nextBool()) {
          booking['status'] = 'Confirmed';
          booking['paymentStatus'] = 'Paid';
          booking['technician'] = 'Jane Smith';
          booking['technicianPhone'] = '+919876543211';
          updated = true;

          // Create a notification
          _createNotification(
              'Booking Confirmed',
              'Your booking for ${booking['service']} has been confirmed.',
              'booking',
              '/bookings');
        }
      } else if (booking['status'] == 'Confirmed') {
        // Check if the booking date has passed
        final bookingDate = booking['date'] as DateTime;
        if (bookingDate.isBefore(DateTime.now()) && random.nextBool()) {
          booking['status'] = 'Completed';
          updated = true;

          // Create a notification
          _createNotification(
              'Booking Completed',
              'Your booking for ${booking['service']} has been completed. Please rate your experience.',
              'booking',
              '/bookings');
        }
      }
    }

    if (updated) {
      // Update user bookings
      _database['userBookings'][_currentUserId!] = userBookings;

      // Update global bookings
      final allBookings =
          List<Map<String, dynamic>>.from(_database['bookings']);
      for (var i = 0; i < allBookings.length; i++) {
        final booking = allBookings[i];
        if (booking['userId'] == _currentUserId) {
          final updatedBooking = userBookings.firstWhere(
            (b) => b['id'] == booking['id'],
            orElse: () => booking,
          );
          allBookings[i] = updatedBooking;
        }
      }
      _database['bookings'] = allBookings;

      // Notify listeners
      _bookingsStreamController.add(userBookings);
    }
  }

  // Helper method to get category name by ID
  String _getCategoryNameById(String categoryId) {
    // Check if it's a subcategory ID (e.g., "1-1")
    if (categoryId.contains('-')) {
      final subcategoryId = categoryId;
      final subcategories =
          List<Map<String, dynamic>>.from(_database['subcategories']);
      final subcategory = subcategories.firstWhere(
        (s) => s['id'] == subcategoryId,
        orElse: () => {'name': 'Unknown Subcategory'},
      );
      return subcategory['name'];
    }

    // It's a main category ID
    final categories = List<Map<String, dynamic>>.from(_database['categories']);
    final category = categories.firstWhere(
      (c) => c['id'] == categoryId,
      orElse: () => {'name': 'Unknown Category'},
    );
    return category['name'];
  }

  // Helper method to get a random time slot
  String _getRandomTimeSlot() {
    final slots = [
      '09:00 AM',
      '10:00 AM',
      '11:00 AM',
      '12:00 PM',
      '01:00 PM',
      '02:00 PM',
      '03:00 PM',
      '04:00 PM',
      '05:00 PM',
      '06:00 PM',
      '07:00 PM',
      '08:00 PM',
    ];
    return slots[Random().nextInt(slots.length)];
  }

  // Helper method to get a random address
  String _getRandomAddress() {
    final addresses = [
      '123 Main Street, Apartment 4B, Mumbai',
      '456 Business Park, Tower B, Floor 5, Mumbai',
      '789 Residential Complex, Building C, Flat 302, Mumbai',
      '234 Market Road, Shop 12, Mumbai',
      '567 Garden View, Villa 7, Mumbai',
    ];
    return addresses[Random().nextInt(addresses.length)];
  }

  // Helper method to get a random payment method
  String _getRandomPaymentMethod() {
    final methods = [
      'Credit/Debit Card',
      'Cash on Delivery',
      'Google Pay',
      'PhonePe',
      'Paytm',
    ];
    return methods[Random().nextInt(methods.length)];
  }
}
