import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';

class SimplePhoneLoginScreen extends StatefulWidget {
  const SimplePhoneLoginScreen({Key? key}) : super(key: key);

  @override
  State<SimplePhoneLoginScreen> createState() => _SimplePhoneLoginScreenState();
}

class _SimplePhoneLoginScreenState extends State<SimplePhoneLoginScreen> {
  final _mobileController = TextEditingController();
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (_) => TextEditingController(),
  );
  bool _isLoading = false;
  bool _otpSent = false;
  int _resendCountdown = 0;

  @override
  void dispose() {
    _mobileController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startResendTimer() {
    setState(() {
      _resendCountdown = 30;
    });

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
        _startResendTimer();
      }
    });
  }

  void _sendOtp() async {
    if (_mobileController.text.length < 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid mobile number'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Clear any previous OTP
    for (var controller in _otpControllers) {
      controller.clear();
    }

    final success = await authProvider.sendOtp(_mobileController.text);
    print("OTP send result: $success");
    print("Auth status after send: ${authProvider.status}");
    print("Mock OTP: ${authProvider.mockOtp}");

    if (mounted) {
      setState(() {
        _isLoading = false;
        if (success) {
          _otpSent = true;
          _startResendTimer();
        }
      });

      // Show success message
      if (success) {
        String message = 'OTP sent successfully.';
        if (authProvider.mockOtp != null) {
          message += ' Use ${authProvider.mockOtp} for testing.';

          // Auto-fill OTP fields for testing after a short delay
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted &&
                authProvider.mockOtp != null &&
                authProvider.mockOtp!.length == 6) {
              setState(() {
                for (int i = 0; i < 6; i++) {
                  _otpControllers[i].text = authProvider.mockOtp![i];
                }
              });
            }
          });
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      } else if (authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _verifyOtp() async {
    final otp = _otpControllers.map((controller) => controller.text).join();

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid 6-digit OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.verifyOtp(otp);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (!success && authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resendOtp() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.resendOtp();

    if (mounted) {
      setState(() {
        _isLoading = false;

        // Auto-fill OTP fields for testing
        if (success &&
            authProvider.mockOtp != null &&
            authProvider.mockOtp!.length == 6) {
          for (int i = 0; i < 6; i++) {
            _otpControllers[i].text = authProvider.mockOtp![i];
          }
        }
      });

      if (success) {
        _startResendTimer();

        String message = 'OTP resent successfully.';
        if (authProvider.mockOtp != null) {
          message += ' Use ${authProvider.mockOtp} for testing.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      } else if (authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildMobileInputScreen() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: TextField(
            controller: _mobileController,
            keyboardType: TextInputType.phone,
            decoration: const InputDecoration(
              hintText: 'Mobile Number',
              prefixIcon: Icon(Icons.phone_android),
              border: InputBorder.none,
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(10),
            ],
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'We will send you a one-time password (OTP) to verify your mobile number',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.grey,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _sendOtp,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text(
                    'Send OTP',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'By continuing, you agree to our Terms of Service and Privacy Policy',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildOtpVerificationScreen() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Text(
          'Enter the 6-digit code sent to ${_mobileController.text}',
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(
            6,
            (index) => SizedBox(
              width: 40,
              child: TextField(
                controller: _otpControllers[index],
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                maxLength: 1,
                decoration: const InputDecoration(
                  counterText: '',
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.black),
                  ),
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                onChanged: (value) {
                  if (value.isNotEmpty && index < 5) {
                    FocusScope.of(context).nextFocus();
                  }
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 30),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _verifyOtp,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Text(
                    'Verify',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 20),
        TextButton(
          onPressed: _resendCountdown > 0 ? null : _resendOtp,
          child: Text(
            _resendCountdown > 0
                ? 'Resend OTP in $_resendCountdown seconds'
                : 'Resend OTP',
            style: TextStyle(
              color: _resendCountdown > 0 ? Colors.grey : Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get the auth provider to check its status
    final authProvider = Provider.of<AuthProvider>(context);

    print(
        "SimplePhoneLoginScreen: Current auth status: ${authProvider.status}");
    print("SimplePhoneLoginScreen: Current _otpSent value: $_otpSent");

    // Update local state based on auth provider status
    if (authProvider.status == AuthStatus.otpSent && !_otpSent) {
      print("SimplePhoneLoginScreen: Setting _otpSent to true");
      setState(() {
        _otpSent = true;

        // Auto-fill OTP fields for testing
        if (authProvider.mockOtp != null && authProvider.mockOtp!.length == 6) {
          for (int i = 0; i < 6; i++) {
            _otpControllers[i].text = authProvider.mockOtp![i];
          }
        }
      });
    } else if (authProvider.status == AuthStatus.unauthenticated && _otpSent) {
      // Reset OTP state when logged out
      print("SimplePhoneLoginScreen: Resetting _otpSent to false");
      setState(() {
        _otpSent = false;
        _mobileController.clear();
        for (var controller in _otpControllers) {
          controller.clear();
        }
      });
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Center(
                    child: Text(
                      'DODO',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'DODO Booker',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Your home services, simplified',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 40),
                // Use the auth provider status to determine which screen to show
                authProvider.status == AuthStatus.otpSent
                    ? _buildOtpVerificationScreen()
                    : _buildMobileInputScreen(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
