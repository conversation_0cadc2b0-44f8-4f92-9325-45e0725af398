# Multi-User Type Authentication System

This authentication system supports three different user types (Customers, Staff, Service Providers) with different authentication methods:

- **Customers**: Mobile number and OTP
- **Staff**: Email and password
- **Service Providers**: Mobile number and OTP

## User Model

The system uses a custom User model with the following key fields:

- `email`: Email address (unique, optional)
- `mobile`: Mobile number (unique, optional)
- `user_type`: Type of user (CUSTOMER, STAFF, SERVICE_PROVIDER)
- Standard Django fields: first_name, last_name, is_active, is_staff, is_superuser, etc.

## Authentication Backends

The system includes multiple authentication backends:

1. `EmailBackend`: For staff users (email/password)
2. `MobileBackend`: For customers and service providers (mobile/OTP)
3. `MultiFieldModelBackend`: Fallback that supports both email and mobile
4. Django's default `ModelBackend`

## API Endpoints

### Common Endpoints

- `api/auth/token/`: Get JWT tokens using email/password (standard response)
- `api/auth/token/refresh/`: Refresh JWT token (standard response)
- `api/auth/token/cookie/`: Get JWT tokens as HTTP-only cookies
- `api/auth/token/cookie/refresh/`: Refresh JWT token in cookies
- `api/auth/token/cookie/logout/`: Clear authentication cookies
- `api/auth/user/info/`: Get current user information

### Customer Endpoints

- `api/auth/customer/otp/send/`: Request OTP for customer login
- `api/auth/customer/otp/resend/`: Resend OTP for customer login
- `api/auth/customer/otp/verify/`: Verify OTP and login as customer

### Service Provider Endpoints

- `api/auth/service-provider/otp/send/`: Request OTP for service provider login
- `api/auth/service-provider/otp/resend/`: Resend OTP for service provider login
- `api/auth/service-provider/otp/verify/`: Verify OTP and login as service provider

### Staff Endpoints

- `api/auth/staff/login/`: Login with email and password as staff

## Authentication Flow

### Customer/Service Provider Authentication (Mobile OTP)

1. Request OTP:
   ```
   POST /api/auth/customer/otp/send/
   {
     "mobile": "+919876543210",
     "user_type": "CUSTOMER"
   }
   ```

2. Verify OTP:
   ```
   POST /api/auth/customer/otp/verify/
   {
     "mobile": "+919876543210",
     "otp": "123456",
     "user_type": "CUSTOMER"
   }
   ```

3. Response includes user data and sets JWT tokens as HTTP-only cookies.

### Staff Authentication (Email/Password)

1. Login:
   ```
   POST /api/auth/staff/login/
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```

2. Response includes user data and sets JWT tokens as HTTP-only cookies.

## Security Features

- JWT tokens stored in HTTP-only cookies
- CSRF protection for cookie-based authentication
- Proper validation for mobile numbers and OTPs
- User type validation to ensure users authenticate through the correct method

## Integration with Next.js PWA

### Customer-facing PWA

Use the customer OTP endpoints for authentication.

### Staff Admin Panel

Use the staff login endpoint for authentication.

### Service Provider Portal

Use the service provider OTP endpoints for authentication.

## Testing the API

You can test the API using the provided test scripts:

- `test_staff_login.py`: Test staff login with email/password
- `test_customer_otp.py`: Test customer login with mobile/OTP
- `test_service_provider_otp.py`: Test service provider login with mobile/OTP

Or use Postman to make requests to the API endpoints.

## Implementation Details

The authentication system is implemented using:

- Django REST Framework
- Simple JWT for token-based authentication
- Custom authentication backends
- Custom user model
- MSG91 for OTP generation and verification

## Migration Guide

If you're migrating from the previous authentication system:

1. Run migrations: `python manage.py migrate`
2. Update existing users' `user_type` field:
   ```python
   from django.contrib.auth import get_user_model
   User = get_user_model()
   
   # Set existing users as customers
   User.objects.filter(user_type='').update(user_type='CUSTOMER')
   ```

3. Create staff users:
   ```python
   User.objects.create_staff(email='<EMAIL>', password='password123')
   ```

## Troubleshooting

- If OTP verification fails, check the MSG91 API key and template ID
- If token authentication fails, check the SIMPLE_JWT settings
- If CSRF validation fails, ensure the X-CSRFToken header is included in requests
