-- Sample data for Do<PERSON><PERSON>ooker
-- Run this after creating the database schema

-- Insert sample categories
INSERT INTO categories (name, description, icon_name, icon_code_point, color_hex, display_order) VALUES
('Cleaning', 'Professional cleaning services for your home', 'cleaning_services', 59470, '#4ECDC4', 1),
('Plumbing', 'Expert plumbing services for all your needs', 'plumbing', 59471, '#FF9800', 2),
('Electrical', 'Professional electrical services for your home', 'electrical_services', 59472, '#F44336', 3),
('Carpenter', 'Expert carpentry services for your home', 'handyman', 59473, '#795548', 4),
('Appliances', 'Appliance repair and maintenance services', 'build', 59474, '#2196F3', 5),
('Painting', 'Professional painting services', 'format_paint', 59475, '#FFEB3B', 6),
('AC Services', 'Air conditioning repair and maintenance', 'ac_unit', 59476, '#00BCD4', 7),
('Pest Control', 'Professional pest control services', 'bug_report', 59477, '#E91E63', 8);

-- Get category IDs for reference
-- Note: In a real scenario, you'd use the actual UUIDs generated

-- Insert sample subcategories (you'll need to replace the UUIDs with actual ones from your categories)
-- For Cleaning category
INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'House Cleaning', 'Complete house cleaning service', 1 FROM categories WHERE name = 'Cleaning';

INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Deep Cleaning', 'Thorough deep cleaning service', 2 FROM categories WHERE name = 'Cleaning';

INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Kitchen Cleaning', 'Specialized kitchen cleaning', 3 FROM categories WHERE name = 'Cleaning';

-- For Plumbing category
INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Pipe Repair', 'Pipe repair and replacement', 1 FROM categories WHERE name = 'Plumbing';

INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Tap Installation', 'Tap and faucet installation', 2 FROM categories WHERE name = 'Plumbing';

INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Drain Cleaning', 'Drain cleaning and unclogging', 3 FROM categories WHERE name = 'Plumbing';

-- For Electrical category
INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Switch Installation', 'Switch and socket installation', 1 FROM categories WHERE name = 'Electrical';

INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Wiring', 'Electrical wiring services', 2 FROM categories WHERE name = 'Electrical';

INSERT INTO subcategories (category_id, name, description, display_order) 
SELECT id, 'Fan Installation', 'Ceiling fan installation', 3 FROM categories WHERE name = 'Electrical';

-- Insert sample products
-- Cleaning products
INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, '1 BHK House Cleaning', 'Complete cleaning for 1 BHK apartment', 299.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Cleaning' AND s.name = 'House Cleaning';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, '2 BHK House Cleaning', 'Complete cleaning for 2 BHK apartment', 499.00, 2
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Cleaning' AND s.name = 'House Cleaning';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, '3 BHK House Cleaning', 'Complete cleaning for 3 BHK apartment', 699.00, 3
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Cleaning' AND s.name = 'House Cleaning';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, '1 BHK Deep Cleaning', 'Thorough deep cleaning for 1 BHK', 599.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Cleaning' AND s.name = 'Deep Cleaning';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, '2 BHK Deep Cleaning', 'Thorough deep cleaning for 2 BHK', 899.00, 2
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Cleaning' AND s.name = 'Deep Cleaning';

-- Plumbing products
INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, 'Pipe Leak Repair', 'Fix pipe leaks and damages', 199.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Plumbing' AND s.name = 'Pipe Repair';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, 'Tap Installation', 'Install new taps and faucets', 149.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Plumbing' AND s.name = 'Tap Installation';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, 'Drain Unclogging', 'Clear blocked drains', 99.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Plumbing' AND s.name = 'Drain Cleaning';

-- Electrical products
INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, 'Switch Installation', 'Install electrical switches', 79.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Electrical' AND s.name = 'Switch Installation';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, 'Socket Installation', 'Install electrical sockets', 89.00, 2
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Electrical' AND s.name = 'Switch Installation';

INSERT INTO products (category_id, subcategory_id, name, description, price, display_order)
SELECT c.id, s.id, 'Ceiling Fan Installation', 'Install ceiling fans', 199.00, 1
FROM categories c 
JOIN subcategories s ON c.id = s.category_id 
WHERE c.name = 'Electrical' AND s.name = 'Fan Installation';

-- Insert sample coupons
INSERT INTO coupons (code, name, description, discount_type, discount_value, max_discount, min_order_value, usage_limit, valid_from, valid_until) VALUES
('WELCOME10', 'Welcome Offer', 'Get 10% off on your first order', 'percentage', 10.00, 100.00, 200.00, 1000, NOW(), NOW() + INTERVAL '30 days'),
('SAVE50', 'Flat ₹50 Off', 'Get flat ₹50 off on orders above ₹300', 'fixed', 50.00, NULL, 300.00, 500, NOW(), NOW() + INTERVAL '15 days'),
('CLEAN20', 'Cleaning Special', 'Get 20% off on cleaning services', 'percentage', 20.00, 200.00, 250.00, 200, NOW(), NOW() + INTERVAL '7 days'),
('FESTIVE15', 'Festival Offer', 'Get 15% off on all services', 'percentage', 15.00, 150.00, 400.00, 300, NOW(), NOW() + INTERVAL '10 days');

-- Note: After running this script, you can view your data with these queries:
-- SELECT * FROM categories ORDER BY display_order;
-- SELECT * FROM subcategories ORDER BY category_id, display_order;
-- SELECT * FROM products ORDER BY category_id, display_order;
-- SELECT * FROM coupons WHERE is_active = true;
