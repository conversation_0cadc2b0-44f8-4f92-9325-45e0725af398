import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../widgets/cart_icon_with_badge.dart';
import '../models/hierarchical_category.dart';
import 'map_location_screen.dart';
import 'category_navigation_screen.dart';
import 'search_results_screen.dart';

class HierarchicalHomeScreen extends StatefulWidget {
  const HierarchicalHomeScreen({super.key});

  @override
  State<HierarchicalHomeScreen> createState() => _HierarchicalHomeScreenState();
}

class _HierarchicalHomeScreenState extends State<HierarchicalHomeScreen> {
  String _currentAddress = 'Fetching location...';
  bool _isLoadingLocation = true;
  final TextEditingController _searchController = TextEditingController();

  // Get main categories from the hierarchical model
  final List<HierarchicalCategory> _categories =
      CategoryData.getMainCategories();

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _currentAddress = 'Location permissions denied';
            _isLoadingLocation = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _currentAddress = 'Location permissions permanently denied';
          _isLoadingLocation = false;
        });
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        setState(() {
          _currentAddress = _formatAddress(place);
          _isLoadingLocation = false;
        });
      } else {
        setState(() {
          _currentAddress = 'Unknown location';
          _isLoadingLocation = false;
        });
      }
    } catch (e) {
      setState(() {
        _currentAddress = 'Error getting location';
        _isLoadingLocation = false;
      });
    }
  }

  String _formatAddress(Placemark place) {
    List<String> addressParts = [];

    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    if (addressParts.isEmpty &&
        place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }

    return addressParts.join(', ');
  }

  void _openLocationPicker() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MapLocationScreen(),
      ),
    );

    if (result != null && result['address'] != null) {
      setState(() {
        _currentAddress = result['address'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: GestureDetector(
          onTap: _openLocationPicker,
          child: Row(
            children: [
              const Icon(
                Icons.location_on,
                color: Color(0xFF2D4059),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _currentAddress,
                  style: const TextStyle(
                    color: Color(0xFF2D4059),
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (_isLoadingLocation)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF2D4059)),
                  ),
                ),
            ],
          ),
        ),
        actions: const [
          CartIconWithBadge(),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title with highlighted "services" text
                RichText(
                  text: const TextSpan(
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                    children: [
                      TextSpan(text: 'What '),
                      TextSpan(
                        text: 'services',
                        style: TextStyle(
                          color: Color(0xFFFF7D54), // Coral/orange color
                        ),
                      ),
                      TextSpan(text: ' are you\nlooking for?'),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Search bar
                GestureDetector(
                  onTap: () {
                    // Navigate to search screen when clicked
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SearchResultsScreen(
                          initialQuery: '',
                        ),
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        const Icon(Icons.search, color: Colors.grey),
                        const SizedBox(width: 8),
                        Expanded(
                          child: AbsorbPointer(
                            // Absorb pointer events to make the TextField non-interactive
                            // This ensures the GestureDetector handles the tap
                            child: TextField(
                              controller: _searchController,
                              decoration: const InputDecoration(
                                hintText: 'Find a service',
                                border: InputBorder.none,
                                hintStyle: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 16,
                                ),
                              ),
                              onSubmitted: (query) {
                                if (query.isNotEmpty) {
                                  // Special handling for numeric queries (like "1" for "1 BHK")
                                  String searchQuery = query.trim();
                                  bool isNumericQuery =
                                      RegExp(r'^\d+$').hasMatch(searchQuery);

                                  // For numeric queries, also try searching for "X BHK"
                                  if (isNumericQuery) {
                                    searchQuery = "$searchQuery bhk";
                                  }

                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => SearchResultsScreen(
                                        initialQuery: searchQuery,
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Service Categories Grid
                GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 24,
                    childAspectRatio: 0.8,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    return _buildNewCategoryItem(category);
                  },
                ),

                // Add some bottom padding
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNewCategoryItem(HierarchicalCategory category) {
    // Define pastel colors for the circles
    final List<Color> pastelColors = [
      const Color(0xFFB5E8FF), // Light blue
      const Color(0xFFFFE8B5), // Light yellow
      const Color(0xFFE8B5FF), // Light purple
      const Color(0xFFB5FFB5), // Light green
      const Color(0xFFFFB5B5), // Light red
      const Color(0xFFB5E8E8), // Light teal
      const Color(0xFFE8E8B5), // Light lime
      const Color(0xFFE8B5B5), // Light pink
      const Color(0xFFB5B5E8), // Light indigo
      const Color(0xFFB5FFE8), // Light mint
      const Color(0xFFFFB5E8), // Light rose
      const Color(0xFFE8FFB5), // Light chartreuse
    ];

    // Get a color based on the category index
    final int colorIndex = int.parse(category.id) % pastelColors.length;
    final Color circleColor = pastelColors[colorIndex];

    return GestureDetector(
      onTap: () => _navigateToCategory(context, category),
      child: Column(
        children: [
          // Circular icon container
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: circleColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                category.icon,
                size: 36,
                color: Colors.black87,
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Category name
          Text(
            category.name,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _navigateToCategory(
      BuildContext context, HierarchicalCategory category) {
    if (category.hasSubcategories) {
      // Navigate to subcategories
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryNavigationScreen(
            parentCategory: category,
            categories: category.subcategories,
            title: '${category.name} Categories',
          ),
        ),
      );
    } else {
      // Navigate to products/services
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryNavigationScreen(
            parentCategory: category,
            categories: const [],
            title: '${category.name} Services',
          ),
        ),
      );
    }
  }
}
