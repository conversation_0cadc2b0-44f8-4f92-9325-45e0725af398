import 'package:flutter/material.dart';
import '../widgets/neumorphic_container.dart';
import '../models/cart_item.dart';
import '../screens/main_navigation_screen.dart';
import '../providers/cart_provider.dart';
import 'package:provider/provider.dart';

class OrderConfirmationScreen extends StatelessWidget {
  final List<CartItem> cartItems;
  final Map<String, dynamic> address;
  final DateTime date;
  final String time;
  final double totalAmount;
  final String orderId;
  final String paymentMethod;

  const OrderConfirmationScreen({
    super.key,
    required this.cartItems,
    required this.address,
    required this.date,
    required this.time,
    required this.totalAmount,
    required this.orderId,
    this.paymentMethod = 'Cash on Delivery',
  });

  // Helper function to create colors with opacity
  Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  @override
  Widget build(BuildContext context) {
    // Clear the cart when this screen is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CartProvider>(context, listen: false).clear();
    });

    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: const Text(
          'Order Confirmation',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Success icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: withOpacity(Theme.of(context).primaryColor, 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  size: 60,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 24),

              // Success message
              const Text(
                'Booking Confirmed!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D4059),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Your booking has been confirmed.\nOrder ID: $orderId',
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF7D8CA3),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Order details
              NeumorphicContainer(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Order Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D4059),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: withOpacity(Colors.blue, 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.shopping_cart,
                            size: 30,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Items: ${cartItems.length}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2D4059),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Total Quantity: ${cartItems.fold(0, (sum, item) => sum + item.quantity)}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF7D8CA3),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '₹${totalAmount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    _buildInfoRow(
                      Icons.location_on,
                      '${address['type']} Address',
                      address['address'],
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      Icons.calendar_today,
                      'Date & Time',
                      '${_getDayName(date.weekday)}, ${date.day} ${_getMonthName(date.month)} ${date.year} • $time',
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      Icons.payment,
                      'Payment Method',
                      paymentMethod,
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      Icons.receipt,
                      'Payment Status',
                      paymentMethod == 'Cash on Delivery' ? 'Pending' : 'Paid',
                      isHighlighted: true,
                      statusColor: paymentMethod == 'Cash on Delivery'
                          ? const Color(0xFFFFA000) // Amber for pending
                          : const Color(0xFF4CAF50), // Green for paid
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Instructions
              NeumorphicContainer(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 20,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'What\'s Next?',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '1. You will receive a confirmation email with your booking details.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7D8CA3),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '2. Our service provider will contact you before the appointment.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7D8CA3),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '3. You can view and manage your booking in the "Bookings" section.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7D8CA3),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // Buttons
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(
                        builder: (context) => const MainNavigationScreen(),
                      ),
                      (route) => false,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Back to Home',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  // Navigate to bookings tab
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(
                      builder: (context) =>
                          const MainNavigationScreen(initialIndex: 1),
                    ),
                    (route) => false,
                  );
                },
                child: const Text(
                  'View My Bookings',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String title, String value,
      {bool isHighlighted = false, Color? statusColor}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20,
          color: const Color(0xFF7D8CA3),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2D4059),
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight:
                      isHighlighted ? FontWeight.bold : FontWeight.normal,
                  color: isHighlighted
                      ? (statusColor ?? const Color(0xFF4CAF50))
                      : const Color(0xFF7D8CA3),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }
}
