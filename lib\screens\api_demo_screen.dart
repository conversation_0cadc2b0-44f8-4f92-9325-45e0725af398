import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/api_provider.dart';
import '../models/cleaning_product.dart';

/// A demo screen to showcase the enhanced mock API functionality
class ApiDemoScreen extends StatefulWidget {
  const ApiDemoScreen({Key? key}) : super(key: key);

  @override
  State<ApiDemoScreen> createState() => _ApiDemoScreenState();
}

class _ApiDemoScreenState extends State<ApiDemoScreen> {
  final _searchController = TextEditingController();
  String _selectedCategoryId = '';
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _subcategories = [];
  List<CleaningProduct> _products = [];
  Map<String, dynamic>? _cart;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final apiProvider = Provider.of<ApiProvider>(context, listen: false);
      
      // Load categories
      await apiProvider.loadCategories();
      _categories = apiProvider.categories;
      
      // Load subcategories
      await apiProvider.loadSubcategories();
      _subcategories = apiProvider.subcategories;
      
      // Get cart
      _cart = await apiProvider.getCart();
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to load data: $e';
      });
    }
  }

  Future<void> _loadProductsByCategory(String categoryId) async {
    setState(() {
      _isLoading = true;
      _error = null;
      _selectedCategoryId = categoryId;
    });

    try {
      final apiProvider = Provider.of<ApiProvider>(context, listen: false);
      final products = await apiProvider.getProductsByCategory(categoryId);
      
      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to load products: $e';
      });
    }
  }

  Future<void> _searchProducts(String query) async {
    if (query.isEmpty) return;
    
    setState(() {
      _isLoading = true;
      _error = null;
      _selectedCategoryId = '';
    });

    try {
      final apiProvider = Provider.of<ApiProvider>(context, listen: false);
      final products = await apiProvider.searchProducts(query);
      
      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to search products: $e';
      });
    }
  }

  Future<void> _addToCart(String productId) async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final apiProvider = Provider.of<ApiProvider>(context, listen: false);
      final success = await apiProvider.addToCart(productId, 1);
      
      if (success) {
        _cart = apiProvider.cart;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Product added to cart')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(apiProvider.error ?? 'Failed to add to cart')),
        );
      }
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Failed to add to cart: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Demo'),
        actions: [
          if (_cart != null)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.shopping_cart),
                    onPressed: () {
                      // Navigate to cart screen
                    },
                  ),
                  if ((_cart!['items'] as List).isNotEmpty)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          (_cart!['items'] as List).length.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : Column(
                  children: [
                    // Search bar
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search products...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _products = [];
                                _selectedCategoryId = '';
                              });
                            },
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onSubmitted: _searchProducts,
                      ),
                    ),
                    
                    // Categories
                    SizedBox(
                      height: 50,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _categories.length,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = category['id'] == _selectedCategoryId;
                          
                          return Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: ChoiceChip(
                              label: Text(category['name']),
                              selected: isSelected,
                              onSelected: (selected) {
                                if (selected) {
                                  _loadProductsByCategory(category['id']);
                                }
                              },
                            ),
                          );
                        },
                      ),
                    ),
                    
                    // Products
                    Expanded(
                      child: _products.isEmpty
                          ? const Center(child: Text('Select a category or search for products'))
                          : ListView.builder(
                              itemCount: _products.length,
                              padding: const EdgeInsets.all(16),
                              itemBuilder: (context, index) {
                                final product = _products[index];
                                
                                return Card(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: ListTile(
                                    leading: product.imageUrl != null
                                        ? Image.network(
                                            product.imageUrl!,
                                            width: 50,
                                            height: 50,
                                            fit: BoxFit.cover,
                                            errorBuilder: (_, __, ___) => const Icon(Icons.image),
                                          )
                                        : const Icon(Icons.image),
                                    title: Text(product.name),
                                    subtitle: Text('₹${product.price}'),
                                    trailing: IconButton(
                                      icon: const Icon(Icons.add_shopping_cart),
                                      onPressed: () => _addToCart(product.id),
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
    );
  }
}
