import 'package:flutter/material.dart';

class QuantityCounter extends StatefulWidget {
  final int initialValue;
  final int minValue;
  final int maxValue;
  final Function(int) onChanged;
  final double width;
  final double height;
  final Color backgroundColor;
  final Color activeColor;
  final Color textColor;

  const QuantityCounter({
    super.key,
    this.initialValue = 1,
    this.minValue = 1,
    this.maxValue = 10,
    required this.onChanged,
    this.width = 120,
    this.height = 40,
    this.backgroundColor = Colors.white,
    this.activeColor = Colors.blue,
    this.textColor = Colors.black,
  });

  @override
  State<QuantityCounter> createState() => _QuantityCounterState();
}

class _QuantityCounterState extends State<QuantityCounter> {
  late int _quantity;

  @override
  void initState() {
    super.initState();
    _quantity = widget.initialValue;
  }

  void _increment() {
    if (_quantity < widget.maxValue) {
      setState(() {
        _quantity++;
      });
      widget.onChanged(_quantity);
    }
  }

  void _decrement() {
    if (_quantity > widget.minValue) {
      setState(() {
        _quantity--;
      });
      widget.onChanged(_quantity);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Decrement button
          _buildButton(
            icon: Icons.remove,
            onPressed: _decrement,
            isEnabled: _quantity > widget.minValue,
          ),
          
          // Quantity display
          Text(
            _quantity.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
          ),
          
          // Increment button
          _buildButton(
            icon: Icons.add,
            onPressed: _increment,
            isEnabled: _quantity < widget.maxValue,
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required IconData icon,
    required VoidCallback onPressed,
    required bool isEnabled,
  }) {
    return InkWell(
      onTap: isEnabled ? onPressed : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: widget.height,
        height: widget.height,
        decoration: BoxDecoration(
          color: isEnabled ? widget.activeColor : widget.activeColor.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }
}
