class ServiceAddon {
  final String id;
  final String serviceId;
  final String name;
  final String description;
  final double price;
  final bool isPopular;
  final int quantity;

  ServiceAddon({
    required this.id,
    required this.serviceId,
    required this.name,
    required this.description,
    required this.price,
    this.isPopular = false,
    this.quantity = 1,
  });

  // Create a copy of this addon with a new quantity
  ServiceAddon copyWith({int? quantity}) {
    return ServiceAddon(
      id: id,
      serviceId: serviceId,
      name: name,
      description: description,
      price: price,
      isPopular: isPopular,
      quantity: quantity ?? this.quantity,
    );
  }

  // Calculate the total price based on quantity
  double get totalPrice => price * quantity;
}
