import 'package:flutter/material.dart';
import '../widgets/neumorphic_container.dart';
import '../screens/main_navigation_screen.dart';

enum NavigationSection {
  home,
  bookings,
  support,
  profile,
}

class AppNavigationBar extends StatelessWidget {
  final NavigationSection currentSection;
  final BuildContext mainContext;

  const AppNavigationBar({
    super.key,
    required this.currentSection,
    required this.mainContext,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0xFFCFD8DC),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(context, NavigationSection.home, Icons.home_outlined,
              Icons.home, 'Home'),
          _buildNavItem(context, NavigationSection.bookings,
              Icons.calendar_today_outlined, Icons.calendar_today, 'Bookings'),
          _buildNavItem(context, NavigationSection.support, Icons.chat_outlined,
              Icons.chat, 'Support'),
          _buildNavItem(context, NavigationSection.profile,
              Icons.person_outline, Icons.person, 'Profile'),
        ],
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    NavigationSection section,
    IconData icon,
    IconData activeIcon,
    String label,
  ) {
    final isSelected = currentSection == section;
    final primaryColor = Theme.of(context).primaryColor;

    return GestureDetector(
      onTap: () => _navigateToSection(section),
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        width: 65,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            NeumorphicContainer(
              width: 36,
              height: 36,
              borderRadius: 18,
              padding: EdgeInsets.zero,
              isPressed: isSelected,
              color: isSelected
                  ? const Color(
                      0xFFF5F5F5) // Light gray background for selected items
                  : Colors.white,
              child: Icon(
                isSelected ? activeIcon : icon,
                color: isSelected ? primaryColor : const Color(0xFF7D8CA3),
                size: 18,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? primaryColor : const Color(0xFF7D8CA3),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToSection(NavigationSection section) {
    // Don't navigate if already on the selected section
    if (section == currentSection) return;

    // Map navigation sections to screen indices (after removing search)
    int screenIndex;
    switch (section) {
      case NavigationSection.home:
        screenIndex = 0;
        break;
      case NavigationSection.bookings:
        screenIndex = 1;
        break;
      case NavigationSection.support:
        screenIndex = 2;
        break;
      case NavigationSection.profile:
        screenIndex = 3;
        break;
    }

    // Pop to root and navigate to the selected section
    Navigator.of(mainContext).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => MainNavigationScreen(
          initialIndex: screenIndex,
        ),
      ),
      (route) => false, // Remove all previous routes
    );
  }
}
