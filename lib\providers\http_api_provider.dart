import 'package:flutter/material.dart';
import '../services/http_api_service.dart';

/// Provider that uses the HTTP API service to connect to the Node.js server
/// This provider fetches data from your actual mock_api_server.js
class HttpApiProvider with ChangeNotifier {
  final HttpApiService _apiService = HttpApiService();

  // Loading states
  bool _isLoading = false;
  bool _isConnected = false;
  String? _error;

  // Data
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _subcategories = [];
  List<Map<String, dynamic>> _products = [];
  Map<String, dynamic>? _cart;
  List<Map<String, dynamic>> _bookings = [];
  Map<String, dynamic>? _profile;
  List<Map<String, dynamic>> _addresses = [];
  List<Map<String, dynamic>> _coupons = [];

  // Getters
  bool get isLoading => _isLoading;
  bool get isConnected => _isConnected;
  String? get error => _error;
  List<Map<String, dynamic>> get categories => _categories;
  List<Map<String, dynamic>> get subcategories => _subcategories;
  List<Map<String, dynamic>> get products => _products;
  Map<String, dynamic>? get cart => _cart;
  List<Map<String, dynamic>> get bookings => _bookings;
  Map<String, dynamic>? get profile => _profile;
  List<Map<String, dynamic>> get addresses => _addresses;
  List<Map<String, dynamic>> get coupons => _coupons;

  HttpApiProvider() {
    _testConnection();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setConnected(bool connected) {
    _isConnected = connected;
    notifyListeners();
  }

  // Test connection to the Node.js server
  Future<void> _testConnection() async {
    try {
      _setLoading(true);
      _setError(null);
      
      final isConnected = await _apiService.testConnection();
      _setConnected(isConnected);
      
      if (isConnected) {
        print('HttpApiProvider: Successfully connected to Node.js server');
        // Load initial data
        await loadCategories();
      } else {
        _setError('Cannot connect to Node.js server at http://localhost:3000');
        print('HttpApiProvider: Failed to connect to Node.js server');
      }
    } catch (e) {
      _setError('Connection error: $e');
      _setConnected(false);
      print('HttpApiProvider: Connection error: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Retry connection
  Future<void> retryConnection() async {
    await _testConnection();
  }

  // Authentication methods
  Future<Map<String, dynamic>> sendOtp(String mobile) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final result = await _apiService.sendOtp(mobile);
      
      if (result['status'] != 'success') {
        _setError(result['message']);
      }
      
      return result;
    } catch (e) {
      _setError('Error sending OTP: $e');
      return {'status': 'error', 'message': 'Error sending OTP: $e'};
    } finally {
      _setLoading(false);
    }
  }

  Future<Map<String, dynamic>> verifyOtp(String mobile, String otp) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final result = await _apiService.verifyOtp(mobile, otp);
      
      if (result['status'] == 'success') {
        // Load user data after successful authentication
        await loadProfile();
        await loadCart();
        await loadBookings();
        await loadAddresses();
      } else {
        _setError(result['message']);
      }
      
      return result;
    } catch (e) {
      _setError('Error verifying OTP: $e');
      return {'status': 'error', 'message': 'Error verifying OTP: $e'};
    } finally {
      _setLoading(false);
    }
  }

  Future<Map<String, dynamic>> logout() async {
    try {
      _setLoading(true);
      _setError(null);
      
      final result = await _apiService.logout();
      
      if (result['status'] == 'success') {
        // Clear user data
        _cart = null;
        _bookings = [];
        _profile = null;
        _addresses = [];
        notifyListeners();
      }
      
      return result;
    } catch (e) {
      _setError('Error logging out: $e');
      return {'status': 'error', 'message': 'Error logging out: $e'};
    } finally {
      _setLoading(false);
    }
  }

  // Category methods
  Future<void> loadCategories() async {
    try {
      _setLoading(true);
      _setError(null);
      
      final result = await _apiService.getCategories();
      
      if (result['status'] == 'success') {
        _categories = List<Map<String, dynamic>>.from(result['data'] ?? []);
        print('HttpApiProvider: Loaded ${_categories.length} categories from server');
      } else {
        _setError(result['message']);
      }
    } catch (e) {
      _setError('Error loading categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadSubcategories(String categoryId) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final result = await _apiService.getSubcategories(categoryId);
      
      if (result['status'] == 'success') {
        _subcategories = List<Map<String, dynamic>>.from(result['data'] ?? []);
        print('HttpApiProvider: Loaded ${_subcategories.length} subcategories for category $categoryId');
      } else {
        _setError(result['message']);
      }
    } catch (e) {
      _setError('Error loading subcategories: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadProducts(String categoryId, String subcategoryId) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final result = await _apiService.getProducts(categoryId, subcategoryId);
      
      if (result['status'] == 'success') {
        _products = List<Map<String, dynamic>>.from(result['data'] ?? []);
        print('HttpApiProvider: Loaded ${_products.length} products for subcategory $subcategoryId');
      } else {
        _setError(result['message']);
      }
    } catch (e) {
      _setError('Error loading products: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Cart methods
  Future<void> loadCart() async {
    try {
      final result = await _apiService.getCart();
      
      if (result['status'] == 'success') {
        _cart = result['data'];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading cart: $e');
    }
  }

  Future<Map<String, dynamic>> addToCart(String productId, int quantity, double price) async {
    try {
      final result = await _apiService.addToCart(productId, quantity, price);
      
      if (result['status'] == 'success') {
        await loadCart(); // Refresh cart data
      }
      
      return result;
    } catch (e) {
      return {'status': 'error', 'message': 'Error adding to cart: $e'};
    }
  }

  Future<Map<String, dynamic>> clearCart() async {
    try {
      final result = await _apiService.clearCart();
      
      if (result['status'] == 'success') {
        _cart = null;
        notifyListeners();
      }
      
      return result;
    } catch (e) {
      return {'status': 'error', 'message': 'Error clearing cart: $e'};
    }
  }

  // Booking methods
  Future<void> loadBookings() async {
    try {
      final result = await _apiService.getBookings();
      
      if (result['status'] == 'success') {
        _bookings = List<Map<String, dynamic>>.from(result['data'] ?? []);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading bookings: $e');
    }
  }

  // Profile methods
  Future<void> loadProfile() async {
    try {
      final result = await _apiService.getProfile();
      
      if (result['status'] == 'success') {
        _profile = result['data'];
        notifyListeners();
      }
    } catch (e) {
      print('Error loading profile: $e');
    }
  }

  // Address methods
  Future<void> loadAddresses() async {
    try {
      final result = await _apiService.getAddresses();
      
      if (result['status'] == 'success') {
        _addresses = List<Map<String, dynamic>>.from(result['data'] ?? []);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading addresses: $e');
    }
  }

  // Coupon methods
  Future<void> loadCoupons() async {
    try {
      final result = await _apiService.getCoupons();
      
      if (result['status'] == 'success') {
        _coupons = List<Map<String, dynamic>>.from(result['data'] ?? []);
        notifyListeners();
      }
    } catch (e) {
      print('Error loading coupons: $e');
    }
  }

  // Force refresh all data
  Future<void> refreshAllData() async {
    await loadCategories();
    if (_apiService.getCurrentUserId() != null) {
      await loadCart();
      await loadBookings();
      await loadProfile();
      await loadAddresses();
      await loadCoupons();
    }
  }
}
