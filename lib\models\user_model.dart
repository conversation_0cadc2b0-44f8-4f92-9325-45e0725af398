class User {
  final String? id;
  final String? mobile;
  final String userType;
  final String? firstName;
  final String? lastName;
  final String? email;
  final bool isActive;

  User({
    this.id,
    this.mobile,
    required this.userType,
    this.firstName,
    this.lastName,
    this.email,
    this.isActive = true,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      mobile: json['mobile'],
      userType: json['user_type'] ?? 'CUSTOMER', // Default to CUSTOMER
      firstName: json['first_name'],
      lastName: json['last_name'],
      email: json['email'],
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mobile': mobile,
      'user_type': userType,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'is_active': isActive,
    };
  }

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();

  @override
  String toString() {
    return 'User(id: $id, mobile: $mobile, userType: $userType, name: $fullName)';
  }
}
