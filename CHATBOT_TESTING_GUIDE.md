# 🤖 DodoBooker AI Chatbot Testing Guide

## Overview
The DodoBooker chat support system now includes intelligent AI bots that can:
- **Book services instantly** through natural conversation
- **Check booking status** and provide updates
- **Cancel bookings** automatically
- **Handle payments** and billing questions
- **Provide help** and guidance
- **Understand natural language** for better user experience

## 🚀 Quick Start

### 1. Start the Server
```bash
npm start
```

### 2. Authenticate
```bash
POST /api/auth/customer/otp/send/
POST /api/auth/customer/otp/verify/
```

### 3. Start Chatting
```bash
POST /api/chat/messages/
{
  "message": "Hello, I need to book a cleaning service",
  "type": "text"
}
```

## 🤖 Available Bots

### 1. **BookingBot 🤖**
- **Purpose**: Handles service bookings
- **Triggers**: "book", "schedule", "service", "cleaning", "plumbing", etc.
- **Actions**: Creates actual bookings in the database

### 2. **StatusBot 📊**
- **Purpose**: Checks booking status
- **Triggers**: "status", "track", "where", "progress", etc.
- **Actions**: Shows real booking information

### 3. **CancelBot ❌**
- **Purpose**: Cancels bookings
- **Triggers**: "cancel", "stop", "remove", "delete", etc.
- **Actions**: Actually cancels bookings in database

### 4. **PaymentBot 💳**
- **Purpose**: Payment assistance
- **Triggers**: "payment", "bill", "cost", "refund", etc.
- **Actions**: Shows payment information and options

### 5. **WelcomeBot 👋**
- **Purpose**: Greets users and provides guidance
- **Triggers**: "hello", "hi", "good morning", etc.
- **Actions**: Welcomes and shows available options

### 6. **HelpBot 🆘**
- **Purpose**: Provides help and instructions
- **Triggers**: "help", "support", "assist", etc.
- **Actions**: Shows bot capabilities and commands

## 📋 Test Scenarios

### Scenario 1: Book a Cleaning Service
```json
1. Send: "Hello"
   → WelcomeBot responds with greeting and options

2. Send: "I need to book a cleaning service"
   → BookingBot offers cleaning service with price

3. Send: "Yes, book it"
   → BookingBot creates booking and shows confirmation
   → Booking appears in database and "My Bookings"
```

### Scenario 2: Check Booking Status
```json
1. Send: "Check my booking status"
   → StatusBot shows latest booking details

2. Send: "Where is my service provider?"
   → StatusBot provides tracking information
```

### Scenario 3: Cancel a Booking
```json
1. Send: "I want to cancel my booking"
   → CancelBot cancels latest pending booking
   → Booking status changes to "Cancelled" in database
```

### Scenario 4: Payment Inquiry
```json
1. Send: "What about payment?"
   → PaymentBot shows pending payments and options

2. Send: "How much do I owe?"
   → PaymentBot calculates total pending amount
```

### Scenario 5: Get Help
```json
1. Send: "Help"
   → HelpBot shows all available commands and services

2. Send: "What can you do?"
   → HelpBot explains bot capabilities
```

## 🔧 Testing Commands

### **Booking Commands**
- "Book cleaning service"
- "Schedule plumbing"
- "I need electrical work"
- "Book carpenter"
- "Fix my appliance"
- "Yes, book it" (after offer)

### **Status Commands**
- "Check status"
- "Where is my booking?"
- "Track my service"
- "Booking progress"
- "When will they arrive?"

### **Cancel Commands**
- "Cancel booking"
- "Cancel my service"
- "Stop my booking"
- "Remove booking"

### **Payment Commands**
- "Payment info"
- "Check bill"
- "How much?"
- "Payment methods"
- "Refund status"

### **Help Commands**
- "Help"
- "What can you do?"
- "Support"
- "Assist me"

### **Greeting Commands**
- "Hello"
- "Hi"
- "Good morning"
- "Hey there"

## 🎯 Expected Bot Behaviors

### ✅ **Successful Booking Flow**
1. User asks to book service
2. Bot detects service type and offers booking
3. User confirms
4. Bot creates actual booking in database
5. Booking appears in "My Bookings" section
6. Bot provides booking confirmation with ID

### ✅ **Status Check Flow**
1. User asks for status
2. Bot retrieves real booking data
3. Bot shows current status with details
4. Bot provides helpful status-specific messages

### ✅ **Cancellation Flow**
1. User requests cancellation
2. Bot finds cancellable bookings
3. Bot cancels the booking in database
4. Bot confirms cancellation with refund info

## 🧪 API Testing Endpoints

### Get Bot Capabilities
```bash
GET /api/chat/bot/capabilities/
```
**Response**: Shows all bot intents, services, and features

### Send Test Messages
```bash
POST /api/chat/messages/
{
  "message": "Your test message here",
  "type": "text"
}
```

### Check Results
```bash
GET /api/chat/messages/        # See bot responses
GET /api/bookings/             # Verify bookings created
```

## 🔍 Verification Steps

### 1. **Verify Booking Creation**
- Send booking request to bot
- Check `/api/bookings/` endpoint
- Confirm booking appears with correct details

### 2. **Verify Status Updates**
- Create a booking
- Ask bot for status
- Confirm bot shows accurate information

### 3. **Verify Cancellation**
- Create a booking
- Ask bot to cancel
- Check booking status changed to "Cancelled"

### 4. **Verify Natural Language**
- Try variations: "book cleaning", "schedule clean", "need cleaner"
- Confirm bot understands different phrasings

## 🐛 Troubleshooting

### Bot Not Responding
- Check authentication (must be logged in)
- Verify server is running
- Check console logs for errors

### Booking Not Created
- Check user authentication
- Verify database state
- Look for error messages in logs

### Wrong Intent Detected
- Try more specific keywords
- Check intent patterns in code
- Use exact examples from guide

## 📊 Console Monitoring

The server logs show:
- **Intent detection**: What the bot understood
- **Bot responses**: Which bot responded
- **Database changes**: Bookings created/cancelled
- **User interactions**: All chat messages

## 🎉 Success Indicators

✅ **Bot creates real bookings** that appear in app  
✅ **Natural language understanding** works  
✅ **Multiple bots** handle different intents  
✅ **Database integration** is seamless  
✅ **Real-time responses** with 2-5 second delay  
✅ **Contextual responses** based on user data  

Happy Testing! 🚀
