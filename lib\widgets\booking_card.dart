import 'package:flutter/material.dart';
import '../utils/app_utils.dart';

/// A reusable booking card widget
class BookingCard extends StatelessWidget {
  final Map<String, dynamic> booking;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;
  final bool showCancelButton;

  const BookingCard({
    Key? key,
    required this.booking,
    this.onTap,
    this.onCancel,
    this.showCancelButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Extract booking details
    final id = booking['id'] ?? '';
    final service = booking['service'] ?? 'Unknown Service';
    final category = booking['category'] ?? 'Unknown Category';
    final date = booking['date'] is DateTime
        ? booking['date'] as DateTime
        : DateTime.now();
    final time = booking['time'] ?? '12:00 PM';
    final address = booking['address'] ?? 'No address provided';
    final price = booking['price'] is num ? booking['price'] as num : 0;
    final status = booking['status'] ?? 'Pending';
    final iconData = booking['icon'] as IconData? ?? Icons.home_repair_service;
    final color = booking['color'] as Color? ?? Colors.grey;

    // Determine if the booking can be cancelled
    final canCancel = status.toLowerCase() == 'pending' && showCancelButton;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Booking header with service and status
              Row(
                children: [
                  // Service icon
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      iconData,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Service details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          service,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          category,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Status badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppUtils.getStatusColor(status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          AppUtils.getStatusIcon(status),
                          size: 14,
                          color: AppUtils.getStatusColor(status),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          status,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppUtils.getStatusColor(status),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Booking details
              _buildDetailRow(
                Icons.calendar_today,
                'Date',
                AppUtils.formatDate(date),
              ),
              const SizedBox(height: 8),
              _buildDetailRow(
                Icons.access_time,
                'Time',
                AppUtils.formatTime(time),
              ),
              const SizedBox(height: 8),
              _buildDetailRow(
                Icons.location_on,
                'Address',
                address,
                maxLines: 2,
              ),
              const SizedBox(height: 8),
              _buildDetailRow(
                Icons.payment,
                'Amount',
                AppUtils.formatPrice(price),
              ),
              const SizedBox(height: 16),
              // Booking ID and actions
              Row(
                children: [
                  // Booking ID
                  Expanded(
                    child: Text(
                      'Booking ID: $id',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                  // Cancel button (if applicable)
                  if (canCancel)
                    TextButton.icon(
                      onPressed: onCancel,
                      icon: const Icon(
                        Icons.cancel_outlined,
                        size: 16,
                        color: Colors.red,
                      ),
                      label: const Text(
                        'Cancel',
                        style: TextStyle(
                          color: Colors.red,
                        ),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: const BorderSide(color: Colors.red, width: 1),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to build a detail row
  Widget _buildDetailRow(
    IconData icon,
    String label,
    String value, {
    int maxLines = 1,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
