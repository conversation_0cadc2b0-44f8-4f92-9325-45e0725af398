import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../widgets/neumorphic_container.dart';
import '../models/service_category.dart';
import '../models/home_banner.dart';
import '../screens/service_list_screen.dart';
import '../providers/cart_provider.dart';
import 'cart_screen.dart';
import 'map_location_screen.dart';

class ServiceCategoriesScreen extends StatefulWidget {
  const ServiceCategoriesScreen({super.key});

  @override
  State<ServiceCategoriesScreen> createState() =>
      _ServiceCategoriesScreenState();
}

class _ServiceCategoriesScreenState extends State<ServiceCategoriesScreen> {
  String _currentAddress = 'Fetching location...';
  bool _isLoadingLocation = true;
  final PageController _bannerController = PageController();
  int _currentBannerIndex = 0;

  final List<HomeBanner> _banners = [
    HomeBanner(
      id: '1',
      title: 'Professional Electrical Services',
      subtitle: 'Reliable and safe solutions',
      imageUrl:
          'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-**********.jpg',
      actionText: 'Explore',
      targetCategoryId: '3',
    ),
    HomeBanner(
      id: '2',
      title: 'Home Cleaning Special',
      subtitle: 'Get 15% off on your first booking',
      imageUrl:
          'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-**********.jpg',
      actionText: 'Clean Now',
      targetCategoryId: '1',
    ),
    HomeBanner(
      id: '3',
      title: 'AC Service & Repair',
      subtitle: 'Beat the heat with our expert services',
      imageUrl:
          'https://img.freepik.com/free-photo/technician-service-removing-air-filter-air-conditioner-cleaning_35076-3617.jpg',
      actionText: 'Book Service',
      targetCategoryId: '7',
    ),
    HomeBanner(
      id: '4',
      title: 'Professional Plumbing Solutions',
      subtitle: 'Fix leaks and plumbing issues',
      imageUrl:
          'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-**********.jpg',
      actionText: 'Call Now',
      targetCategoryId: '2',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();

    // Auto-scroll banners
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        _startBannerAutoScroll();
      }
    });
  }

  @override
  void dispose() {
    _bannerController.dispose();
    super.dispose();
  }

  void _startBannerAutoScroll() {
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        if (_currentBannerIndex < _banners.length - 1) {
          _currentBannerIndex++;
        } else {
          _currentBannerIndex = 0;
        }

        _bannerController.animateToPage(
          _currentBannerIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );

        _startBannerAutoScroll();
      }
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _currentAddress = 'Location permissions denied';
            _isLoadingLocation = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _currentAddress = 'Location permissions permanently denied';
          _isLoadingLocation = false;
        });
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        setState(() {
          _currentAddress = _formatAddress(place);
          _isLoadingLocation = false;
        });
      } else {
        setState(() {
          _currentAddress = 'Unknown location';
          _isLoadingLocation = false;
        });
      }
    } catch (e) {
      setState(() {
        _currentAddress = 'Error getting location';
        _isLoadingLocation = false;
      });
    }
  }

  String _formatAddress(Placemark place) {
    List<String> addressParts = [];

    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    if (addressParts.isEmpty &&
        place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }

    return addressParts.join(', ');
  }

  void _openLocationPicker() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MapLocationScreen(),
      ),
    );

    if (result != null && result['address'] != null) {
      setState(() {
        _currentAddress = result['address'];
      });
    }
  }

  final List<ServiceCategory> _categories = [
    ServiceCategory(
      id: '1',
      name: 'Cleaning',
      icon: Icons.cleaning_services,
      color: const Color(0xFF4ECDC4),
      imageUrl:
          'https://img.freepik.com/free-photo/woman-cleaning-floor-with-mop_23-**********.jpg',
    ),
    ServiceCategory(
      id: '2',
      name: 'Plumbing',
      icon: Icons.plumbing,
      color: const Color(0xFF6C63FF),
      imageUrl:
          'https://img.freepik.com/free-photo/plumber-fixing-sink-pipe_23-**********.jpg',
    ),
    ServiceCategory(
      id: '3',
      name: 'Electrical',
      icon: Icons.electrical_services,
      color: const Color(0xFFFF6B6B),
      imageUrl:
          'https://img.freepik.com/free-photo/electrician-work-with-electric-installation_23-**********.jpg',
    ),
    ServiceCategory(
      id: '4',
      name: 'Painting',
      icon: Icons.format_paint,
      color: const Color(0xFFFFD166),
      imageUrl:
          'https://img.freepik.com/free-photo/painter-painting-wall-with-roller_23-2147894089.jpg',
    ),
    ServiceCategory(
      id: '5',
      name: 'Carpenter',
      icon: Icons.handyman,
      color: const Color(0xFF8D6E63),
      imageUrl:
          'https://img.freepik.com/free-photo/carpenter-working-with-equipment-wooden-table-workshop_176474-7920.jpg',
    ),
    ServiceCategory(
      id: '6',
      name: 'Appliance Repair',
      icon: Icons.home_repair_service,
      color: const Color(0xFF118AB2),
      imageUrl:
          'https://img.freepik.com/free-photo/technician-repairing-refrigerator_23-2149176085.jpg',
    ),
    ServiceCategory(
      id: '7',
      name: 'AC Service',
      icon: Icons.ac_unit,
      color: const Color(0xFF64B5F6),
      imageUrl:
          'https://img.freepik.com/free-photo/technician-service-removing-air-filter-air-conditioner-cleaning_35076-3617.jpg',
    ),
    ServiceCategory(
      id: '8',
      name: 'Pest Control',
      icon: Icons.pest_control,
      color: const Color(0xFFEF476F),
      imageUrl:
          'https://img.freepik.com/free-photo/pest-control-worker-spraying-pesticide_23-2149326894.jpg',
    ),
    ServiceCategory(
      id: '9',
      name: 'Car Wash',
      icon: Icons.car_repair,
      color: const Color(0xFF26A69A),
      imageUrl:
          'https://img.freepik.com/free-photo/man-washing-his-car-garage_1157-26072.jpg',
    ),
    ServiceCategory(
      id: '10',
      name: 'Commercial Cleaning',
      icon: Icons.business,
      color: const Color(0xFF5C6BC0),
      imageUrl:
          'https://img.freepik.com/free-photo/cleaning-service-office_23-2149374132.jpg',
    ),
    ServiceCategory(
      id: '11',
      name: 'Packers & Movers',
      icon: Icons.local_shipping,
      color: const Color(0xFF073B4C),
      imageUrl:
          'https://img.freepik.com/free-photo/delivery-man-carrying-boxes_23-**********.jpg',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF0F5F9),
        elevation: 0,
        title: GestureDetector(
          onTap: _openLocationPicker,
          child: Row(
            children: [
              const Icon(
                Icons.location_on,
                color: Color(0xFF2D4059),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _currentAddress,
                  style: const TextStyle(
                    color: Color(0xFF2D4059),
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (_isLoadingLocation)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF2D4059)),
                  ),
                ),
            ],
          ),
        ),
        actions: [
          Consumer<CartProvider>(
            builder: (ctx, cart, child) => Stack(
              alignment: Alignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.shopping_cart_outlined,
                      color: Color(0xFF2D4059)),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const CartScreen(),
                      ),
                    );
                  },
                ),
                if (cart.itemCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '${cart.itemCount}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'What service do you need?',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D4059),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Choose from our professional services',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7D8CA3),
                  ),
                ),
                const SizedBox(height: 16),

                // Banner Carousel
                SizedBox(
                  height: 180,
                  child: PageView.builder(
                    controller: _bannerController,
                    itemCount: _banners.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentBannerIndex = index;
                      });
                    },
                    itemBuilder: (context, index) {
                      return _buildBannerItem(_banners[index]);
                    },
                  ),
                ),

                // Banner Indicators
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _banners.length,
                      (index) => Container(
                        width: 8,
                        height: 8,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentBannerIndex == index
                              ? Colors.black
                              : Colors.grey.shade300,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),
                const Text(
                  'Our Services',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D4059),
                  ),
                ),
                const SizedBox(height: 12),

                // Service Categories Grid - Using shrinkWrap and NeverScrollableScrollPhysics
                // to make it work within SingleChildScrollView
                GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 0.75,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  shrinkWrap: true, // Important for SingleChildScrollView
                  physics: const NeverScrollableScrollPhysics(), // Disable grid scrolling
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    return _buildCategoryItem(category);
                  },
                ),

                // Add some bottom padding
                const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBannerItem(HomeBanner banner) {
    return GestureDetector(
      onTap: () {
        // Find the category by ID and navigate to it
        if (banner.targetCategoryId != null) {
          final category = _categories.firstWhere(
            (cat) => cat.id == banner.targetCategoryId,
            orElse: () => _categories.first,
          );

          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ServiceListScreen(category: category),
            ),
          );
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Banner Image
              Positioned.fill(
                child: Image.network(
                  banner.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: Icon(Icons.image_not_supported,
                            size: 50, color: Colors.grey),
                      ),
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: Colors.grey[200],
                      child: Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Gradient overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(179),
                      ],
                      stops: const [0.5, 1.0],
                    ),
                  ),
                ),
              ),

              // Text content
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      banner.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      banner.subtitle,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        banner.actionText,
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryItem(ServiceCategory category) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                ServiceListScreen(category: category),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOutCubic;
              var tween = Tween(begin: begin, end: end).chain(
                CurveTween(curve: curve),
              );
              var offsetAnimation = animation.drive(tween);
              return SlideTransition(position: offsetAnimation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      },
      child: NeumorphicContainer(
        color: const Color(0xFFF0F5F9),
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0xCCFFFFFF), // White with 80% opacity
                      offset: Offset(-2, -2),
                      blurRadius: 5,
                    ),
                    BoxShadow(
                      color: Color(0x66A3B1C6), // 0xFFA3B1C6 with 40% opacity
                      offset: Offset(2, 2),
                      blurRadius: 5,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Image
                      if (category.imageUrl != null)
                        Image.network(
                          category.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildFallbackIcon(category);
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return _buildFallbackIcon(category);
                          },
                        )
                      else
                        _buildFallbackIcon(category),

                      // Gradient overlay
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              category.color.withAlpha(180),
                            ],
                            stops: const [0.6, 1.0],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D4059),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFallbackIcon(ServiceCategory category) {
    return Container(
      color: category.color.withAlpha(50),
      child: Center(
        child: Icon(
          category.icon,
          size: 30,
          color: category.color,
        ),
      ),
    );
  }
}
