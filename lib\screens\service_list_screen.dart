import 'package:flutter/material.dart';
import '../models/service_category.dart';
import '../models/service.dart';
import '../widgets/neumorphic_container.dart';
import '../screens/service_detail_screen.dart';

class ServiceListScreen extends StatefulWidget {
  final ServiceCategory category;

  const ServiceListScreen({
    super.key,
    required this.category,
  });

  @override
  State<ServiceListScreen> createState() => _ServiceListScreenState();
}

class _ServiceListScreenState extends State<ServiceListScreen> {
  late List<Service> _services;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Mock data
    _services = [
      Service(
        id: '1',
        categoryId: widget.category.id,
        name: 'Standard ${widget.category.name}',
        description:
            'Professional ${widget.category.name.toLowerCase()} service for your home.',
        price: 49.99,
        rating: 4.8,
        reviewCount: 156,
        imageUrl: 'assets/images/service1.jpg',
        durationMinutes: 120,
      ),
      Service(
        id: '2',
        categoryId: widget.category.id,
        name: 'Deep ${widget.category.name}',
        description:
            'Thorough ${widget.category.name.toLowerCase()} service with attention to detail.',
        price: 79.99,
        rating: 4.9,
        reviewCount: 203,
        imageUrl: 'assets/images/service2.jpg',
        durationMinutes: 180,
      ),
      Service(
        id: '3',
        categoryId: widget.category.id,
        name: 'Express ${widget.category.name}',
        description:
            'Quick ${widget.category.name.toLowerCase()} service for urgent needs.',
        price: 39.99,
        rating: 4.6,
        reviewCount: 98,
        imageUrl: 'assets/images/service3.jpg',
        durationMinutes: 90,
      ),
      Service(
        id: '4',
        categoryId: widget.category.id,
        name: 'Premium ${widget.category.name}',
        description:
            'Luxury ${widget.category.name.toLowerCase()} service with premium products.',
        price: 99.99,
        rating: 4.9,
        reviewCount: 127,
        imageUrl: 'assets/images/service4.jpg',
        durationMinutes: 240,
      ),
    ];

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: Text(
          '${widget.category.name} Services',
          style: const TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.black),
            onPressed: () {},
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: widget.category.color.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          widget.category.icon,
                          size: 24,
                          color: widget.category.color,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Available ${widget.category.name} Services',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Choose from our ${_services.length} professional services',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF7D8CA3),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _services.length,
                      itemBuilder: (context, index) {
                        final service = _services[index];
                        return _buildServiceItem(service);
                      },
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildServiceItem(Service service) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).push(
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  ServiceDetailScreen(
                      service: service, category: widget.category),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeInOutCubic;
                var tween = Tween(begin: begin, end: end).chain(
                  CurveTween(curve: curve),
                );
                var offsetAnimation = animation.drive(tween);
                return SlideTransition(position: offsetAnimation, child: child);
              },
              transitionDuration: const Duration(milliseconds: 500),
            ),
          );
        },
        child: NeumorphicContainer(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: widget.category.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  widget.category.icon,
                  size: 40,
                  color: widget.category.color,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D4059),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${service.durationMinutes ~/ 60}h ${service.durationMinutes % 60}min',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF7D8CA3),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16,
                          color: widget.category.color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${service.rating} (${service.reviewCount})',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF2D4059),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '₹${service.price.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: widget.category.color,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: widget.category.color,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      'Book',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
