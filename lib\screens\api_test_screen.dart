import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/real_time_api_provider.dart';

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({Key? key}) : super(key: key);

  @override
  _ApiTestScreenState createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedCategoryId;
  String? _selectedSubcategoryId;
  String? _selectedProductId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // Initialize data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  // Initialize data by calling API methods
  Future<void> _initializeData() async {
    final apiProvider =
        Provider.of<RealTimeApiProvider>(context, listen: false);

    // Set a default user for testing
    await apiProvider.verifyOtp('**********', '123456');

    // Fetch categories to ensure they're loaded
    await apiProvider.getCategories();

    // Refresh the UI
    setState(() {});
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Test'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Categories'),
            Tab(text: 'Subcategories'),
            Tab(text: 'Products'),
            Tab(text: 'Cart'),
            Tab(text: 'Profile'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoriesTab(),
          _buildSubcategoriesTab(),
          _buildProductsTab(),
          _buildCartTab(),
          _buildProfileTab(),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    final apiProvider = Provider.of<RealTimeApiProvider>(context);

    return StreamBuilder<List<Map<String, dynamic>>>(
      stream: apiProvider.categoriesStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('No categories found'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    _initializeData();
                  },
                  child: const Text('Refresh Data'),
                ),
              ],
            ),
          );
        }

        final categories = snapshot.data!;

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: Icon(
                  IconData(category['icon'], fontFamily: 'MaterialIcons'),
                  color: Color(category['color']),
                ),
                title: Text(category['name']),
                subtitle: Text('ID: ${category['id']}'),
                selected: _selectedCategoryId == category['id'],
                onTap: () {
                  setState(() {
                    _selectedCategoryId = category['id'];
                    _tabController.animateTo(1); // Switch to subcategories tab
                  });
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSubcategoriesTab() {
    final apiProvider = Provider.of<RealTimeApiProvider>(context);

    if (_selectedCategoryId == null) {
      return const Center(
        child: Text('Please select a category first'),
      );
    }

    return StreamBuilder<List<Map<String, dynamic>>>(
      stream: apiProvider.subcategoriesStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No subcategories found'));
        }

        final allSubcategories = snapshot.data!;
        final subcategories = allSubcategories
            .where((subcat) => subcat['parentId'] == _selectedCategoryId)
            .toList();

        if (subcategories.isEmpty) {
          return Center(
            child: Text(
                'No subcategories found for category $_selectedCategoryId'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: subcategories.length,
          itemBuilder: (context, index) {
            final subcategory = subcategories[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: Icon(
                  IconData(subcategory['icon'], fontFamily: 'MaterialIcons'),
                  color: Color(subcategory['color']),
                ),
                title: Text(subcategory['name']),
                subtitle: Text('ID: ${subcategory['id']}'),
                selected: _selectedSubcategoryId == subcategory['id'],
                onTap: () {
                  setState(() {
                    _selectedSubcategoryId = subcategory['id'];
                    _tabController.animateTo(2); // Switch to products tab
                  });
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildProductsTab() {
    final apiProvider = Provider.of<RealTimeApiProvider>(context);

    if (_selectedSubcategoryId == null && _selectedCategoryId == null) {
      return const Center(
        child: Text('Please select a category or subcategory first'),
      );
    }

    final categoryId = _selectedSubcategoryId ?? _selectedCategoryId!;

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: apiProvider.getProductsByCategory(categoryId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Text(
                'No products found for ${_selectedSubcategoryId != null ? 'subcategory' : 'category'} $categoryId'),
          );
        }

        final products = snapshot.data!;

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product image
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            product['imageUrl'] ?? '',
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 80,
                                height: 80,
                                color: Colors.grey[200],
                                child: const Icon(Icons.image_not_supported,
                                    color: Colors.grey),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Product details
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product['name'] ?? 'No name',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '₹${product['price'] ?? 0}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              if (product['rating'] != null) ...[
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    const Icon(Icons.star,
                                        size: 16, color: Colors.amber),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${product['rating']} (${product['reviewCount'] ?? 0} reviews)',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _selectedProductId = product['id'];
                        });
                        _addToCart(context, product);
                      },
                      child: const Text('Add to Cart'),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCartTab() {
    final apiProvider = Provider.of<RealTimeApiProvider>(context);

    return StreamBuilder<Map<String, dynamic>>(
      stream: apiProvider.cartStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData ||
            snapshot.data == null ||
            snapshot.data!['items'] == null ||
            (snapshot.data!['items'] as List).isEmpty) {
          return const Center(
            child: Text('Your cart is empty'),
          );
        }

        final cart = snapshot.data!;
        final items = List<Map<String, dynamic>>.from(cart['items']);

        return Column(
          children: [
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.network(
                          item['imageUrl'] ?? '',
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 50,
                              height: 50,
                              color: Colors.grey[200],
                              child: const Icon(Icons.image_not_supported,
                                  size: 24, color: Colors.grey),
                            );
                          },
                        ),
                      ),
                      title: Text(item['name'] ?? 'No name'),
                      subtitle: Text('₹${item['price']} x ${item['quantity']}'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () {
                          apiProvider.removeFromCart(item['productId']);
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Subtotal:'),
                      Text('₹${cart['subtotal'] ?? 0}'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Tax:'),
                      Text('₹${cart['tax'] ?? 0}'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Total:'),
                      Text(
                        '₹${cart['total'] ?? 0}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Checkout functionality not implemented in test mode'),
                          ),
                        );
                      },
                      child: const Text('CHECKOUT'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildProfileTab() {
    final apiProvider = Provider.of<RealTimeApiProvider>(context);

    return StreamBuilder<Map<String, dynamic>>(
      stream: apiProvider.profileStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData ||
            snapshot.data == null ||
            snapshot.data!.isEmpty) {
          return const Center(
            child: Text('Profile data not available. Please log in.'),
          );
        }

        final profile = snapshot.data!;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile header
              Center(
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 50,
                      backgroundImage:
                          NetworkImage(profile['profilePic'] ?? ''),
                      onBackgroundImageError: (_, __) {},
                      child: profile['profilePic'] == null
                          ? const Icon(Icons.person, size: 50)
                          : null,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      profile['name'] ?? 'No name',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      profile['email'] ??
                          profile['mobile'] ??
                          'No contact info',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              const Text(
                'Account Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildProfileInfoItem(
                  Icons.phone, 'Mobile', profile['mobile'] ?? 'Not provided'),
              _buildProfileInfoItem(
                  Icons.email, 'Email', profile['email'] ?? 'Not provided'),
              _buildProfileInfoItem(Icons.calendar_today, 'Member Since',
                  _formatDate(profile['createdAt'])),
              _buildProfileInfoItem(Icons.access_time, 'Last Login',
                  _formatDate(profile['lastLogin'])),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    apiProvider.logout();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  child: const Text('LOGOUT'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProfileInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'Not available';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  void _addToCart(BuildContext context, Map<String, dynamic> product) {
    final apiProvider =
        Provider.of<RealTimeApiProvider>(context, listen: false);

    apiProvider.addToCart(product, 1).then((success) {
      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${product['name']} added to cart'),
            action: SnackBarAction(
              label: 'VIEW CART',
              onPressed: () {
                _tabController.animateTo(3); // Switch to cart tab
              },
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add to cart: ${apiProvider.error}'),
          ),
        );
      }
    });
  }
}
