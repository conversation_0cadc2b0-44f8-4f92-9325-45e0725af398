import 'package:flutter/material.dart';
import '../widgets/neumorphic_container.dart';
import '../models/service.dart';
import '../models/service_category.dart';
import '../models/order.dart';
import '../models/cart_item.dart';
import '../models/service_addon.dart';
import 'order_details_screen.dart';

class BookingsScreen extends StatefulWidget {
  const BookingsScreen({super.key});

  @override
  State<BookingsScreen> createState() => _BookingsScreenState();
}

class _BookingsScreenState extends State<BookingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Sample data for bookings
  final List<Map<String, dynamic>> _upcomingBookings = [
    {
      'id': 'ORD-**********',
      'service': 'Standard Cleaning',
      'category': 'Cleaning',
      'date': DateTime.now().add(const Duration(days: 2)),
      'time': '10:00 AM',
      'address': '123 Main Street, Apt 4B, New York, NY 10001',
      'price': 49.99,
      'status': 'Confirmed',
      'icon': Icons.cleaning_services,
      'color': const Color(0xFF4ECDC4),
    },
    {
      'id': 'ORD-**********',
      'service': 'Pipe Repair',
      'category': 'Plumbing',
      'date': DateTime.now().add(const Duration(days: 5)),
      'time': '2:00 PM',
      'address': '123 Main Street, Apt 4B, New York, NY 10001',
      'price': 89.99,
      'status': 'Pending',
      'icon': Icons.plumbing,
      'color': const Color(0xFF6C63FF),
    },
  ];

  final List<Map<String, dynamic>> _pastBookings = [
    {
      'id': 'ORD-**********',
      'service': 'Deep Cleaning',
      'category': 'Cleaning',
      'date': DateTime.now().subtract(const Duration(days: 7)),
      'time': '9:00 AM',
      'address': '123 Main Street, Apt 4B, New York, NY 10001',
      'price': 79.99,
      'status': 'Completed',
      'icon': Icons.cleaning_services,
      'color': const Color(0xFF4ECDC4),
    },
    {
      'id': 'ORD-4567890123',
      'service': 'Electrical Wiring',
      'category': 'Electrical',
      'date': DateTime.now().subtract(const Duration(days: 14)),
      'time': '11:00 AM',
      'address': '123 Main Street, Apt 4B, New York, NY 10001',
      'price': 99.99,
      'status': 'Completed',
      'icon': Icons.electrical_services,
      'color': const Color(0xFFFF6B6B),
    },
    {
      'id': 'ORD-5678901234',
      'service': 'Standard Cleaning',
      'category': 'Cleaning',
      'date': DateTime.now().subtract(const Duration(days: 21)),
      'time': '3:00 PM',
      'address': '123 Main Street, Apt 4B, New York, NY 10001',
      'price': 49.99,
      'status': 'Cancelled',
      'icon': Icons.cleaning_services,
      'color': const Color(0xFF4ECDC4),
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Helper function to create colors with opacity
  Color withOpacity(Color color, double opacity) {
    return Color.fromRGBO(
      color.red,
      color.green,
      color.blue,
      opacity,
    );
  }

  // Convert booking data to Order object
  Order _convertBookingToOrder(Map<String, dynamic> booking) {
    final service = Service(
      id: 'service-${booking['id']}',
      categoryId: 'cat-${booking['category']}',
      name: booking['service'],
      description:
          'Professional ${booking['category'].toLowerCase()} service for your home.',
      price: booking['price'],
      imageUrl: 'assets/images/service1.jpg',
      rating: 4.5,
      reviewCount: 120,
      durationMinutes: 120,
    );

    final category = ServiceCategory(
      id: 'cat-${booking['category']}',
      name: booking['category'],
      icon: booking['icon'],
      color: booking['color'],
    );

    final cartItem = CartItem(
      id: 'item-${booking['id']}',
      service: service,
      category: category,
      quantity: 1,
      date: booking['date'],
      time: booking['time'],
      addons: [],
    );

    final status = booking['status'];
    OrderStatus orderStatus;
    switch (status) {
      case 'Confirmed':
        orderStatus = OrderStatus.confirmed;
        break;
      case 'Pending':
        orderStatus = OrderStatus.pending;
        break;
      case 'Completed':
        orderStatus = OrderStatus.completed;
        break;
      case 'Cancelled':
        orderStatus = OrderStatus.cancelled;
        break;
      default:
        orderStatus = OrderStatus.pending;
    }

    return Order(
      id: booking['id'],
      items: [cartItem],
      address: {
        'formattedAddress': booking['address'],
      },
      date: booking['date'],
      time: booking['time'],
      subtotal: booking['price'],
      tax: booking['price'] * 0.05,
      discount: 0,
      smallOrderFee: booking['price'] < 300 ? 150 : 0,
      total: booking['price'] +
          (booking['price'] * 0.05) +
          (booking['price'] < 300 ? 150 : 0),
      paymentMethod: 'Credit/Debit Card',
      status: orderStatus,
      createdAt: booking['date'].subtract(const Duration(days: 1)),
      technician: orderStatus == OrderStatus.confirmed ||
              orderStatus == OrderStatus.inProgress ||
              orderStatus == OrderStatus.completed
          ? 'John Doe'
          : null,
      technicianPhone: orderStatus == OrderStatus.confirmed ||
              orderStatus == OrderStatus.inProgress ||
              orderStatus == OrderStatus.completed
          ? '+919876543210'
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'My Bookings',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.black,
          labelColor: Colors.black,
          unselectedLabelColor: const Color(0xFF7D8CA3),
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Upcoming bookings tab
          _upcomingBookings.isEmpty
              ? _buildEmptyState('No upcoming bookings')
              : _buildBookingsList(_upcomingBookings),

          // Past bookings tab
          _pastBookings.isEmpty
              ? _buildEmptyState('No past bookings')
              : _buildBookingsList(_pastBookings),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.calendar_today,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingsList(List<Map<String, dynamic>> bookings) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];
        final date = booking['date'] as DateTime;
        final status = booking['status'] as String;
        final color = booking['color'] as Color;

        Color statusColor;
        switch (status) {
          case 'Confirmed':
            statusColor = Colors.green;
            break;
          case 'Pending':
            statusColor = Colors.orange;
            break;
          case 'Completed':
            statusColor = Colors.blue;
            break;
          case 'Cancelled':
            statusColor = Colors.red;
            break;
          default:
            statusColor = Colors.grey;
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: GestureDetector(
            onTap: () {
              // Convert booking to Order and navigate to OrderDetailsScreen
              final order = _convertBookingToOrder(booking);
              Navigator.of(context).push(
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      OrderDetailsScreen(order: order),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const begin = Offset(1.0, 0.0);
                    const end = Offset.zero;
                    const curve = Curves.easeInOutCubic;
                    var tween = Tween(begin: begin, end: end).chain(
                      CurveTween(curve: curve),
                    );
                    var offsetAnimation = animation.drive(tween);
                    return SlideTransition(
                        position: offsetAnimation, child: child);
                  },
                  transitionDuration: const Duration(milliseconds: 500),
                ),
              );
            },
            child: NeumorphicContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: withOpacity(color, 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          booking['icon'] as IconData,
                          color: color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              booking['service'] as String,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2D4059),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              booking['category'] as String,
                              style: TextStyle(
                                fontSize: 14,
                                color: color,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: withOpacity(statusColor, 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          status,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: statusColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 24),
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Color(0xFF7D8CA3),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${_getDayName(date.weekday)}, ${date.day} ${_getMonthName(date.month)} ${date.year} • ${booking['time']}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Color(0xFF7D8CA3),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          booking['address'] as String,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF7D8CA3),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.receipt,
                        size: 16,
                        color: Color(0xFF7D8CA3),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Order ID: ${booking['id']}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF7D8CA3),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '₹${(booking['price'] as double).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D4059),
                        ),
                      ),
                      if (status == 'Pending')
                        TextButton(
                          onPressed: () {
                            // Show cancellation dialog
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Cancel Booking'),
                                content: const Text(
                                    'Are you sure you want to cancel this booking?'),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    child: const Text('No'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      // Update booking status
                                      setState(() {
                                        booking['status'] = 'Cancelled';
                                        _pastBookings.add(booking);
                                        _upcomingBookings.remove(booking);
                                      });
                                    },
                                    child: const Text('Yes'),
                                  ),
                                ],
                              ),
                            );
                          },
                          child: const Text(
                            'Cancel',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }
}
