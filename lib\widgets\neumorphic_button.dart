import 'package:flutter/material.dart';

class NeumorphicButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? color;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final double width;
  final double height;

  const NeumorphicButton({
    super.key,
    required this.child,
    this.onPressed,
    this.color,
    this.borderRadius = 16.0,
    this.padding = const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
    this.width = double.infinity,
    this.height = 56.0,
  });

  @override
  State<NeumorphicButton> createState() => _NeumorphicButtonState();
}

class _NeumorphicButtonState extends State<NeumorphicButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.color ?? Colors.black;
    final isDisabled = widget.onPressed == null;

    // Helper function to create colors with opacity
    Color withOpacity(Color color, double opacity) {
      return Color.fromRGBO(
        color.red,
        color.green,
        color.blue,
        opacity,
      );
    }

    final disabledColor = withOpacity(backgroundColor, 0.6);
    final shadowColor1 = withOpacity(Colors.black, 0.2);
    final shadowColor2 = withOpacity(Colors.white, 0.5);

    return GestureDetector(
      onTapDown: (_) {
        if (!isDisabled) {
          setState(() {
            _isPressed = true;
          });
        }
      },
      onTapUp: (_) {
        if (!isDisabled) {
          setState(() {
            _isPressed = false;
          });
          widget.onPressed?.call();
        }
      },
      onTapCancel: () {
        if (!isDisabled) {
          setState(() {
            _isPressed = false;
          });
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        width: widget.width,
        height: widget.height,
        padding: widget.padding,
        decoration: BoxDecoration(
          color: isDisabled ? disabledColor : backgroundColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: _isPressed
              ? [
                  BoxShadow(
                    color: shadowColor1,
                    offset: const Offset(1, 1),
                    blurRadius: 2,
                    spreadRadius: 0,
                  ),
                ]
              : [
                  BoxShadow(
                    color: shadowColor1,
                    offset: const Offset(3, 3),
                    blurRadius: 5,
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: shadowColor2,
                    offset: const Offset(-1, -1),
                    blurRadius: 3,
                    spreadRadius: 0,
                  ),
                ],
        ),
        child: Center(
          child: DefaultTextStyle(
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }
}
