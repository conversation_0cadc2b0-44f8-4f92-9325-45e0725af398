import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/main_navigation_screen.dart';
import 'screens/onboarding/onboarding_screen.dart';
import 'screens/auth/google_login_screen.dart';
import 'screens/api_test_screen.dart';
import 'screens/simple_test_screen.dart';
import 'screens/test_menu_screen.dart';
import 'providers/cart_provider.dart';
import 'providers/coupon_provider.dart';
import 'providers/product_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/onboarding_provider.dart';
import 'providers/category_provider.dart';
import 'providers/supabase_provider.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // Build the home screen based on onboarding and authentication state
  Widget _buildHomeScreen(BuildContext context) {
    // For testing purposes, you can uncomment one of these lines:
    // return const TestMenuScreen();
    // return const ApiTestScreen();
    // return const SimpleTestScreen();

    // Use the normal app flow
    final authProvider = Provider.of<AuthProvider>(context);
    final onboardingProvider = Provider.of<OnboardingProvider>(context);

    // Show loading screen while initializing
    if (onboardingProvider.isLoading ||
        authProvider.status == AuthStatus.initial ||
        authProvider.status == AuthStatus.loading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show onboarding for first launch
    if (onboardingProvider.isFirstLaunch) {
      return const OnboardingScreen();
    }

    // Handle authentication state
    switch (authProvider.status) {
      case AuthStatus.authenticated:
        return const MainNavigationScreen();
      case AuthStatus.unauthenticated:
      case AuthStatus.error:
        return const GoogleLoginScreen();
      default:
        return const GoogleLoginScreen();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (ctx) {
          final provider = AuthProvider();
          // Initialize auth state
          provider.initialize();
          return provider;
        }),
        ChangeNotifierProvider(create: (ctx) {
          final provider = OnboardingProvider();
          // Initialize onboarding state
          provider.initialize();
          return provider;
        }),
        ChangeNotifierProvider(create: (ctx) => CartProvider()),
        ChangeNotifierProvider(create: (ctx) => CouponProvider()),
        ChangeNotifierProvider(create: (ctx) {
          final provider = ProductProvider();
          provider.loadProducts();
          return provider;
        }),
        ChangeNotifierProvider(create: (ctx) {
          final provider = CategoryProvider();
          // Categories will be loaded from Supabase
          return provider;
        }),
        ChangeNotifierProvider(create: (ctx) {
          final provider = SupabaseProvider();
          // Initialize Supabase when the provider is created
          provider.initialize();
          return provider;
        }),
      ],
      child: Consumer<AuthProvider>(
        builder: (ctx, authProvider, _) {
          return MaterialApp(
            title: 'DODO Booker',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primaryColor: Colors.black, // Black primary color
              scaffoldBackgroundColor: const Color(0xFFF0F5F9),
              appBarTheme: const AppBarTheme(
                backgroundColor: Colors.transparent,
                elevation: 0,
                iconTheme: IconThemeData(color: Color(0xFF2D4059)),
                titleTextStyle: TextStyle(
                  color: Color(0xFF2D4059),
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              textTheme: const TextTheme(
                bodyLarge: TextStyle(color: Color(0xFF2D4059)),
                bodyMedium: TextStyle(color: Color(0xFF7D8CA3)),
              ),
              inputDecorationTheme: InputDecorationTheme(
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.all(16),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                hintStyle: const TextStyle(color: Color(0xFFABB2BF)),
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  foregroundColor: Colors.black,
                ),
              ),
              bottomNavigationBarTheme: const BottomNavigationBarThemeData(
                backgroundColor: Colors.white,
                selectedItemColor: Colors.black,
                unselectedItemColor: Color(0xFF7D8CA3),
                type: BottomNavigationBarType.fixed,
                elevation: 8,
              ),
            ),
            home: _buildHomeScreen(ctx),
          );
        },
      ),
    );
  }
}
