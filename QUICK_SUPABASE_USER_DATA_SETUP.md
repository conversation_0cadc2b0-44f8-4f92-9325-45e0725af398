# Quick Setup: Get User Data in Supabase

## Current Situation
- ✅ **App works perfectly** with mock authentication
- ❌ **User data not in Supabase** because using mock API
- ❌ **Phone authentication disabled** in Supabase

## Quick Solution: Use Google Sign-in

### Step 1: Configure Google OAuth in Supabase

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create OAuth 2.0 Client ID**:
   - Application type: Web application
   - Authorized redirect URIs: `https://vlrdhrgahxaxnizkedmv.supabase.co/auth/v1/callback`
3. **Copy Client ID and Client Secret**

4. **Go to Supabase Dashboard**: https://supabase.com/dashboard
5. **Navigate to**: Authentication → Providers → Google
6. **Enable Google Provider**
7. **Enter your Client ID and Client Secret**
8. **Save**

### Step 2: Test Google Sign-in

1. **Run your app**: `flutter run -d chrome`
2. **Click "Continue with Google"** on login screen
3. **Complete Google OAuth flow**
4. **User data will be stored in Supabase!**

### Step 3: Verify User Data in Supabase

1. **Go to Supabase Dashboard**
2. **Navigate to**: Authentication → Users
3. **You should see your Google user**
4. **Navigate to**: Table Editor → user_profiles
5. **You should see user profile data**

## Alternative: Enable Phone Authentication

### Step 1: Configure SMS Provider

1. **Sign up for Twilio**: https://www.twilio.com/
2. **Get credentials**:
   - Account SID
   - Auth Token
   - Phone Number

### Step 2: Configure in Supabase

1. **Go to Supabase Dashboard**
2. **Navigate to**: Authentication → Providers → Phone
3. **Enable Phone Provider**
4. **Enter Twilio credentials**
5. **Save**

### Step 3: Test Phone Authentication

1. **Your app will now work** with real phone numbers
2. **Users receive real SMS OTP**
3. **User data stored in Supabase**

## Current App Settings

Your app is currently set to:
```dart
_authService.setUseSupabase(true); // Uses Supabase
```

This means:
- ✅ **Google sign-in** → Works with Supabase (stores user data)
- ❌ **Phone authentication** → Fails (needs SMS provider)

## Recommendation

**For immediate testing**: Use Google sign-in
**For production**: Configure phone authentication

## Testing Google Sign-in

1. **Configure Google OAuth** (5 minutes)
2. **Test sign-in** → User data appears in Supabase
3. **Verify in database** → Check user_profiles table

## Benefits

### Google Sign-in:
- ✅ **No SMS costs**
- ✅ **Instant setup**
- ✅ **Real user data in Supabase**
- ✅ **Production ready**

### Phone Authentication:
- ✅ **Better user experience**
- ✅ **No Google dependency**
- ❌ **Requires SMS provider setup**
- ❌ **SMS costs**

## Next Steps

1. **Choose your preferred method**:
   - Google sign-in (quick)
   - Phone authentication (complete)

2. **Configure the chosen method**

3. **Test user registration**

4. **Verify data in Supabase**

Your user data will then appear in the Supabase database!
