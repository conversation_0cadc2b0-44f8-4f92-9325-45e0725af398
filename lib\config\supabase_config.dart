class SupabaseConfig {
  // TODO: Replace these with your actual Supabase project credentials
  // You can find these in your Supabase project dashboard at https://supabase.com/dashboard

  // Your Supabase project URL (looks like: https://your-project-id.supabase.co)
  static const String supabaseUrl = 'https://vlrdhrgahxaxnizkedmv.supabase.co';

  // Your Supabase anon/public key (starts with eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...)
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZscmRocmdhaHhheG5pemtlZG12Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNDU2MDksImV4cCI6MjA2MzkyMTYwOX0.AfNn16C8u0vbpIAGMpx8gi34-k-fwKl56mBfpLRsKpA';

  // Example (replace with your actual values):
  // static const String supabaseUrl = 'https://abcdefghijklmnop.supabase.co';
  // static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFiY2RlZmdoaWprbG1ub3AiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTY5ODc2NTQwMCwiZXhwIjoyMDE0MzQxNDAwfQ.example-signature';

  // Database table names
  static const String categoriesTable = 'categories';
  static const String subcategoriesTable = 'subcategories';
  static const String productsTable = 'products';
  static const String userProfilesTable = 'user_profiles';
  static const String cartItemsTable = 'cart_items';
  static const String bookingsTable = 'bookings';
  static const String bookingItemsTable = 'booking_items';
  static const String userAddressesTable = 'user_addresses';
  static const String couponsTable = 'coupons';
}
