import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../widgets/neumorphic_container.dart';
import '../widgets/neumorphic_button.dart';
import '../providers/auth_provider.dart';
import '../providers/api_provider.dart';
import '../providers/real_time_api_provider.dart';
import 'notifications_screen.dart';
import 'chat_support_screen.dart';
import 'ai_chat_screen.dart';
import 'edit_profile_screen.dart';
import 'edit_phone_screen.dart';
import 'addresses_screen.dart';
import 'test_menu_screen.dart';
import 'debug_screen.dart';
import 'auth/google_login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String _name = 'Loading...';
  String _email = 'Loading...';
  String _phone = 'Loading...';
  String _profilePic = 'https://randomuser.me/api/portraits/men/32.jpg';
  final String _companyPhone = '+****************';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    try {
      final apiProvider =
          Provider.of<RealTimeApiProvider>(context, listen: false);
      final profile = await apiProvider.getProfile();

      if (profile != null && mounted) {
        setState(() {
          _name = profile['name'] ?? 'No name';
          _email = profile['email'] ?? 'No email';
          _phone = profile['mobile'] ?? 'No phone';
          _profilePic = profile['profilePic'] ??
              'https://randomuser.me/api/portraits/men/32.jpg';
          _isLoading = false;
        });
      } else if (mounted) {
        setState(() {
          _name = 'No name';
          _email = 'No email';
          _phone = 'No phone';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _name = 'Error loading';
          _email = 'Error loading';
          _phone = 'Error loading';
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    await launchUrl(launchUri);
  }

  void _showLogoutConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _logout();
            },
            child: const Text(
              'Logout',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _logout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final apiProvider = Provider.of<ApiProvider>(context, listen: false);
    final realTimeApiProvider =
        Provider.of<RealTimeApiProvider>(context, listen: false);

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Perform logout on all providers
      await authProvider.logout();
      await realTimeApiProvider.logout();

      // Reset API provider state
      apiProvider.setCurrentUser('guest-user');

      // Close loading indicator
      if (mounted) {
        Navigator.pop(context);

        // Navigate to login screen and clear all previous routes
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => const GoogleLoginScreen(),
          ),
          (route) => false,
        );
      }
    } catch (e) {
      // Close loading indicator
      if (mounted) {
        Navigator.pop(context);

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Logout failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'My Profile',
          style: TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildProfileHeader(),
              const SizedBox(height: 24),
              _buildAccountSettings(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Center(
      child: Column(
        children: [
          Stack(
            children: [
              NeumorphicContainer(
                width: 100,
                height: 100,
                borderRadius: 50,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50),
                  child: Image.network(
                    _profilePic,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 100,
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.person,
                          size: 50,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                right: 0,
                bottom: 0,
                child: GestureDetector(
                  onTap: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EditProfileScreen(),
                      ),
                    );

                    if (result != null) {
                      // Reload the profile data from API after editing
                      _loadProfile();
                    }
                  },
                  child: Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4ECDC4),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: const Icon(
                      Icons.edit,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            _name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _email,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF7D8CA3),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _phone,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF7D8CA3),
            ),
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EditProfileScreen(),
                ),
              );

              if (result != null) {
                // Reload the profile data from API after editing
                _loadProfile();
              }
            },
            child: NeumorphicContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.edit,
                    size: 16,
                    color: Color(0xFF4ECDC4),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Edit Profile',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF2D4059),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSettings() {
    final settings = [
      {
        'title': 'Edit Phone Number',
        'icon': Icons.arrow_forward_ios,
        'onTap': () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EditPhoneScreen(initialPhone: _phone),
            ),
          );

          if (result != null) {
            // Reload the profile data from API after editing phone
            _loadProfile();
          }
        }
      },
      {
        'title': 'Your Addresses',
        'icon': Icons.arrow_forward_ios,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddressesScreen()),
          );
        }
      },
      {
        'title': 'AI Chat Assistant',
        'icon': Icons.smart_toy,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AIChatScreen()),
          );
        }
      },
      {
        'title': 'Chat Support',
        'icon': Icons.arrow_forward_ios,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ChatSupportScreen()),
          );
        }
      },
      {
        'title': 'Call',
        'icon': Icons.arrow_forward_ios,
        'onTap': () {
          _makePhoneCall(_companyPhone);
        }
      },
      {
        'title': 'Notifications',
        'icon': Icons.arrow_forward_ios,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const NotificationsScreen()),
          );
        }
      },
      {
        'title': 'Logout',
        'icon': Icons.logout,
        'color': Colors.red,
        'onTap': () {
          _showLogoutConfirmationDialog();
        }
      },
      {
        'title': 'API Testing',
        'icon': Icons.code,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const TestMenuScreen()),
          );
        }
      },
      {
        'title': 'Debug & Refresh Data',
        'icon': Icons.refresh,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const DebugScreen()),
          );
        }
      },
      {
        'title': 'More',
        'icon': Icons.arrow_forward_ios,
        'onTap': () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('More options coming soon'),
            ),
          );
        }
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(left: 8.0, bottom: 16.0),
          child: Text(
            'Account Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D4059),
            ),
          ),
        ),
        ...settings.map((setting) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: GestureDetector(
              onTap: () => (setting['onTap'] as Function)(),
              child: NeumorphicContainer(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        setting['title'] as String,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: setting['color'] != null
                              ? setting['color'] as Color
                              : const Color(0xFF2D4059),
                        ),
                      ),
                    ),
                    Icon(
                      setting['icon'] as IconData,
                      size: 16,
                      color: setting['color'] != null
                          ? setting['color'] as Color
                          : const Color(0xFF7D8CA3),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }
}
