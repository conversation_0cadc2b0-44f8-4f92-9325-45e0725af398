# DodoBooker

A Flutter application for booking home services. This app allows users to browse different service categories, select services, schedule appointments, and manage bookings.

## Features

- User authentication (mobile number and OTP verification)
- Browse service categories
- View service details
- Schedule service appointments
- Select addresses (with map integration)
- Shopping cart functionality
- Payment processing
- Order confirmation
- Track bookings
- Manage profile

## Screenshots

### Login Screen
![Login Screen](screenshots/login_screen.png)

## App Flow

1. **Login/Signup**: Users can create an account or log in
2. **Home Screen**: Browse service categories
3. **Service List**: View services within a category
4. **Service Details**: View service information, select date, time, and quantity
5. **Address Selection**: Choose delivery address or add a new one
6. **Schedule Selection**: Select date and time for the service
7. **Payment**: Process payment for the service
8. **Confirmation**: Receive booking confirmation
9. **Bookings**: Track all bookings in one place

## Technologies Used

- Flutter for cross-platform mobile development
- Provider for state management
- Google Maps integration for address selection
- Custom UI components with Neumorphic design

## Dependencies

- Flutter
- Cupertino Icons
- Provider
- Google Maps Flutter
- Geolocator
- Geocoding
- HTTP

## Getting Started

1. Clone the repository
   ```
   git clone https://github.com/yourusername/DodoBooker.git
   ```
2. Navigate to the project directory
   ```
   cd DodoBooker
   ```
3. Install dependencies
   ```
   flutter pub get
   ```
4. Run the application
   ```
   flutter run
   ```

## Project Structure

- `lib/models/` - Data models
- `lib/providers/` - State management
- `lib/screens/` - UI screens
- `lib/widgets/` - Reusable UI components

## License

This project is licensed under the MIT License - see the LICENSE file for details.
