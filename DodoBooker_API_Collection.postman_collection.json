{"info": {"name": "DodoBooker API Collection", "description": "Complete API collection for DodoBooker app testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "mobile", "value": "9876543210", "type": "string"}, {"key": "otp", "value": "123456", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Send OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mobile\": \"{{mobile}}\",\n  \"user_type\": \"CUSTOMER\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/customer/otp/send/", "host": ["{{baseUrl}}"], "path": ["api", "auth", "customer", "otp", "send", ""]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mobile\": \"{{mobile}}\",\n  \"otp\": \"{{otp}}\",\n  \"user_type\": \"CUSTOMER\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/customer/otp/verify/", "host": ["{{baseUrl}}"], "path": ["api", "auth", "customer", "otp", "verify", ""]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/token/cookie/logout/", "host": ["{{baseUrl}}"], "path": ["api", "auth", "token", "cookie", "logout", ""]}}}]}, {"name": "Categories & Products", "item": [{"name": "Get Categories", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/categories/", "host": ["{{baseUrl}}"], "path": ["api", "categories", ""]}}}, {"name": "Get Subcategories", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/categories/1/subcategories/", "host": ["{{baseUrl}}"], "path": ["api", "categories", "1", "subcategories", ""]}}}, {"name": "Get Products", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/categories/1/subcategories/sub-1-1/products/", "host": ["{{baseUrl}}"], "path": ["api", "categories", "1", "subcategories", "sub-1-1", "products", ""]}}}]}, {"name": "Cart Management", "item": [{"name": "Get Cart", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/cart/", "host": ["{{baseUrl}}"], "path": ["api", "cart", ""]}}}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"product-1\",\n  \"quantity\": 1,\n  \"price\": 2999.95\n}"}, "url": {"raw": "{{baseUrl}}/api/cart/add/", "host": ["{{baseUrl}}"], "path": ["api", "cart", "add", ""]}}}, {"name": "Clear Cart", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/cart/clear/", "host": ["{{baseUrl}}"], "path": ["api", "cart", "clear", ""]}}}]}, {"name": "Bookings", "item": [{"name": "Create Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"service\": \"1 BHK furnished apartment cleaning\",\n  \"category\": \"Cleaning\",\n  \"price\": 2999.95,\n  \"date\": \"2025-05-27T10:00:00.000Z\",\n  \"time\": \"10:00 AM\",\n  \"address\": {\n    \"id\": 1,\n    \"type\": \"Home\",\n    \"address\": \"123 Main Street, Apt 4B, New York, NY 10001\",\n    \"isDefault\": true,\n    \"isSelected\": true\n  },\n  \"paymentMethod\": \"Cash on Delivery\",\n  \"icon\": 57703,\n  \"color\": 4280391411\n}"}, "url": {"raw": "{{baseUrl}}/api/bookings/", "host": ["{{baseUrl}}"], "path": ["api", "bookings", ""]}}}, {"name": "Get Bookings", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/bookings/", "host": ["{{baseUrl}}"], "path": ["api", "bookings", ""]}}}, {"name": "Get Booking Details", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/bookings/booking-1748239777198/", "host": ["{{baseUrl}}"], "path": ["api", "bookings", "booking-1748239777198", ""]}}}]}, {"name": "Chat Support", "item": [{"name": "Get Chat Messages", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/chat/messages/", "host": ["{{baseUrl}}"], "path": ["api", "chat", "messages", ""]}}}, {"name": "Send Chat Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Hello, I need help with my booking\",\n  \"type\": \"text\"\n}"}, "url": {"raw": "{{baseUrl}}/api/chat/messages/", "host": ["{{baseUrl}}"], "path": ["api", "chat", "messages", ""]}}}, {"name": "<PERSON> as Read", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/chat/messages/msg-123456/read/", "host": ["{{baseUrl}}"], "path": ["api", "chat", "messages", "msg-123456", "read", ""]}}}, {"name": "Get Unread Count", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/chat/unread-count/", "host": ["{{baseUrl}}"], "path": ["api", "chat", "unread-count", ""]}}}, {"name": "Clear Chat History", "request": {"method": "DELETE", "url": {"raw": "{{baseUrl}}/api/chat/messages/", "host": ["{{baseUrl}}"], "path": ["api", "chat", "messages", ""]}}}, {"name": "Get Bot Capabilities", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/chat/bot/capabilities/", "host": ["{{baseUrl}}"], "path": ["api", "chat", "bot", "capabilities", ""]}}}]}, {"name": "Support System", "item": [{"name": "Get Support Agents", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/support/agents/", "host": ["{{baseUrl}}"], "path": ["api", "support", "agents", ""]}}}, {"name": "Get FAQ", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/support/faq/", "host": ["{{baseUrl}}"], "path": ["api", "support", "faq", ""]}}}, {"name": "Create Support Ticket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subject\": \"Issue with my booking\",\n  \"description\": \"I'm having trouble with my recent booking. The service provider hasn't arrived yet.\",\n  \"category\": \"Booking\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{baseUrl}}/api/support/tickets/", "host": ["{{baseUrl}}"], "path": ["api", "support", "tickets", ""]}}}, {"name": "Get Support Tickets", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/support/tickets/", "host": ["{{baseUrl}}"], "path": ["api", "support", "tickets", ""]}}}]}, {"name": "Profile & Others", "item": [{"name": "Get Profile", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/profile/", "host": ["{{baseUrl}}"], "path": ["api", "profile", ""]}}}, {"name": "Get Addresses", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/addresses/", "host": ["{{baseUrl}}"], "path": ["api", "addresses", ""]}}}, {"name": "Get Coupons", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/coupons/", "host": ["{{baseUrl}}"], "path": ["api", "coupons", ""]}}}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}]}