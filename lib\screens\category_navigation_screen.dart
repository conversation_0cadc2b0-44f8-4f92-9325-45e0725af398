import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hierarchical_category.dart';
import '../models/cleaning_product.dart';
import '../providers/product_provider.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/app_navigation_bar.dart';
import '../widgets/cart_icon_with_badge.dart';
import 'cleaning_product_list_screen.dart';
import 'category_product_list_screen.dart';
import 'search_results_screen.dart';

class CategoryNavigationScreen extends StatefulWidget {
  final HierarchicalCategory? parentCategory;
  final List<HierarchicalCategory> categories;
  final String title;

  const CategoryNavigationScreen({
    super.key,
    this.parentCategory,
    required this.categories,
    required this.title,
  });

  @override
  State<CategoryNavigationScreen> createState() =>
      _CategoryNavigationScreenState();
}

class _CategoryNavigationScreenState extends State<CategoryNavigationScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<HierarchicalCategory> _filteredCategories = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _filteredCategories = widget.categories;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    // Normalize the query
    final normalizedQuery = query.trim().toLowerCase();

    setState(() {
      _searchQuery = normalizedQuery;
      if (normalizedQuery.isEmpty) {
        _isSearching = false;
        _filteredCategories = widget.categories;
      } else {
        _isSearching = true;
        _filteredCategories = widget.categories.where((category) {
          final categoryName = category.name.toLowerCase();

          // Check for exact matches first
          if (categoryName.contains(normalizedQuery)) {
            return true;
          }

          // Check for word matches
          final words = categoryName.split(' ');
          return words.any((word) => word.startsWith(normalizedQuery));
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F5F9),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 2,
        title: Text(
          widget.title,
          style: const TextStyle(
            color: Color(0xFF2D4059),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: const [
          CartIconWithBadge(),
        ],
      ),
      bottomNavigationBar: AppNavigationBar(
        currentSection: NavigationSection.home,
        mainContext: context,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SearchBarWidget(
                controller: _searchController,
                onChanged: _onSearchChanged,
                onSubmitted: (query) {
                  if (query.isNotEmpty) {
                    // If no local results, search globally
                    if (_filteredCategories.isEmpty) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SearchResultsScreen(
                            initialQuery: query,
                          ),
                        ),
                      );
                    }
                  }
                },
                hintText: 'Search in ${widget.title}...',
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Parent category info if available
                      if (widget.parentCategory != null)
                        _buildParentCategoryInfo(
                            context, widget.parentCategory!),

                      const SizedBox(height: 16),

                      // No results message when searching
                      if (_isSearching && _filteredCategories.isEmpty)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.search_off,
                                  size: 64,
                                  color: Colors.grey.shade300,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No categories found for "$_searchQuery"',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        )
                      else
                        // Categories grid
                        GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 0.75,
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _filteredCategories.length,
                          itemBuilder: (context, index) {
                            final category = _filteredCategories[index];
                            return _buildCategoryItem(context, category);
                          },
                        ),

                      // Bottom padding
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParentCategoryInfo(
      BuildContext context, HierarchicalCategory category) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: category.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: category.color.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              category.icon,
              color: category.color,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D4059),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Browse ${category.name} subcategories',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF7D8CA3),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(
      BuildContext context, HierarchicalCategory category) {
    return GestureDetector(
      onTap: () => _navigateToCategory(context, category),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Image
            Expanded(
              child: ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
                child: category.imageUrl != null
                    ? Image.network(
                        category.imageUrl!,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: category.color.withOpacity(0.1),
                            child: Center(
                              child: Icon(
                                category.icon,
                                color: category.color,
                                size: 32,
                              ),
                            ),
                          );
                        },
                      )
                    : Container(
                        color: category.color.withOpacity(0.1),
                        child: Center(
                          child: Icon(
                            category.icon,
                            color: category.color,
                            size: 32,
                          ),
                        ),
                      ),
              ),
            ),

            // Category name
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                category.name,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D4059),
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCategory(
      BuildContext context, HierarchicalCategory category) {
    if (category.hasSubcategories) {
      // Navigate to subcategories
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryNavigationScreen(
            parentCategory: category,
            categories: category.subcategories,
            title: '${category.name} Categories',
          ),
        ),
      );
    } else {
      // Navigate to products/services
      // Use CategoryProductListScreen for AC, Pest Control, Plumbing, Electrical, Carpenter, and Appliances categories
      if (category.id.startsWith('7-') || // AC Services
          category.id.startsWith('8-') || // Pest Control
          category.id.startsWith('2-') || // Plumbing
          category.id.startsWith('3-') || // Electrical
          category.id.startsWith('4-') || // Carpenter
          category.id.startsWith('5-')) {
        // Appliances
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CategoryProductListScreen(
              category: category,
            ),
          ),
        );
      } else {
        // Use CleaningProductListScreen for other categories
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CleaningProductListScreen(
              category: category,
            ),
          ),
        );
      }
    }
  }
}
