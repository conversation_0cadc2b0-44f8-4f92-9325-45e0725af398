import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/supabase_provider.dart';

class SupabaseDebugScreen extends StatefulWidget {
  const SupabaseDebugScreen({super.key});

  @override
  State<SupabaseDebugScreen> createState() => _SupabaseDebugScreenState();
}

class _SupabaseDebugScreenState extends State<SupabaseDebugScreen> {
  String _testResult = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Debug'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Consumer<SupabaseProvider>(
        builder: (context, supabaseProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Configuration Status
                _buildStatusCard(
                  'Configuration Status',
                  supabaseProvider.isConfigured 
                    ? 'Configured ✅' 
                    : 'Not Configured ❌',
                  supabaseProvider.isConfigured 
                    ? Colors.green 
                    : Colors.red,
                ),
                const SizedBox(height: 16),

                // Initialization Status
                _buildStatusCard(
                  'Initialization Status',
                  supabaseProvider.isInitialized 
                    ? 'Initialized ✅' 
                    : 'Not Initialized ❌',
                  supabaseProvider.isInitialized 
                    ? Colors.green 
                    : Colors.orange,
                ),
                const SizedBox(height: 16),

                // Error Display
                if (supabaseProvider.error != null)
                  _buildStatusCard(
                    'Error',
                    supabaseProvider.error!,
                    Colors.red,
                  ),
                const SizedBox(height: 16),

                // Data Status
                _buildDataStatus(supabaseProvider),
                const SizedBox(height: 32),

                // Test Buttons
                if (supabaseProvider.isConfigured) ...[
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _testConnection(supabaseProvider),
                    icon: _isLoading 
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.wifi),
                    label: Text(_isLoading ? 'Testing...' : 'Test Connection'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                  const SizedBox(height: 16),

                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _loadCategories(supabaseProvider),
                    icon: const Icon(Icons.category),
                    label: const Text('Load Categories'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                  const SizedBox(height: 16),

                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _loadProducts(supabaseProvider),
                    icon: const Icon(Icons.shopping_bag),
                    label: const Text('Load Products'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ] else ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange[200]!),
                    ),
                    child: Column(
                      children: [
                        Icon(Icons.warning, color: Colors.orange[700], size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Supabase Not Configured',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Please update your Supabase credentials in lib/config/supabase_config.dart',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: 32),

                // Test Results
                if (_testResult.isNotEmpty) ...[
                  const Text(
                    'Test Results:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      _testResult,
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(String title, String status, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            status.contains('✅') ? Icons.check_circle : Icons.error,
            color: color,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataStatus(SupabaseProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Data Status',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),
          const SizedBox(height: 8),
          Text('Categories: ${provider.categories.length}'),
          Text('Products: ${provider.products.length}'),
          Text('Cart Items: ${provider.cartItems.length}'),
          Text('Bookings: ${provider.bookings.length}'),
          Text('Coupons: ${provider.coupons.length}'),
          Text('User: ${provider.currentUser?.email ?? 'Not signed in'}'),
        ],
      ),
    );
  }

  Future<void> _testConnection(SupabaseProvider provider) async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      await provider.initialize();
      setState(() {
        _testResult = 'Connection Test: SUCCESS ✅\n'
            'Supabase client initialized successfully\n'
            'Ready to perform database operations';
      });
    } catch (e) {
      setState(() {
        _testResult = 'Connection Test: FAILED ❌\n'
            'Error: $e\n'
            'Please check your configuration and internet connection';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCategories(SupabaseProvider provider) async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      await provider.loadCategories();
      setState(() {
        _testResult = 'Load Categories: SUCCESS ✅\n'
            'Loaded ${provider.categories.length} categories\n'
            'Categories: ${provider.categories.map((c) => c['name']).join(', ')}';
      });
    } catch (e) {
      setState(() {
        _testResult = 'Load Categories: FAILED ❌\n'
            'Error: $e\n'
            'Make sure your database schema is set up correctly';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadProducts(SupabaseProvider provider) async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      await provider.loadProducts();
      setState(() {
        _testResult = 'Load Products: SUCCESS ✅\n'
            'Loaded ${provider.products.length} products\n'
            'Sample products: ${provider.products.take(3).map((p) => p['name']).join(', ')}';
      });
    } catch (e) {
      setState(() {
        _testResult = 'Load Products: FAILED ❌\n'
            'Error: $e\n'
            'Make sure your database has product data';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
