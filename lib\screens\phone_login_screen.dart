import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/neumorphic_button.dart';
import '../widgets/neumorphic_text_field.dart';

class PhoneLoginScreen extends StatefulWidget {
  const PhoneLoginScreen({Key? key}) : super(key: key);

  @override
  State<PhoneLoginScreen> createState() => _PhoneLoginScreenState();
}

class _PhoneLoginScreenState extends State<PhoneLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mobileController = TextEditingController();
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (_) => TextEditingController(),
  );
  bool _isLoading = false;
  bool _otpSent = false;
  int _resendCountdown = 0;

  @override
  void dispose() {
    _mobileController.dispose();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startResendTimer() {
    setState(() {
      _resendCountdown = 30;
    });

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
        _startResendTimer();
      }
    });
  }

  void _sendOtp() async {
    if (_formKey.currentState!.validate()) {
      print("Sending OTP - validation passed");
      setState(() {
        _isLoading = true;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      print(
          "Calling authProvider.sendOtp with mobile: ${_mobileController.text}");
      final success = await authProvider.sendOtp(_mobileController.text);
      print("OTP send result: $success");
      print("Auth status after send: ${authProvider.status}");
      print("Mock OTP: ${authProvider.mockOtp}");

      if (mounted) {
        print("Setting _otpSent to true");
        setState(() {
          _isLoading = false;
          if (success) {
            _otpSent = true;
            print("_otpSent is now: $_otpSent");
            _startResendTimer();

            // Auto-fill OTP fields for testing
            if (authProvider.mockOtp != null &&
                authProvider.mockOtp!.length == 6) {
              print("Auto-filling OTP fields with: ${authProvider.mockOtp}");
              for (int i = 0; i < 6; i++) {
                _otpControllers[i].text = authProvider.mockOtp![i];
              }
            } else {
              print(
                  "Not auto-filling OTP fields. mockOtp: ${authProvider.mockOtp}");
            }
          } else {
            print("Success was false, not setting _otpSent to true");
          }
        });

        // Show success message
        if (success) {
          String message = 'OTP sent successfully.';
          if (authProvider.mockOtp != null) {
            message += ' Use ${authProvider.mockOtp} for testing.';
          }

          print("Showing success message: $message");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
            ),
          );
        } else if (authProvider.errorMessage != null) {
          print("Showing error message: ${authProvider.errorMessage}");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authProvider.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      print("Form validation failed");
    }
  }

  void _verifyOtp() async {
    setState(() {
      _isLoading = true;
    });

    final otp = _otpControllers.map((controller) => controller.text).join();
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.verifyOtp(otp);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (!success && authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resendOtp() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isLoading = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.resendOtp();

    if (mounted) {
      setState(() {
        _isLoading = false;

        // Auto-fill OTP fields for testing
        if (success &&
            authProvider.mockOtp != null &&
            authProvider.mockOtp!.length == 6) {
          for (int i = 0; i < 6; i++) {
            _otpControllers[i].text = authProvider.mockOtp![i];
          }
        }
      });

      if (success) {
        _startResendTimer();

        String message = 'OTP resent successfully.';
        if (authProvider.mockOtp != null) {
          message += ' Use ${authProvider.mockOtp} for testing.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      } else if (authProvider.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print("Building phone login screen, _otpSent: $_otpSent");
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: size.height * 0.05),
                  // Logo or App Name
                  Center(
                    child: Text(
                      'DodoBooker',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  SizedBox(height: size.height * 0.05),
                  // Title
                  Text(
                    _otpSent ? 'Verify OTP' : 'Login with Mobile',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 30),
                  if (!_otpSent) ...[
                    // Mobile Number Field
                    NeumorphicTextField(
                      controller: _mobileController,
                      labelText: 'Mobile Number',
                      hintText: 'Enter your mobile number',
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your mobile number';
                        }
                        if (value.length < 10) {
                          return 'Please enter a valid mobile number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 30),
                    // Send OTP Button
                    NeumorphicButton(
                      onPressed: _isLoading ? null : _sendOtp,
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text(
                              'Send OTP',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                    ),
                  ] else ...[
                    // OTP Fields
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Text(
                        'Enter the 6-digit code sent to ${_mobileController.text}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(
                        6,
                        (index) => SizedBox(
                          width: 40,
                          child: TextField(
                            controller: _otpControllers[index],
                            keyboardType: TextInputType.number,
                            textAlign: TextAlign.center,
                            maxLength: 1,
                            decoration: const InputDecoration(
                              counterText: '',
                              enabledBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.grey),
                              ),
                              focusedBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.black),
                              ),
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            onChanged: (value) {
                              if (value.isNotEmpty && index < 5) {
                                FocusScope.of(context).nextFocus();
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 30),
                    // Verify Button
                    NeumorphicButton(
                      onPressed: _isLoading ? null : _verifyOtp,
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text(
                              'Verify',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                    ),
                    const SizedBox(height: 20),
                    // Resend OTP
                    TextButton(
                      onPressed: _resendCountdown > 0 ? null : _resendOtp,
                      child: Text(
                        _resendCountdown > 0
                            ? 'Resend OTP in $_resendCountdown seconds'
                            : 'Resend OTP',
                        style: TextStyle(
                          color: _resendCountdown > 0
                              ? Colors.grey
                              : Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
