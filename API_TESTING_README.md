# DodoBooker Mock API Server

A standalone HTTP server that exposes all the DodoBooker app APIs for testing with Postman or any HTTP client.

## Quick Setup

### 1. Install Node.js
Download and install Node.js from [nodejs.org](https://nodejs.org/)

### 2. Install Dependencies
```bash
npm install
```

### 3. Start the Server
```bash
npm start
```

The server will start on `http://localhost:3000`

## Testing with Postman

### Base URL
```
http://localhost:3000
```

### Authentication Flow
1. **Send OTP**: `POST /api/auth/customer/otp/send/`
2. **Verify OTP**: `POST /api/auth/customer/otp/verify/`
3. Use the returned token for subsequent requests

### Test Credentials
- **Mobile**: `**********`
- **OTP**: `123456`

## API Endpoints

### Authentication
- `POST /api/auth/customer/otp/send/` - Send OTP
- `POST /api/auth/customer/otp/verify/` - Verify OTP
- `POST /api/auth/token/cookie/logout/` - Logout

### Categories & Products
- `GET /api/categories/` - Get all categories
- `GET /api/categories/{id}/subcategories/` - Get subcategories
- `GET /api/categories/{id}/subcategories/{subId}/products/` - Get products

### Cart Management
- `GET /api/cart/` - Get cart
- `POST /api/cart/add/` - Add to cart
- `DELETE /api/cart/clear/` - Clear cart

### Bookings
- `POST /api/bookings/` - Create booking
- `GET /api/bookings/` - Get user bookings
- `GET /api/bookings/{id}/` - Get booking details

### Chat Support
- `GET /api/chat/messages/` - Get chat messages
- `POST /api/chat/messages/` - Send chat message
- `PUT /api/chat/messages/{id}/read/` - Mark message as read
- `GET /api/chat/unread-count/` - Get unread message count
- `DELETE /api/chat/messages/` - Clear chat history

### Support System
- `GET /api/support/agents/` - Get support agents
- `GET /api/support/faq/` - Get FAQ
- `POST /api/support/tickets/` - Create support ticket
- `GET /api/support/tickets/` - Get user support tickets

### Profile & Others
- `GET /api/profile/` - Get user profile
- `GET /api/addresses/` - Get addresses
- `GET /api/coupons/` - Get available coupons

### Health Check
- `GET /health` - Server status

## Sample Requests

### 1. Send OTP
```json
POST /api/auth/customer/otp/send/
{
  "mobile": "**********",
  "user_type": "CUSTOMER"
}
```

### 2. Verify OTP
```json
POST /api/auth/customer/otp/verify/
{
  "mobile": "**********",
  "otp": "123456",
  "user_type": "CUSTOMER"
}
```

### 3. Add to Cart
```json
POST /api/cart/add/
{
  "productId": "product-1",
  "quantity": 1,
  "price": 2999.95
}
```

### 4. Create Booking
```json
POST /api/bookings/
{
  "service": "1 BHK furnished apartment cleaning",
  "category": "Cleaning",
  "price": 2999.95,
  "date": "2025-05-27T10:00:00.000Z",
  "time": "10:00 AM",
  "address": {
    "id": 1,
    "type": "Home",
    "address": "123 Main Street, Apt 4B, New York, NY 10001",
    "isDefault": true
  },
  "paymentMethod": "Cash on Delivery"
}
```

### 5. Send Chat Message
```json
POST /api/chat/messages/
{
  "message": "Hello, I need help with my booking",
  "type": "text"
}
```

### 6. Create Support Ticket
```json
POST /api/support/tickets/
{
  "subject": "Issue with my booking",
  "description": "I'm having trouble with my recent booking. The service provider hasn't arrived yet.",
  "category": "Booking",
  "priority": "high"
}
```

## Response Format

### Success Response
```json
{
  "status": "success",
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "status": "error",
  "message": "Error description",
  "data": null
}
```

## Features

✅ **Complete API Coverage** - All app endpoints available
✅ **Authentication Flow** - OTP-based login simulation
✅ **Data Persistence** - In-memory database for session
✅ **CORS Enabled** - Works with web clients
✅ **Console Logging** - See API calls in real-time
✅ **Error Handling** - Proper HTTP status codes

## Troubleshooting

### Port Already in Use
If port 3000 is busy, modify the PORT in `mock_api_server.js`:
```javascript
const PORT = 3001; // Change to any available port
```

### CORS Issues
The server has CORS enabled for all origins. If you face issues, check your client configuration.

### Authentication Required
Most endpoints require authentication. Always call the OTP verification endpoint first.

## Development

### Watch Mode
```bash
npm run dev
```

This will restart the server automatically when you make changes.

### Adding New Endpoints
Edit `mock_api_server.js` and add your new routes following the existing pattern.

## Testing Workflow

1. **Start the server**: `npm start`
2. **Open Postman**
3. **Import the collection** (if provided)
4. **Authenticate**: Send OTP → Verify OTP
5. **Test endpoints**: Use the authenticated session
6. **Check logs**: Monitor server console for API calls

Happy Testing! 🚀
