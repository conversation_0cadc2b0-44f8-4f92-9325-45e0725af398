import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hierarchical_category.dart';
import '../models/cleaning_product.dart';
import '../models/service_adapter.dart';
import '../providers/cart_provider.dart';
import '../providers/product_provider.dart';

import '../widgets/search_bar_widget.dart';
import '../widgets/app_navigation_bar.dart';
import '../widgets/quantity_counter.dart';
import '../widgets/cart_icon_with_badge.dart';
import 'cleaning_product_detail_screen.dart';

class CleaningProductListScreen extends StatefulWidget {
  final HierarchicalCategory category;

  const CleaningProductListScreen({
    super.key,
    required this.category,
  });

  @override
  State<CleaningProductListScreen> createState() =>
      _CleaningProductListScreenState();
}

class _CleaningProductListScreenState extends State<CleaningProductListScreen> {
  List<CleaningProduct> _products = [];
  List<CleaningProduct> _filteredProducts = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();

  // Track which products have been added to cart and their quantities
  final Map<String, int> _addedProducts = {};

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterProducts(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredProducts = _products;
      });
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    setState(() {
      _filteredProducts = _products.where((product) {
        return product.name.toLowerCase().contains(lowercaseQuery) ||
            product.description.toLowerCase().contains(lowercaseQuery);
      }).toList();
    });
  }

  Future<void> _loadProducts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load products from Supabase based on sub-subcategory ID
      final productProvider =
          Provider.of<ProductProvider>(context, listen: false);
      final products = await productProvider
          .loadProductsBySubSubcategory(widget.category.id);

      if (mounted) {
        setState(() {
          _products = products;
          _filteredProducts = products;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading products: $e');
      if (mounted) {
        setState(() {
          _products = [];
          _filteredProducts = [];
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.category.name,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: const [
          CartIconWithBadge(),
        ],
      ),
      bottomNavigationBar: AppNavigationBar(
        currentSection: NavigationSection.home,
        mainContext: context,
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SearchBarWidget(
              controller: _searchController,
              onChanged: _filterProducts,
              onSubmitted: _filterProducts,
              hintText: 'Search ${widget.category.name} services...',
            ),
          ),

          // Products list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredProducts.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _filteredProducts.length,
                        itemBuilder: (context, index) {
                          return _buildProductCard(_filteredProducts[index]);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final bool isSearching = _searchController.text.isNotEmpty;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isSearching ? Icons.search_off : Icons.cleaning_services_outlined,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            isSearching
                ? 'No results found for "${_searchController.text}"'
                : 'No services available for ${widget.category.name}',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          if (isSearching) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                _filterProducts('');
              },
              child: const Text('Clear Search'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductCard(CleaningProduct product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product header with image
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                ),
                child: Image.network(
                  product.imageUrl,
                  width: 150,
                  height: 150,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 150,
                      height: 150,
                      color: Colors.grey.shade200,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 40,
                      ),
                    );
                  },
                ),
              ),

              // Product title and rating
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.shade50,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.star,
                                  color: Colors.green,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  product.rating.toString(),
                                  style: const TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '(${_formatReviewCount(product.reviews)} reviews)',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Starts at ₹${product.price.toInt()}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Divider
          const Divider(height: 1),

          // Product details
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Only show first include point
                if (product.includes.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            product.includes.first,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Show more points indicator if there are more includes
                if (product.includes.length > 1)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(width: 24),
                        Text(
                          '+ ${product.includes.length - 1} more items',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),

                // Only show first exclude point
                if (product.excludes.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.info,
                          color: Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            product.excludes.first,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // View details and Add button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    // Navigate to product details
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CleaningProductDetailScreen(
                          product: product,
                        ),
                      ),
                    );
                  },
                  child: const Text(
                    'View details',
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _addedProducts.containsKey(product.id)
                    ? QuantityCounter(
                        initialValue: _addedProducts[product.id]!,
                        onChanged: (quantity) {
                          setState(() {
                            if (quantity == 0) {
                              _addedProducts.remove(product.id);
                            } else {
                              _addedProducts[product.id] = quantity;
                            }
                          });
                          _updateCart(product, quantity);
                        },
                        minValue: 0,
                        activeColor: Colors.blue,
                      )
                    : ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _addedProducts[product.id] = 1;
                          });
                          _updateCart(product, 1);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Add'),
                      ),
              ],
            ),
          ),

          // Options
          if (product.options > 0)
            Padding(
              padding: const EdgeInsets.only(left: 12, right: 12, bottom: 12),
              child: Text(
                '${product.options} options',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatReviewCount(int count) {
    if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(count >= 10000 ? 0 : 1)}K';
    }
    return count.toString();
  }

  void _updateCart(CleaningProduct product, int quantity) {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);

    if (quantity <= 0) {
      // Remove from cart
      cartProvider.removeItem(product.id);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product.name} removed from cart'),
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // Check if we need to clear the cart first
      // Allow multiple services from the same category (cleaning)
      if (cartProvider.items.isNotEmpty &&
          !cartProvider.items.any((item) => item.service.id == product.id) &&
          !cartProvider.isFromSameCategory(product.categoryId)) {
        _showClearCartConfirmation(product, quantity);
        return;
      }

      // Convert product to service
      final service = convertToService(product);
      final category = createServiceCategory(product);

      // Add or update cart
      cartProvider.addItem(
        service: service,
        category: category,
        quantity: quantity,
        date: DateTime.now(),
        time: '10:00 AM', // Default time
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product.name} added to cart'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showClearCartConfirmation(CleaningProduct product, int quantity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Replace Cart Items?'),
        content: const Text(
            'You can only book services from the same category at a time. Your cart contains services from a different category. Do you want to replace the items in your cart?'),
        actions: [
          TextButton(
            onPressed: () {
              // Cancel - revert the UI
              setState(() {
                _addedProducts.remove(product.id);
              });
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Clear cart and add new item
              final cartProvider =
                  Provider.of<CartProvider>(context, listen: false);
              cartProvider.clear();

              // Convert product to service
              final service = convertToService(product);
              final category = createServiceCategory(product);

              // Add to cart
              cartProvider.addItem(
                service: service,
                category: category,
                quantity: quantity,
                date: DateTime.now(),
                time: '10:00 AM', // Default time
              );

              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${product.name} added to cart'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            child: const Text('Replace'),
          ),
        ],
      ),
    );
  }
}
