# DodoBooker API Documentation

## Base URL
```
http://localhost:3000
```

## Setup Instructions

1. Install Node.js (if not already installed)
2. Navigate to the `api_server` directory
3. Run: `npm install`
4. Run: `npm start`
5. Server will start on `http://localhost:3000`

## API Endpoints

### 1. Health Check
**GET** `/health`

**Response:**
```json
{
  "status": "success",
  "message": "Success",
  "data": {
    "status": "Server is running"
  },
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

### 2. Authentication

#### Send OTP
**POST** `/auth/send-otp`

**Request Body:**
```json
{
  "mobile": "**********"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Success",
  "data": {
    "message": "OTP sent successfully",
    "mobile": "**********",
    "otp": "123456"
  },
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

#### Verify OTP
**POST** `/auth/verify-otp`

**Request Body:**
```json
{
  "mobile": "**********",
  "otp": "123456"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Success",
  "data": {
    "user": {
      "id": "user_**********",
      "mobile": "**********",
      "name": "User",
      "email": "<EMAIL>",
      "profilePic": "https://randomuser.me/api/portraits/men/1.jpg",
      "createdAt": "2023-07-15T10:30:00.000Z",
      "lastLogin": "2023-07-15T10:30:00.000Z"
    },
    "token": "token_user_**********_1689412200000"
  },
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

### 3. Categories

#### Get All Categories
**GET** `/categories`

**Response:**
```json
{
  "status": "success",
  "message": "Success",
  "data": [
    {
      "id": "1",
      "name": "Cleaning",
      "icon": 61648,
      "color": 4280391411,
      "imageUrl": "https://images.unsplash.com/photo-1581578731548-c64695cc6952",
      "description": "Professional cleaning services for your home"
    },
    {
      "id": "2",
      "name": "Plumbing",
      "icon": 58704,
      "color": 4285887861,
      "imageUrl": "https://images.unsplash.com/photo-1607472586893-edb57bdc0e39",
      "description": "Expert plumbing services for all your needs"
    }
  ],
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

#### Get Category by ID
**GET** `/categories/{categoryId}`

**Example:** `/categories/1`

### 4. Subcategories

#### Get Subcategories
**GET** `/subcategories`

**Query Parameters:**
- `categoryId` (optional): Filter by parent category

**Example:** `/subcategories?categoryId=1`

### 5. Products

#### Get Products
**GET** `/products`

**Query Parameters:**
- `categoryId` (optional): Filter by category
- `subcategoryId` (optional): Filter by subcategory

**Example:** `/products?categoryId=1-1`

#### Get Product by ID
**GET** `/products/{productId}`

**Example:** `/products/1-1-1`

### 6. Cart Management

#### Get Cart
**GET** `/cart/{userId}`

**Example:** `/cart/user_**********`

#### Add Item to Cart
**POST** `/cart/{userId}/add`

**Request Body:**
```json
{
  "productId": "1-1-1",
  "quantity": 1
}
```

#### Clear Cart
**DELETE** `/cart/{userId}`

#### Remove Item from Cart
**DELETE** `/cart/{userId}/item/{productId}`

### 7. Profile Management

#### Get Profile
**GET** `/profile/{userId}`

**Example:** `/profile/user_**********`

#### Update Profile
**PUT** `/profile/{userId}`

**Request Body:**
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>"
}
```

### 8. Testing Endpoints

#### Get All Users
**GET** `/users`

## Postman Collection

### Environment Variables
Create a Postman environment with:
- `base_url`: `http://localhost:3000`
- `user_id`: `user_**********` (or any user ID from auth)

### Sample Test Flow

1. **Health Check**
   - GET `{{base_url}}/health`

2. **Authentication Flow**
   - POST `{{base_url}}/auth/send-otp`
   - POST `{{base_url}}/auth/verify-otp`

3. **Browse Categories**
   - GET `{{base_url}}/categories`
   - GET `{{base_url}}/subcategories?categoryId=1`
   - GET `{{base_url}}/products?categoryId=1-1`

4. **Cart Operations**
   - GET `{{base_url}}/cart/{{user_id}}`
   - POST `{{base_url}}/cart/{{user_id}}/add`
   - DELETE `{{base_url}}/cart/{{user_id}}/item/1-1-1`

5. **Profile Management**
   - GET `{{base_url}}/profile/{{user_id}}`
   - PUT `{{base_url}}/profile/{{user_id}}`

## Error Responses

All error responses follow this format:
```json
{
  "status": "error",
  "message": "Error description",
  "code": 400,
  "timestamp": "2023-07-15T10:30:00.000Z"
}
```

## Notes

- All endpoints return JSON responses
- The OTP for testing is always `123456`
- User IDs are generated as `user_{mobile_number}`
- Cart calculations include 5% tax
- All timestamps are in ISO 8601 format
