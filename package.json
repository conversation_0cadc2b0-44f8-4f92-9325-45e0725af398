{"name": "dodobooker-mock-api", "version": "1.0.0", "description": "Mock API server for DodoBooker Flutter app testing", "main": "mock_api_server.js", "scripts": {"start": "node mock_api_server.js", "dev": "nodemon mock_api_server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["mock", "api", "flutter", "testing", "postman"], "author": "DodoBooker Team", "license": "MIT"}