import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/coupon_model.dart';
import '../providers/coupon_provider.dart';

class CouponModal extends StatefulWidget {
  const CouponModal({Key? key}) : super(key: key);

  @override
  State<CouponModal> createState() => _CouponModalState();
}

class _CouponModalState extends State<CouponModal> {
  final TextEditingController _couponController = TextEditingController();
  String _errorMessage = '';

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final couponProvider = Provider.of<CouponProvider>(context);
    final availableCoupons = couponProvider.availableCoupons;
    final appliedCoupon = couponProvider.appliedCoupon;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Coupons',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _couponController,
              decoration: InputDecoration(
                hintText: 'Enter coupon code',
                border: InputBorder.none,
                suffixIcon: TextButton(
                  onPressed: () {
                    final success = couponProvider.applyCoupon(_couponController.text.trim());
                    if (success) {
                      Navigator.pop(context);
                    } else {
                      setState(() {
                        _errorMessage = 'Invalid coupon or minimum order value not met';
                      });
                    }
                  },
                  child: const Text('APPLY'),
                ),
              ),
            ),
          ),
          if (_errorMessage.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                _errorMessage,
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
          const SizedBox(height: 16),
          const Text(
            'Available Coupons',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableCoupons.length,
              itemBuilder: (context, index) {
                final coupon = availableCoupons[index];
                final isApplied = appliedCoupon?.code == coupon.code;
                final isValid = couponProvider.isCouponValid(coupon);
                
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                coupon.code,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (isApplied)
                              const Text(
                                'APPLIED',
                                style: TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            else if (isValid)
                              TextButton(
                                onPressed: () {
                                  couponProvider.applyCoupon(coupon.code);
                                  Navigator.pop(context);
                                },
                                child: const Text('APPLY'),
                              )
                            else
                              const Text(
                                'NOT APPLICABLE',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          coupon.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(coupon.description),
                        const SizedBox(height: 8),
                        Text(
                          'Min order: ₹${coupon.minOrderValue.toStringAsFixed(0)} | Max discount: ₹${coupon.maxDiscount.toStringAsFixed(0)}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          'Valid till: ${coupon.validUntil.day}/${coupon.validUntil.month}/${coupon.validUntil.year}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
