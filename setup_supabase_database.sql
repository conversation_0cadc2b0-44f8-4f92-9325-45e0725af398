-- Do<PERSON><PERSON>ooker Supabase Database Setup
-- Run this SQL in your Supabase SQL Editor to set up the database

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    icon_name VARCHAR(100),
    icon_code_point INTEGER,
    color_hex VARCHAR(7),
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subcategories table
CREATE TABLE IF NOT EXISTS subcategories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    icon_name VARCHAR(100),
    icon_code_point INTEGER,
    color_hex VARCHAR(7),
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    subcategory_id UUID REFERENCES subcategories(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    duration_minutes INTEGER DEFAULT 60,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    is_popular BOOLEAN DEFAULT false,
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    profile_pic TEXT,
    is_profile_complete BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User addresses table
CREATE TABLE IF NOT EXISTS user_addresses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    address_line_1 VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) DEFAULT 'India',
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart items table
CREATE TABLE IF NOT EXISTS cart_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    booking_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    scheduled_date DATE,
    scheduled_time TIME,
    address_id UUID REFERENCES user_addresses(id),
    subtotal DECIMAL(10,2) NOT NULL,
    tax DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status VARCHAR(50) DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Booking items table
CREATE TABLE IF NOT EXISTS booking_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupons table
CREATE TABLE IF NOT EXISTS coupons (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    discount_type VARCHAR(20) NOT NULL, -- 'percentage' or 'fixed'
    discount_value DECIMAL(10,2) NOT NULL,
    max_discount DECIMAL(10,2),
    min_order_value DECIMAL(10,2) DEFAULT 0,
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_until TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_items ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- User profiles: Users can only access their own profile
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- User addresses: Users can only access their own addresses
CREATE POLICY "Users can view own addresses" ON user_addresses FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own addresses" ON user_addresses FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own addresses" ON user_addresses FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own addresses" ON user_addresses FOR DELETE USING (auth.uid() = user_id);

-- Cart items: Users can only access their own cart
CREATE POLICY "Users can view own cart" ON cart_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own cart items" ON cart_items FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own cart items" ON cart_items FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own cart items" ON cart_items FOR DELETE USING (auth.uid() = user_id);

-- Bookings: Users can only access their own bookings
CREATE POLICY "Users can view own bookings" ON bookings FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own bookings" ON bookings FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own bookings" ON bookings FOR UPDATE USING (auth.uid() = user_id);

-- Booking items: Users can only access their own booking items
CREATE POLICY "Users can view own booking items" ON booking_items FOR SELECT USING (
    auth.uid() = (SELECT user_id FROM bookings WHERE id = booking_id)
);
CREATE POLICY "Users can insert own booking items" ON booking_items FOR INSERT WITH CHECK (
    auth.uid() = (SELECT user_id FROM bookings WHERE id = booking_id)
);

-- Public read access for categories, subcategories, products, and coupons
CREATE POLICY "Anyone can view categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Anyone can view subcategories" ON subcategories FOR SELECT USING (true);
CREATE POLICY "Anyone can view products" ON products FOR SELECT USING (true);
CREATE POLICY "Anyone can view active coupons" ON coupons FOR SELECT USING (is_active = true);

-- Insert sample data
INSERT INTO categories (name, description, image_url, icon_code_point, color_hex, display_order) VALUES
('Cleaning', 'Professional home cleaning services', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952', 61584, '#4CAF50', 1),
('Plumbing', 'Expert plumbing repair and installation', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39', 58704, '#2196F3', 2),
('Electrical', 'Safe and reliable electrical services', 'https://images.unsplash.com/photo-1621905251918-48416bd8575a', 57744, '#FF9800', 3),
('Carpenter', 'Professional carpentry and woodwork', 'https://images.unsplash.com/photo-1504148455328-c376907d081c', 59576, '#795548', 4),
('Appliances', 'Appliance repair and maintenance', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136', 58732, '#9C27B0', 5),
('Painting', 'Interior and exterior painting services', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828', 59598, '#E91E63', 6),
('AC Services', 'Air conditioning installation and repair', 'https://images.unsplash.com/photo-1581092160562-40aa08e78837', 57403, '#00BCD4', 7),
('Pest Control', 'Safe and effective pest management', 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13', 59595, '#4CAF50', 8);

-- Insert sample subcategories
INSERT INTO subcategories (category_id, name, description, image_url, icon_code_point, color_hex, display_order) VALUES
((SELECT id FROM categories WHERE name = 'Cleaning'), 'Home Cleaning', 'Regular home cleaning service', 'https://images.unsplash.com/photo-1581578731548-c64695cc6952', 61584, '#4CAF50', 1),
((SELECT id FROM categories WHERE name = 'Cleaning'), 'Deep Cleaning', 'Thorough deep cleaning service', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64', 61648, '#4CAF50', 2),
((SELECT id FROM categories WHERE name = 'Plumbing'), 'Pipe Repair', 'Fix leaky and broken pipes', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39', 58704, '#2196F3', 1),
((SELECT id FROM categories WHERE name = 'Plumbing'), 'Tap Installation', 'Install new taps and faucets', 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13', 58704, '#2196F3', 2),
((SELECT id FROM categories WHERE name = 'Electrical'), 'Switch Installation', 'Install electrical switches', 'https://images.unsplash.com/photo-1621905251918-48416bd8575a', 57744, '#FF9800', 1),
((SELECT id FROM categories WHERE name = 'Electrical'), 'Wiring Repair', 'Fix electrical wiring issues', 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13', 57744, '#FF9800', 2);

-- Insert sample products
INSERT INTO products (category_id, subcategory_id, name, description, price, image_url, duration_minutes, rating, review_count, is_popular, is_featured) VALUES
((SELECT id FROM categories WHERE name = 'Cleaning'), (SELECT id FROM subcategories WHERE name = 'Home Cleaning'), 'Basic Home Cleaning', 'Complete cleaning of your home including dusting, mopping, and bathroom cleaning.', 499.00, 'https://images.unsplash.com/photo-1581578731548-c64695cc6952', 120, 4.5, 120, true, true),
((SELECT id FROM categories WHERE name = 'Cleaning'), (SELECT id FROM subcategories WHERE name = 'Home Cleaning'), 'Premium Home Cleaning', 'Comprehensive cleaning service with additional services like window cleaning and appliance cleaning.', 799.00, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64', 180, 4.8, 89, false, true),
((SELECT id FROM categories WHERE name = 'Cleaning'), (SELECT id FROM subcategories WHERE name = 'Deep Cleaning'), 'Deep House Cleaning', 'Thorough deep cleaning including hard-to-reach areas, inside appliances, and detailed sanitization.', 1299.00, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64', 240, 4.7, 156, true, false),
((SELECT id FROM categories WHERE name = 'Plumbing'), (SELECT id FROM subcategories WHERE name = 'Pipe Repair'), 'Pipe Leak Repair', 'Fix leaky pipes and prevent water damage.', 299.00, 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39', 60, 4.3, 78, false, false),
((SELECT id FROM categories WHERE name = 'Plumbing'), (SELECT id FROM subcategories WHERE name = 'Tap Installation'), 'Kitchen Tap Installation', 'Install new kitchen taps with modern fixtures.', 399.00, 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13', 90, 4.6, 92, true, false),
((SELECT id FROM categories WHERE name = 'Electrical'), (SELECT id FROM subcategories WHERE name = 'Switch Installation'), 'Light Switch Installation', 'Install new light switches and dimmers.', 199.00, 'https://images.unsplash.com/photo-1621905251918-48416bd8575a', 45, 4.4, 67, false, false),
((SELECT id FROM categories WHERE name = 'Electrical'), (SELECT id FROM subcategories WHERE name = 'Wiring Repair'), 'Electrical Wiring Repair', 'Fix faulty electrical wiring safely.', 599.00, 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13', 120, 4.5, 134, true, true);

-- Insert sample coupons
INSERT INTO coupons (code, name, description, discount_type, discount_value, max_discount, min_order_value, usage_limit, valid_from, valid_until) VALUES
('WELCOME10', 'Welcome Discount', 'Get 10% off on your first booking', 'percentage', 10.00, 100.00, 200.00, 1000, NOW(), NOW() + INTERVAL '30 days'),
('SAVE50', 'Flat ₹50 Off', 'Save ₹50 on orders above ₹300', 'fixed', 50.00, NULL, 300.00, 500, NOW(), NOW() + INTERVAL '15 days'),
('CLEAN20', 'Cleaning Special', '20% off on all cleaning services', 'percentage', 20.00, 200.00, 400.00, 200, NOW(), NOW() + INTERVAL '7 days');
