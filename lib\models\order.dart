import 'package:flutter/material.dart';
import 'cart_item.dart';

enum OrderStatus {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
}

class Order {
  final String id;
  final List<CartItem> items;
  final Map<String, dynamic> address;
  final DateTime date;
  final String time;
  final double subtotal;
  final double tax;
  final double discount;
  final double smallOrderFee;
  final double total;
  final String paymentMethod;
  final OrderStatus status;
  final DateTime createdAt;
  final String? technician;
  final String? technicianPhone;
  final String? notes;
  final String? cancellationReason;

  Order({
    required this.id,
    required this.items,
    required this.address,
    required this.date,
    required this.time,
    required this.subtotal,
    required this.tax,
    required this.discount,
    required this.smallOrderFee,
    required this.total,
    required this.paymentMethod,
    required this.status,
    required this.createdAt,
    this.technician,
    this.technicianPhone,
    this.notes,
    this.cancellationReason,
  });

  // Get the main service category color
  Color get categoryColor {
    if (items.isEmpty) return Colors.grey;
    return items.first.category.color;
  }

  // Get the main service category icon
  IconData get categoryIcon {
    if (items.isEmpty) return Icons.home_repair_service;
    return items.first.category.icon;
  }

  // Get the main service name
  String get serviceName {
    if (items.isEmpty) return "Unknown Service";
    return items.first.service.name;
  }

  // Get the main service category name
  String get categoryName {
    if (items.isEmpty) return "Unknown Category";
    return items.first.category.name;
  }

  // Get payment status
  String get paymentStatus {
    if (paymentMethod == 'Cash on Delivery') {
      return status == OrderStatus.completed ? 'Paid' : 'Pending';
    }
    return 'Paid';
  }

  // Get status color
  Color get statusColor {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFFFFA000); // Amber
      case OrderStatus.confirmed:
        return const Color(0xFF2196F3); // Blue
      case OrderStatus.inProgress:
        return const Color(0xFF9C27B0); // Purple
      case OrderStatus.completed:
        return const Color(0xFF4CAF50); // Green
      case OrderStatus.cancelled:
        return const Color(0xFFF44336); // Red
    }
  }

  // Get status text
  String get statusText {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  // Factory method to create an order from cart items
  factory Order.fromCart({
    required String id,
    required List<CartItem> items,
    required Map<String, dynamic> address,
    required DateTime date,
    required String time,
    required double subtotal,
    required double tax,
    required double discount,
    required double smallOrderFee,
    required double total,
    required String paymentMethod,
  }) {
    return Order(
      id: id,
      items: items,
      address: address,
      date: date,
      time: time,
      subtotal: subtotal,
      tax: tax,
      discount: discount,
      smallOrderFee: smallOrderFee,
      total: total,
      paymentMethod: paymentMethod,
      status: OrderStatus.confirmed,
      createdAt: DateTime.now(),
    );
  }
}
